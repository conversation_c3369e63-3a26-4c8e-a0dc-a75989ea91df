<?xml version="1.0" encoding="UTF-8"?>

<project xmlns="http://maven.apache.org/POM/4.0.0" xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance"
         xsi:schemaLocation="http://maven.apache.org/POM/4.0.0 http://maven.apache.org/xsd/maven-4.0.0.xsd">
    <parent>
        <artifactId>id-yc-province-parent</artifactId>
        <groupId>cn.teleinfo</groupId>
        <version>0.4.0.RELEASE</version>
        <relativePath>../id-yc-province-parent/pom.xml</relativePath>
    </parent>
    <modelVersion>4.0.0</modelVersion>

    <artifactId>id-yc-mq-transfer</artifactId>
    <name>id-yc-mq-transfer</name>
    <version>0.4.0.RELEASE</version>
    
    <properties>
        <maven.compiler.source>8</maven.compiler.source>
        <maven.compiler.target>8</maven.compiler.target>
        <project.build.sourceEncoding>UTF-8</project.build.sourceEncoding>
    </properties>

    <dependencies>
        <dependency>
            <groupId>cn.teleinfo</groupId>
            <artifactId>id-yc-province-infrastructure</artifactId>
            <exclusions>
                <exclusion>
                    <artifactId>log4j-slf4j-impl</artifactId>
                    <groupId>org.apache.logging.log4j</groupId>
                </exclusion>
            </exclusions>
        </dependency>
        <dependency>
            <groupId>org.springframework.session</groupId>
            <artifactId>spring-session-core</artifactId>
        </dependency>

        <dependency>
            <groupId>org.springframework.boot</groupId>
            <artifactId>spring-boot-starter-web</artifactId>
            <exclusions>
                <exclusion>
                    <artifactId>log4j-slf4j-impl</artifactId>
                    <groupId>org.apache.logging.log4j</groupId>
                </exclusion>
            </exclusions>
        </dependency>
        <dependency>
            <groupId>org.springframework.boot</groupId>
            <artifactId>spring-boot-starter-data-jpa</artifactId>
        </dependency>

        <dependency>
            <groupId>cn.hutool</groupId>
            <artifactId>hutool-all</artifactId>
        </dependency>

        <dependency><!-- 该依赖必加，里面有sping对schedule的支持 -->
            <groupId>org.springframework</groupId>
            <artifactId>spring-context-support</artifactId>
        </dependency>
        <dependency>
            <groupId>org.springframework.kafka</groupId>
            <artifactId>spring-kafka</artifactId>
        </dependency>
        <dependency>
            <groupId>org.apache.kafka</groupId>
            <artifactId>kafka-clients</artifactId>
            <version>3.4.0</version>
        </dependency>

        <dependency>
            <groupId>org.springframework.boot</groupId>
            <artifactId>spring-boot-starter-test</artifactId>
            <scope>test</scope>
        </dependency>

        <dependency>
            <groupId>org.junit.jupiter</groupId>
            <artifactId>junit-jupiter</artifactId>
            <scope>test</scope>
            <exclusions>
                <exclusion>
                    <artifactId>opentest4j</artifactId>
                    <groupId>org.opentest4j</groupId>
                </exclusion>
            </exclusions>
            <version>5.8.2</version>
        </dependency>
        <dependency>
            <groupId>com.alibaba</groupId>
            <artifactId>fastjson</artifactId>
            <version>2.0.15</version>
            <scope>compile</scope>
        </dependency>
        <!--Lombok-->
        <dependency>
            <groupId>org.projectlombok</groupId>
            <artifactId>lombok</artifactId>
            <version>1.18.26</version>
            <scope>provided</scope>
        </dependency>

    </dependencies>
    
    <build>
        <plugins>
            <plugin>
                <groupId>com.google.cloud.tools</groupId>
                <artifactId>jib-maven-plugin</artifactId>
                <configuration>
                    <from>
                        <image>dockerhub.qingcloud.com/idpointer/java:8</image>
                    </from>
                    <to>
                        <image>dockerhub.qingcloud.com/idpointer/id-yc-mq-transfer</image>
                    </to>
                    <!--容器相关的属性-->
                    <container>
                        <!--jvm内存参数-->
                        <environment>
                            <JAVA_OPTS>-Xms1g -Xmx2g -Xss256k</JAVA_OPTS>
                        </environment>
                        <!-- springboot项目的入口类 -->
                        <entrypoint>
                            <arg>/bin/bash</arg>
                            <arg>-c</arg>
                            <!--suppress UnresolvedMavenProperty -->
                            <arg>java ${JAVA_OPTS} -Duser.timezone=PRC -cp /app/resources/:/app/classes/:/app/libs/* cn.teleinfo.mq.transfer.IdMqTransferServer
                            </arg>
                        </entrypoint>
                        <ports>
                            <port>4900</port>
                        </ports>
                        <creationTime>USE_CURRENT_TIMESTAMP</creationTime>
                    </container>
                    <allowInsecureRegistries>true</allowInsecureRegistries>
                </configuration>
            </plugin>
        </plugins>
    </build>


</project>
