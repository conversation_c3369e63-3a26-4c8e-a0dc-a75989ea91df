package cn.teleinfo.mq.transfer.config;

import cn.teleinfo.mq.transfer.exception.AppHandlerExceptionResolver;
import com.abluepoint.summer.common.util.AppContextUtil;
import org.springframework.context.annotation.Bean;
import org.springframework.context.annotation.Configuration;
import org.springframework.web.servlet.HandlerExceptionResolver;

@Configuration
public class AppConfig {

    @Bean
    public AppContextUtil.ContextHolder applicationContextHolder() {
        return new AppContextUtil.ContextHolder();
    }

    @Bean
    public HandlerExceptionResolver appHandlerExceptionResolver() {
        return new AppHandlerExceptionResolver();
    }

}
