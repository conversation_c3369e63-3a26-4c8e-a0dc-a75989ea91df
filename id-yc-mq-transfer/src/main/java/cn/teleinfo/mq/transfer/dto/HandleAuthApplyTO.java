package cn.teleinfo.mq.transfer.dto;

import lombok.Data;

import java.util.ArrayList;
import java.util.List;

@Data
public class HandleAuthApplyTO {
    private String handle;
    private String name;
    private String applyHandleName;
    private String applyHandleUser;
    private Integer applyHandleIndex;
    private Integer applyHandleType;
    private Integer entityType;
    private Integer state;
    private List<HandleAuthItemApplyDTO> item = new ArrayList<>();
}
