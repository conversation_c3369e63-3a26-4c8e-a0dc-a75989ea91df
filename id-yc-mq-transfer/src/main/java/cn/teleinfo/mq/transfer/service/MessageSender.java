package cn.teleinfo.mq.transfer.service;

import cn.hutool.core.util.StrUtil;
import cn.hutool.json.JSONObject;
import cn.hutool.json.JSONUtil;
import cn.teleinfo.idycprovince.common.exception.BusinessCodeMessage;
import cn.teleinfo.mq.transfer.config.AppRouterProperties;
import cn.teleinfo.mq.transfer.dto.MQSignMessageDTO;
import cn.teleinfo.mq.transfer.dto.MqHttpResult;
import cn.teleinfo.mq.transfer.dto.ResponseMessageBody;
import cn.teleinfo.mq.transfer.dto.Router;
import cn.teleinfo.mq.transfer.utils.HttpClientUtil;
import com.abluepoint.summer.mvc.domain.R;
import com.abluepoint.summer.mvc.exception.BusinessRuntimeException;
import com.alibaba.fastjson.JSON;
import lombok.SneakyThrows;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.beans.BeanUtils;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.scheduling.annotation.Async;
import org.springframework.stereotype.Service;

import javax.annotation.Resource;
import java.util.*;
import java.util.Arrays;
import java.util.List;


/***
 * @title kafkaService
 * @description 消息中转
 * <AUTHOR>
 * @version 1.0.0
 * @create 2023/3/15 19:15
 **/
@Slf4j
@Service
public class MessageSender {

    @Resource
    private MqSender mqSender;

    private final String MQ_ACCEPT_URL = "/api/mq/accept";

    @Resource
    private AppRouterProperties appRouterProperties;

    /**
     * 消息发送 （mq/http）
     *
     * @param message
     */
    @Async
    @SneakyThrows
    public void send(String message) {
        ResponseMessageBody messageBody = parse(message);
        String targetPrefix = messageBody.getTargetPrefix();
        if (StrUtil.isBlank(targetPrefix)) {
            return;
        }
        //将行业地址拆分为集合
        Map<String,String> map = appRouterProperties.getRouter();
        // k8s变量名不支持双引号, 无法使用app.router."99.2000"，需要在k8s里配置成app.router."992000", 然后读取的时候转成99.2000
        HashMap<String,String> router = new HashMap<>();
        for (Map.Entry<String, String> entry : map.entrySet()) {
            String key = entry.getKey();
            String prefix = key.substring(0, 2) + "." + key.substring(2);
            String value = entry.getValue();
            router.put(prefix, value);
        }
        Set<String> set = router.keySet();
        //接收到请求后，根据前缀判断 发mq 还是发http
        //找到就发http消息
        for (String s : set) {
            if(targetPrefix.startsWith(s)){
                log.info("前缀"+targetPrefix+"属于行业测, 调用HTTP接口发送消息");
                String target = router.get(s);
                List<String> targets = Arrays.asList(target.split(","));
                for (String tg : targets) {
                    try {
                        MqHttpResult result = HttpClientUtil.doPost(tg + MQ_ACCEPT_URL, message);
                        int code = result.getCode();
                        int successCode = R.ok().getCode();
                        boolean isSuccess = code == successCode;
                        if (!isSuccess) {
                            log.warn("中转服务请求发送失败POST: ;地址：{};请求体：{};结果：{}", tg + MQ_ACCEPT_URL, message, result);
                        } else {
                            log.info("中转服务请求发送成功POST: ;地址：{};请求体：{};结果：{}", tg + MQ_ACCEPT_URL, message, result);
                        }
                    } catch (Exception e) {
                        log.warn("中转服务请求发送失败POST: ;地址：{};请求体：{};结果：{}", tg + MQ_ACCEPT_URL, message, e.getMessage());
                        log.error(e.getMessage(), e);
                        throw e;
                    }
                }
            }
        }
        //找不到就发MQ
        mqSender.send(message);
    }


    private ResponseMessageBody parse(String message) {
        cn.hutool.json.JSONObject jsonObject = JSONUtil.parseObj(message);
        MQSignMessageDTO responseMessageDTO;
        ResponseMessageBody messageBody;
        try {
            responseMessageDTO = JSONUtil.toBean(jsonObject.getStr("dataJson"), MQSignMessageDTO.class);
            JSONObject body = (JSONObject) responseMessageDTO.getBody();
            messageBody = JSONUtil.toBean(body,ResponseMessageBody.class);
        } catch (Exception e) {
            log.error(e.getMessage(), e);
            throw new BusinessRuntimeException(BusinessCodeMessage.MQ_MESSAGE_PARSE_FAILED);
        }
        return messageBody;
    }

}
