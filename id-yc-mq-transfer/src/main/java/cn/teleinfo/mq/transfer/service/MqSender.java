package cn.teleinfo.mq.transfer.service;

import cn.hutool.json.JSON;
import cn.hutool.json.JSONObject;
import cn.hutool.json.JSONUtil;
import cn.teleinfo.idycprovince.common.exception.BusinessCodeMessage;
import com.abluepoint.summer.mvc.exception.BusinessRuntimeException;
import lombok.extern.slf4j.Slf4j;
import org.apache.kafka.clients.producer.RecordMetadata;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.kafka.annotation.KafkaListener;
import org.springframework.kafka.core.KafkaTemplate;
import org.springframework.kafka.support.SendResult;
import org.springframework.stereotype.Service;
import org.springframework.util.concurrent.ListenableFuture;

import javax.annotation.Resource;

import java.util.HashMap;
import java.util.Map;

import static java.lang.Thread.sleep;

/***
 * @title kafkaService
 * @description 消息中转
 * <AUTHOR>
 * @version 1.0.0
 * @create 2023/3/15 19:15
 **/
@Slf4j
@Service
public class MqSender {

    @Resource
    private KafkaTemplate kafkaTemplate;

    /**
     * 第二次发送消息topic
     */
    @Value("${transfer.topic.send}")
    private String[] sendTopicList;


    /**
     * 申请id
     */
    @Value("${transfer.apply_id}")
    private String applyId;

    /**
     * 消息监听，获取配置的topic
     * @param msg
     */
    public void send(String msg) {
        JSONObject jsonObject = JSONUtil.parseObj(msg);
        jsonObject.set("applyId", applyId);
        jsonObject.remove("operateId");
        // 消息处理
        for (String topic: sendTopicList){
            String message = JSONUtil.toJsonStr(jsonObject);
            kafkaTemplate.send(topic, message);
            log.info("消息下发成功, 内容: {}", message);
        }
    }

}
