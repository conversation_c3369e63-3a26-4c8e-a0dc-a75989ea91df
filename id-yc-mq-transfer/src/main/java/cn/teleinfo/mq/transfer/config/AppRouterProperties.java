package cn.teleinfo.mq.transfer.config;

import lombok.Data;
import org.springframework.boot.context.properties.ConfigurationProperties;
import org.springframework.context.annotation.Configuration;
import org.springframework.stereotype.Component;

import java.util.Map;

@Data
@Configuration
@ConfigurationProperties(prefix = "app")
@Component
public class AppRouterProperties {

    private Map<String,String> router;
}
