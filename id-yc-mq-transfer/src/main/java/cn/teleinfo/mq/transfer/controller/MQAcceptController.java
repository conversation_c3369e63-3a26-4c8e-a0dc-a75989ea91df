package cn.teleinfo.mq.transfer.controller;

import cn.teleinfo.mq.transfer.service.MessageSender;
import com.abluepoint.summer.mvc.domain.R;
import com.abluepoint.summer.mvc.domain.Result;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.*;


/**
 * <AUTHOR>
 * @version 1.0
 * @date Created in 2022/11/8
 * @description 标识管理
 */
@Slf4j
@RestController
@RequestMapping("/api/mq")
@RequiredArgsConstructor
public class MQAcceptController {

    @Autowired
    private MessageSender messageSender;

    /**
     * 接收MQ消息
     * @return
     */
    @PostMapping("accept")
    public Result accept(@RequestBody String message) {

        log.info("接收到请求消息::{}", message);

        messageSender.send(message);

        return R.ok();
    }


}
