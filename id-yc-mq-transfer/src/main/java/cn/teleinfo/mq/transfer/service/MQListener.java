package cn.teleinfo.mq.transfer.service;

import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.kafka.annotation.KafkaListener;
import org.springframework.stereotype.Service;

import javax.annotation.Resource;
import java.util.List;

/***
 * @title kafkaService
 * @description 消息中转
 * <AUTHOR>
 * @version 1.0.0
 * @create 2023/3/15 19:15
 **/
@Slf4j
@Service
public class MQListener {

    @Autowired
    private MessageSender messageSender;

    /**
     * 消息监听，获取配置的topic
     *
     * @param messages
     */
    @KafkaListener(topics = "${transfer.topic.accept}", containerFactory = "boot_batchFactory")
    public void receiveMsg(List<String> messages) {
        log.info("======================监听到消息======================");
        for (String message : messages) {
            log.info("开始处理消息: 内容: " + message);
            try {
                messageSender.send(message);
                log.info("消息处理成功");
            }catch (Exception e) {
                log.info("消息处理失败");
                throw new RuntimeException(e);
            }
        }
        log.info("======================消息处理完毕======================");
    }

}
