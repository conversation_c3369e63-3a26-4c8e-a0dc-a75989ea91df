package cn.teleinfo.mq.transfer.exception;


import cn.hutool.core.util.StrUtil;
import com.abluepoint.summer.common.exception.CodeMessageEnum;
import com.abluepoint.summer.mvc.domain.Results;
import com.abluepoint.summer.mvc.exception.BusinessRuntimeException;
import com.abluepoint.summer.mvc.exception.SummerHandlerExceptionResolver;
import lombok.AllArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.web.servlet.ModelAndView;

import javax.servlet.http.HttpServletRequest;
import javax.servlet.http.HttpServletResponse;
import java.nio.charset.Charset;


/*
 * File: AppHandlerExceptionResolver.java
 * <AUTHOR>
 * @since 2022-09-14
 *
 * Copyright (c) 2022 abluepoint teleinfo All Rights Reserved.
 */

@AllArgsConstructor
@Slf4j
public class AppHandlerExceptionResolver extends SummerHandlerExceptionResolver {

    @Override
    public ModelAndView resolveException(HttpServletRequest request, HttpServletResponse response, Object handler, Exception ex) {
        if (ex instanceof BusinessRuntimeException) {
            BusinessRuntimeException exception = (BusinessRuntimeException) ex;
            log.error(exception.getMessage(), ex);
            return Results.failView(exception.getCode(), StrUtil.str(exception.getMessage(), Charset.defaultCharset()));
        }
        log.error(ex.getMessage(), ex);
        return Results.failView(CodeMessageEnum.SYSTEM_UNDEFINED_ERROR.getCode(), CodeMessageEnum.SYSTEM_UNDEFINED_ERROR.getMessageKey());
    }
}