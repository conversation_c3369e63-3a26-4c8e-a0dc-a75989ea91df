package cn.teleinfo.mq.transfer.dto;

import lombok.Data;

import java.util.ArrayList;
import java.util.List;
import java.util.Map;

/***
 * @title ResponseMessageVO
 * @description <TODO description class purpose>
 * <AUTHOR>
 * @version 1.0.0
 * @create 2023/3/15 13:52
 **/
@Data
public class RequestMessageBody {

    /**
     * 业务id
     */
    private String businessId;

    /**
     * 业务编码
     */
    private String businessCode;
    /**
     * 接收方前缀
     */
    private String targetPrefix;
    /**
     * 标识维护方app编码（标识）
     */
    private String appCode;
    /**
     * 维护的标识
     */
    private String handle;

    /**
     * 增加属性信息
     */
    private List<Map> add = new ArrayList<>();

    /**
     * 修改属性信息
     */
    private List<Map> edit = new ArrayList<>();

    /**
     * 删除属性信息
     */
    private List<Map> del = new ArrayList<>();

    /**
     * 自动维护信息
     */
    private MonitorChannelTO autoMaintain = new MonitorChannelTO();

    /**
     * 权限申请信息*
     */
    private HandleAuthApplyTO handleAuthApply = new HandleAuthApplyTO();

}
