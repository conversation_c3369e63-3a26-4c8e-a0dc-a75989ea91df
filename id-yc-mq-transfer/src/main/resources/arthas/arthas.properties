#arthas.config.overrideAll=true
arthas.telnetPort=3658
arthas.httpPort=8563
arthas.ip=127.0.0.1

# seconds
arthas.sessionTimeout=1800

#arthas.enhanceLoaders=java.lang.ClassLoader

# https://arthas.aliyun.com/doc/en/auth
# arthas.username=arthas
# arthas.password=arthas

# local connection non auth, like telnet 127.0.0.1 3658
arthas.localConnectionNonAuth=true

#arthas.appName=demoapp
#arthas.tunnelServer=ws://127.0.0.1:7777/ws
#arthas.agentId=mmmmmmyiddddd

#arthas.disabledCommands=stop,dump

#arthas.outputPath=arthas-output