spring:
  kafka:
    bootstrap-servers: 10.14.153.104:30011,10.14.153.105:30012,10.14.153.106:30013
    consumer:
      auto-commit-interval: 1000
      auto-offset-reset: earliest
      boot:
        group-id: ggjzdiw3qnxf8w7xp5wvfs0nkseu
      enable-auto-commit: false
      key-deserializer: org.apache.kafka.common.serialization.StringDeserializer
      max-poll-records: 100
      value-deserializer: org.apache.kafka.common.serialization.StringDeserializer
    producer:
      batch-size: 16384
      buffer-memory: 33554432
      key-serializer: org.apache.kafka.common.serialization.StringSerializer
      linger-ms: 0
      max-request-size: 16384
      retries: 1
      value-serializer: org.apache.kafka.common.serialization.StringSerializer
    properties:
      sasl:
        jaas:
          config: org.apache.kafka.common.security.plain.PlainLoginModule required
            username="2db99086-396a-4a79-840d-5b8a0ff7ec83"
            password="@9-oUIK-3/JD%P-kIngaJ/0aFBq-i3./x$7O1yNX#71pH@@4=5xX5$3$F1-d14.y";
        mechanism: PLAIN
      ssl:
        endpoint:
          identification:
            algorithm: ''
    security:
      # 行业和省级云配置SASL_SSL  边侧云配置''
      protocol: ''
    ssl:
      truststore:
        location: classpath:mq/client.truststore.jks
        password: dms@kafka
teleinfo:
  file:
    enable: true
    provider: local
transfer:
  #数据传输的申请id
  apply_id: 1672139602850217984
  topic:
    accept: local-send
    send: local-recieve