server:
  servlet:
    session:
      cookie:
        secure: false
  port: 6210
spring:
  profiles:
    active: local
  messages:
    basename: config.msg.app,config.msg.dict,config.msg.validate
    fallback-to-system-locale: false
    cache-duration: 86400
    use-code-as-default-message: true
logging:
  file:
    name: id-yc-mq-transfer
    path: ./id-yc-mq-transfer/target/logs
  logback:
    rollingpolicy:
      # 单文件的大小，默认10M, 超过之后打包成一个日志文件
      max-file-size: 30MB
      # 日志保存的天数
      max-history: 30
  level:
    #日志级别
    root: info
app:
  #行业二级前缀和行业访问地址
  router:
    992000: http://127.0.0.1:6100