package cn.teleinfo.mq.transfer;

import cn.hutool.core.util.StrUtil;
import cn.teleinfo.mq.transfer.config.AppRouterProperties;
import org.apache.kafka.common.protocol.types.Field;
import org.junit.jupiter.api.Test;
import org.springframework.boot.test.context.SpringBootTest;

import javax.annotation.Resource;
import java.util.Map;

@SpringBootTest
public class IdMqTransferServerTest {

    @Resource
    private AppRouterProperties appRouterProperties;

    @Test
    public void router() {
        Map<String, String>  map = appRouterProperties.getRouter();
        // k8s变量名不支持双引号, 无法使用app.router."99.2000"，需要在k8s里配置成app.router."992000", 然后读取的时候转成99.2000
        for (Map.Entry<String, String> entry : map.entrySet()) {
            String key = entry.getKey();
            String prefix = key.substring(0, 2) + "." + key.substring(2);
            String value = entry.getValue();
            map.put(prefix, value);
        }
        System.out.println(map);
    }

}
