package cn.teleinfo.mq.transfer;

import lombok.extern.slf4j.Slf4j;
import org.apache.kafka.clients.producer.RecordMetadata;
import org.junit.jupiter.api.Test;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.boot.test.context.SpringBootTest;
import org.springframework.kafka.core.KafkaTemplate;
import org.springframework.kafka.support.SendResult;
import org.springframework.util.concurrent.ListenableFuture;

import javax.annotation.Resource;
import java.util.concurrent.ExecutionException;

@SpringBootTest
@Slf4j
public class MQSendTest {

    @Resource
    KafkaTemplate kafkaTemplate;

//    @Value("${transfer.topic.send}")
//    private String topic;

    @Value("${transfer.topic.send}")
    private String topic;

    @Test
    public void sendMsgDataxx(String message) throws ExecutionException, InterruptedException {

        //kafka消息发送(topic:【自定义,与消费者topic一致】、message【消息内容】)
        String jsonString = message;
        ListenableFuture resule = kafkaTemplate.send(topic, jsonString);
        SendResult sendResult = (SendResult) resule.get();
        RecordMetadata recordMetadata = sendResult.getRecordMetadata();
        boolean b = recordMetadata.hasOffset();

        if (b) {
            System.out.println("========消息发送成功::"+1);

        }else {
            System.out.println("========消息发送成功::"+1);
        }
    }

}
