package cn.teleinfo.idycprovince.server.config;

import lombok.Data;
import org.springframework.boot.context.properties.ConfigurationProperties;
import org.springframework.context.annotation.Configuration;

@Data
@Configuration
@ConfigurationProperties(prefix = "api")
public class IpProperties {

    private String ip;

    public String uploadUrl = "/api/v1/public/object/handle/accept/addOrUpdate";

    private String deleteUrl = "/api/v1/public/object/handle/accept/delete";

    private String syncProvinceUrl = "/api/v1/public/handle/accept/addOrUpdate";

    private String syncDeleteProvinceUrl = "/api/v1/public/handle/accept/delete";

    private String syncUserUrl = "/api/v1/public/integrated-users";

    private String syncAppUrl = "/api/v1/public/integrated-applications";

    private String syncChannelUrl = "/api/v1/public/integrated-channels";

    private String syncHandleUrl = "/api/v1/public/integrated-handles";

    private String syncEntPrefixUrl = "/api/v1/public/integrated-ent-prefix";

    private String syncDataServiceUrl = "/api/v1/public/integrated-data-service";

    private String syncEntUrl = "/api/v1/public/integrated-ent";

}
