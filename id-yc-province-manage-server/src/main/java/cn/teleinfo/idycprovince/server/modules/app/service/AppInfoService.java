package cn.teleinfo.idycprovince.server.modules.app.service;

import cn.hutool.core.lang.tree.Tree;
import cn.teleinfo.idycprovince.infrastructure.db.entity.AppInfoEntity;
import cn.teleinfo.idycprovince.server.modules.app.dto.AppInfoDTO;
import cn.teleinfo.idycprovince.server.modules.app.dto.AppInfoKeyDTO;
import cn.teleinfo.idycprovince.server.modules.app.vo.AppInfoVO;
import cn.teleinfo.idycprovince.server.modules.app.vo.EntPrefixListVO;
import cn.teleinfo.idycprovince.server.modules.app.vo.KeyPairVO;
import cn.teleinfo.idycprovince.server.modules.system.vo.EntPrefixVO;
import com.abluepoint.summer.mvc.domain.PageResult;
import com.fasterxml.jackson.core.JsonProcessingException;
import org.springframework.data.domain.Pageable;

import java.util.List;

public interface AppInfoService {

    AppInfoEntity createAppInfo(AppInfoDTO appInfoDTO) throws Exception;

    void updateAppInfo(AppInfoDTO appInfoDTO) throws JsonProcessingException;

    AppInfoVO detail(Long id);

    void removeAppInfo(Long id) throws JsonProcessingException;

    PageResult<AppInfoVO> pageAppInfo(String appName,Integer appType,Pageable pageable);

    List<AppInfoVO> getAppList(String appName);

    List<EntPrefixVO> queryPrefixList(String prefix);

    List<Tree<String>> dmmAppTree();

    List<AppInfoVO> list(Long entId);

    List<EntPrefixListVO> getEntPrefixList(Long entId);

    void securityCertificate(AppInfoKeyDTO appInfoKeyDTO);

    KeyPairVO createKeyPair();

    // 开启关闭审核
    void audit(Long id);
}
