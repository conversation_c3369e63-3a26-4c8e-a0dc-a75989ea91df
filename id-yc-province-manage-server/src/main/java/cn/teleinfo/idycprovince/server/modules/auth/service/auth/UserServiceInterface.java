package cn.teleinfo.idycprovince.server.modules.auth.service.auth;

import cn.teleinfo.idycprovince.infrastructure.db.entity.UserEntity;
import cn.teleinfo.idycprovince.server.modules.auth.controller.dto.ForgetDto;
import cn.teleinfo.idycprovince.server.modules.auth.controller.dto.InternalAccountDto;
import cn.teleinfo.idycprovince.server.modules.auth.controller.dto.SocialUserAddDTO;
import cn.teleinfo.idycprovince.server.modules.auth.controller.dto.SocialUserUpdateDTO;
import cn.teleinfo.idycprovince.server.modules.auth.controller.dto.UserDto;
import cn.teleinfo.idycprovince.server.modules.auth.controller.vo.UserVo;
import cn.teleinfo.idycprovince.server.modules.prefix.vo.EntDropDownVO;
import com.abluepoint.summer.mvc.domain.PageResult;

import java.util.List;

public interface UserServiceInterface {
    void addUserAdmin(UserDto userDto);

    void addUserEntAdmin(UserDto userDto);

    void modifyUserInfo(UserDto userDto);
    UserEntity modifyUserInfoBySelf(UserDto userDto);

    void modifyUserPwd(UserDto userDto);

    void forgetUserPasssword(ForgetDto forgetDto);

    Boolean existsAllByProvinceId(Long provinceId);

    PageResult<UserVo> getUserList(String username,Integer type, Integer currentPage, Integer pageSize, Boolean isSupperAdmin);

    void bindHandleUser(UserDto userDto);

    void unbindHandleUser(UserDto userDto);

    UserVo getUserDetail(Long id);
    UserEntity getUserDetailByEmailAndProvinceId(String email, Long provinceId);
    UserEntity getUserDetailByEmail(String  email);
    UserVo getUserInfo(Long id);

    void removeUser(Long id);

    List<EntDropDownVO> entDropDowntList();

    void checkEmailLock(String key);

    void addSocialUser(SocialUserAddDTO socialUserAddDTO);

    void updateSocialUser(SocialUserUpdateDTO socialUserUpdateDTO);

    void resetPwdUser(UserDto userDto);

    Long addInternalAccount(InternalAccountDto internalAccountDto);
}
