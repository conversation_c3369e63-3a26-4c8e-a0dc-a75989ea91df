package cn.teleinfo.idycprovince.server.config;

import cn.teleinfo.idycprovince.common.manager.AmazonS3FileManager;
import com.amazonaws.ClientConfiguration;
import com.amazonaws.auth.AWSCredentials;
import com.amazonaws.auth.AWSCredentialsProvider;
import com.amazonaws.auth.AWSStaticCredentialsProvider;
import com.amazonaws.auth.BasicAWSCredentials;
import com.amazonaws.client.builder.AwsClientBuilder;
import com.amazonaws.services.s3.AmazonS3;
import com.amazonaws.services.s3.AmazonS3Client;
import org.springframework.boot.autoconfigure.condition.ConditionalOnProperty;
import org.springframework.boot.context.properties.EnableConfigurationProperties;
import org.springframework.context.annotation.Bean;
import org.springframework.context.annotation.Configuration;

@Configuration
@ConditionalOnProperty(prefix = "app", name = "file-manager", havingValue = "s3")
@EnableConfigurationProperties({S3Properties.class})
public class S3Config {
    @Bean
    public AmazonS3 amazonS3(S3Properties s3Properties) {
        // 客户端配置，主要是全局的配置信息
        ClientConfiguration clientConfiguration = new ClientConfiguration();
        clientConfiguration.setMaxConnections(100);
        // url以及region配置
        AwsClientBuilder.EndpointConfiguration endpointConfiguration = new AwsClientBuilder.EndpointConfiguration(
                s3Properties.getServiceEndpoint(), s3Properties.getSignRegion());
        // 凭证配置
        AWSCredentials awsCredentials = new BasicAWSCredentials(s3Properties.getAccessKey(),
                s3Properties.getSecretKey());
        AWSCredentialsProvider awsCredentialsProvider = new AWSStaticCredentialsProvider(awsCredentials);
        // build amazonS3Client客户端
        AmazonS3 amazonS3 = AmazonS3Client.builder().withEndpointConfiguration(endpointConfiguration)
                .withClientConfiguration(clientConfiguration).withCredentials(awsCredentialsProvider)
                .disableChunkedEncoding().withPathStyleAccessEnabled(s3Properties.getPathStyleAccess()).build();
        return amazonS3;
    }

    @Bean
    public AmazonS3FileManager configS3Manager(AmazonS3 amazonS3, S3Properties s3Properties) {
        return new AmazonS3FileManager(amazonS3, s3Properties.getBucketName());
    }

}
