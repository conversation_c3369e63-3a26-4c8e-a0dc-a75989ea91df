package cn.teleinfo.idycprovince.server.config;

import org.springframework.context.annotation.Bean;
import org.springframework.context.annotation.Configuration;
import org.springframework.core.task.AsyncTaskExecutor;
import org.springframework.scheduling.concurrent.ThreadPoolTaskExecutor;

/**
 * <AUTHOR>
 * @version 1.0
 * @date Created in 2022/11/10
 * @description
 */
@Configuration
public class AsyncConfig {

    //最大线程数
    private static final Integer MAX_POOL_SIZE = 8;
    //核心线程数
    private static final Integer CORE_POOL_SIZE = 8;
    //阻塞队列容量
    private static final Integer QUEUE_CAPACITY = 1000;

    @Bean("asyncTaskExecutor")
    public AsyncTaskExecutor asyncTaskExecutor(){
        ThreadPoolTaskExecutor taskExecutor = new ThreadPoolTaskExecutor();
        taskExecutor.setCorePoolSize(CORE_POOL_SIZE);
        taskExecutor.setMaxPoolSize(MAX_POOL_SIZE);
        taskExecutor.setQueueCapacity(QUEUE_CAPACITY);
        taskExecutor.setThreadNamePrefix("async-task-thread-pool-");
        taskExecutor.initialize();
        return taskExecutor;
    }
}
