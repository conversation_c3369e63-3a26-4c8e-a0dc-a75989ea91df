package cn.teleinfo.idycprovince.server.modules.auth.model;

import com.fasterxml.jackson.annotation.JsonIgnore;
import org.springframework.security.core.CredentialsContainer;
import org.springframework.security.core.GrantedAuthority;
import org.springframework.security.core.SpringSecurityCoreVersion;
import org.springframework.security.core.userdetails.UserDetails;
import org.springframework.util.Assert;

import java.io.Serializable;
import java.util.*;

/*
 * File: CUserDetail.java
 * <AUTHOR>
 * @since 2022-07-02
 *
 * Copyright (c) 2022 abluepoint teleinfo All Rights Reserved.
 */

public class UserDetail implements UserDetails, CredentialsContainer {

    private static final long serialVersionUID = 7139121134185952573L;

    private final Long userId;
    @JsonIgnore
    private String password;
    private final String username;
    private final String nickName;
    private final Set<GrantedAuthority> authorities;
    private final boolean accountNonExpired;
    private final boolean accountNonLocked;
    private final boolean credentialsNonExpired;
    private final boolean enabled;
    private final Long provinceId;

    /**
     * 企业租户id
     */
    private final Long entId;
    private final String role = "role_user";
    /**
     * 应用id
     */
    private final Long appId;
    /**
     * 角色编码
     */
    private final List<String> roleCodes;

    /**
     * 标识用户
     */
    private final List<HandleUserTO> handleUserTOList;

    @Override
    public int hashCode() {
        return Objects.hash(userId);
    }

    @Override
    public boolean equals(Object obj) {
        if (this == obj) return true;
        if (obj instanceof UserDetail) {
            if (Objects.equals(this.userId, ((UserDetail) obj).userId)) return true;
        }
        return false;
    }

    public UserDetail(Long userId,
                      String password,
                      String username,
                      String nickName,
                      Set<GrantedAuthority> authorities,
                      boolean accountNonExpired,
                      boolean accountNonLocked,
                      boolean credentialsNonExpired,
                      boolean enabled,
                      Long provinceId,
                      Long entId,
                      Long appId,
                      List<String> roleCodes, List<HandleUserTO> handleUserTOList) {
        this.userId = userId;
        this.password = password;
        this.username = username;
        this.nickName = nickName;
        this.authorities = Collections.unmodifiableSet(sortAuthorities(authorities));
        this.accountNonExpired = accountNonExpired;
        this.accountNonLocked = accountNonLocked;
        this.credentialsNonExpired = credentialsNonExpired;
        this.enabled = enabled;
        this.provinceId = provinceId;
        this.entId = entId;
        this.appId = appId;
        this.roleCodes = roleCodes;
        this.handleUserTOList = handleUserTOList;
    }

    private static SortedSet<GrantedAuthority> sortAuthorities(Collection<? extends GrantedAuthority> authorities) {
        Assert.notNull(authorities, "Cannot pass a null GrantedAuthority collection");
        // Ensure array iteration order is predictable (as per
        // UserDetails.getAuthorities() contract and SEC-717)
        SortedSet<GrantedAuthority> sortedAuthorities = new TreeSet<>(new UserDetail.AuthorityComparator());
        for (GrantedAuthority grantedAuthority : authorities) {
            Assert.notNull(grantedAuthority, "GrantedAuthority list cannot contain any null elements");
            sortedAuthorities.add(grantedAuthority);
        }
        return sortedAuthorities;
    }

    public static CUserDetailBuilder builder() {
        return new CUserDetailBuilder();
    }

    @Override
    public void eraseCredentials() {
        this.password = null;
    }

    public Long getUserId() {
        return this.userId;
    }

    public String getPassword() {
        return this.password;
    }

    public String getUsername() {
        return this.username;
    }

    public String getNickName() {
        return this.nickName;
    }

    public Set<GrantedAuthority> getAuthorities() {
        return this.authorities;
    }

    public boolean isAccountNonExpired() {
        return this.accountNonExpired;
    }

    public boolean isAccountNonLocked() {
        return this.accountNonLocked;
    }

    public boolean isCredentialsNonExpired() {
        return this.credentialsNonExpired;
    }

    public boolean isEnabled() {
        return this.enabled;
    }

    public String getRole() {
        return this.role;
    }

    public Long getProvinceId() {
        return this.provinceId;
    }

    public Long getEntId() {
        return this.entId;
    }
    
    public Long getAppId() {
        return this.appId;
    }

    public List<String> getRoleCodes() {
        return roleCodes;
    }

    public List<HandleUserTO> getHandleUserTOList() {
        return this.handleUserTOList;
    }

    private static class AuthorityComparator implements Comparator<GrantedAuthority>, Serializable {

        private static final long serialVersionUID = SpringSecurityCoreVersion.SERIAL_VERSION_UID;

        @Override
        public int compare(GrantedAuthority g1, GrantedAuthority g2) {
            // Neither should ever be null as each entry is checked before adding it to
            // the set. If the authority is null, it is a custom authority and should
            // precede others.
            if (g2.getAuthority() == null) {
                return -1;
            }
            if (g1.getAuthority() == null) {
                return 1;
            }
            return g1.getAuthority().compareTo(g2.getAuthority());
        }

    }

    public static class CUserDetailBuilder {
        private Long userId;
        private String password;
        private String username;
        private String nickName;
        private Set<GrantedAuthority> authorities;
        private boolean accountNonExpired;
        private boolean accountNonLocked;
        private boolean credentialsNonExpired;
        private boolean enabled;
        private Long provinceId;
        private Long entId;
        private Long appId;

        private List<HandleUserTO> handleUserTOList;

        private List<String> roleCodes;

        CUserDetailBuilder() {
        }

        public CUserDetailBuilder userId(Long userId) {
            this.userId = userId;
            return this;
        }

        public CUserDetailBuilder password(String password) {
            this.password = password;
            return this;
        }

        public CUserDetailBuilder username(String username) {
            this.username = username;
            return this;
        }

        public CUserDetailBuilder nickName(String nickName) {
            this.nickName = nickName;
            return this;
        }

        public CUserDetailBuilder provinceId(Long provinceId) {
            this.provinceId = provinceId;
            return this;
        }

        public CUserDetailBuilder entId(Long entId) {
            this.entId = entId;
            return this;
        }
    
        public CUserDetailBuilder appId(Long appId) {
            this.appId = appId;
            return this;
        }

        public CUserDetailBuilder authorities(Set<GrantedAuthority> authorities) {
            this.authorities = authorities;
            return this;
        }

        public CUserDetailBuilder accountNonExpired(boolean accountNonExpired) {
            this.accountNonExpired = accountNonExpired;
            return this;
        }

        public CUserDetailBuilder accountNonLocked(boolean accountNonLocked) {
            this.accountNonLocked = accountNonLocked;
            return this;
        }

        public CUserDetailBuilder credentialsNonExpired(boolean credentialsNonExpired) {
            this.credentialsNonExpired = credentialsNonExpired;
            return this;
        }

        public CUserDetailBuilder enabled(boolean enabled) {
            this.enabled = enabled;
            return this;
        }

        public CUserDetailBuilder handleUserTOList(List<HandleUserTO> handleUserTOList) {
            this.handleUserTOList = handleUserTOList;
            return this;
        }

        public CUserDetailBuilder roleCodes(List<String> roleCodes) {
            this.roleCodes = roleCodes;
            return this;
        }

        public UserDetail build() {
            return new UserDetail(userId, password,
                    username, nickName, authorities, accountNonExpired,
                    accountNonLocked, credentialsNonExpired, enabled,
                    provinceId,
                    entId,
                    appId,
                    roleCodes, handleUserTOList);
        }

        public String toString() {
            return "CUserDetail.CUserDetailBuilder(userId=" + this.userId + ", password=" + this.password + ", username=" + this.username + ", nickName=" + this.nickName + ", authorities=" + this.authorities + ", accountNonExpired=" + this.accountNonExpired + ", accountNonLocked=" + this.accountNonLocked + ", credentialsNonExpired=" + this.credentialsNonExpired + ", enabled=" + this.enabled + ", cif=" + ")";
        }
    }
}
