package cn.teleinfo.idycprovince.server.config;

import cn.hutool.core.util.ObjectUtil;
import cn.teleinfo.idycprovince.server.modules.auth.util.SecurityUtil;
import org.springframework.context.annotation.Configuration;
import org.springframework.data.domain.AuditorAware;
import org.springframework.security.core.context.SecurityContextHolder;

import java.util.Optional;
@Configuration
public class AuditorConfig implements AuditorAware<Long> {

    /**
     * 返回操作员标志信息
     *
     * @return
     */
    @Override
    public Optional<Long> getCurrentAuditor() {
        if(ObjectUtil.isNotEmpty(SecurityContextHolder.getContext().getAuthentication())){
            try {
                // 这里应根据实际业务情况获取具体信息
                return Optional.of(SecurityUtil.getCurrentUserId());
            } catch (Exception e) {
                // ignored
            }
        }
        
        // 用户定时任务，或者无Token调用的情况
        return Optional.ofNullable(null);
    }

}