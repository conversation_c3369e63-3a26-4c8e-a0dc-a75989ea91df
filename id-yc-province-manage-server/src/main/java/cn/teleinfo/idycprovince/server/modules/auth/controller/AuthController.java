package cn.teleinfo.idycprovince.server.modules.auth.controller;

import cn.teleinfo.idycprovince.common.exception.BusinessCodeMessage;
import cn.teleinfo.idycprovince.server.modules.auth.controller.dto.AuthDto;
import cn.teleinfo.idycprovince.server.modules.auth.controller.vo.AuthVo;
import cn.teleinfo.idycprovince.server.modules.auth.service.auth.AuthServiceInterface;
import cn.teleinfo.idycprovince.server.modules.auth.service.auth.RoleServiceInterface;
import cn.teleinfo.idycprovince.server.modules.logcallback.RestfulLogCallback;
import cn.teleinfo.idycprovince.server.modules.util.Assert;
import cn.teleinfo.summer.log.annotation.OpLog;
import com.abluepoint.summer.mvc.domain.R;
import com.abluepoint.summer.mvc.domain.Result;
import lombok.RequiredArgsConstructor;
import org.springframework.security.access.prepost.PreAuthorize;
import org.springframework.util.CollectionUtils;
import org.springframework.web.bind.annotation.*;

import java.util.List;

@RestController
@RequestMapping("/api/v1/auth")
@RequiredArgsConstructor
public class AuthController {

    private final RoleServiceInterface roleService;
    private final AuthServiceInterface authService;

    /**
     * 按角色查询权限树
     *
     * @param roleId
     * @return
     */
    @GetMapping("/tree/list")
    @OpLog(value = "按角色信息查询权限树", callback = RestfulLogCallback.class)
    @PreAuthorize("hasAuthority('SYS:AUTH:TREELIST')")
    public Result authTree(Long roleId) {

        Assert.notNull(roleId, BusinessCodeMessage.ID_NOT_EXIST);
        List<AuthVo> authVoList = authService.getAuthTreeByRoleId(roleId);

        return R.ok(authVoList);
    }

    /**
     * 更新权限树
     *
     * @param authDto,核心字段为roleId,roleCode
     * @return
     */
    @PostMapping("/tree/update")
    @OpLog(value = "更新用户权限", callback = RestfulLogCallback.class)
    @PreAuthorize("hasAuthority('SYS:AUTH:TREEEDIT')")
    public Result authSetting(@RequestBody AuthDto authDto) {
        //TODO:缺少校验，需要前端配合修改
        Long roleId = authDto.getRoleId();
        Assert.notBlankLong(roleId, BusinessCodeMessage.ROLE_NOT_EXIST);
        List<Long> authIdList = authDto.getAuthIdList();
        Assert.assertTrue(!CollectionUtils.isEmpty(authIdList), BusinessCodeMessage.AUTH_NOT_EXIST);
        authService.removeRoleAuth(roleId);
        roleService.modifyRoleAuth(roleId, authIdList);
        return R.ok();
    }
}
