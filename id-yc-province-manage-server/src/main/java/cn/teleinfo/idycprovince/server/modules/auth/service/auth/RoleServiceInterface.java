package cn.teleinfo.idycprovince.server.modules.auth.service.auth;


import cn.teleinfo.idycprovince.infrastructure.db.entity.RoleEntity;
import cn.teleinfo.idycprovince.server.enums.RoleTypeEnum;
import cn.teleinfo.idycprovince.server.modules.auth.controller.dto.RoleDto;
import cn.teleinfo.idycprovince.server.modules.auth.controller.vo.RoleVo;
import com.abluepoint.summer.mvc.domain.PageResult;

import java.util.List;
import java.util.Set;

public interface RoleServiceInterface {

    void checkRoleUndelete(Long roleId);
    RoleEntity getRoleById(Long roleId);

    PageResult<RoleVo> getRoleList(RoleDto roleDto, Integer currentPage, Integer pageSize);

    void addRole(RoleDto roleDto);

    Set<Integer> getRoleSort();

    RoleVo getRoleDetail(Long id);
    RoleEntity getSuperAdminRole(Long provinceId);

    List<RoleVo> getAllRole(Integer type);

    void modifyRole(RoleDto roleDto);

    void removeRole(Long id);

    void removeUserRole(Long userId);

    void modifyRoleAuth(Long roleId, List<Long> authIdList);

    void presetProvinceRole(Long provinceId);

    RoleEntity getRoleByProvinceIdAndRoleTypeEnum(Long provinceId, RoleTypeEnum roleTypeEnumEnt);

    List<RoleVo> getRoleListWhenAddUser();
}
