package cn.teleinfo.idycprovince.server.config;

import cn.teleinfo.idycprovince.infrastructure.db.entity.LoginConfigEntity;
import cn.teleinfo.idycprovince.infrastructure.db.repository.LoginConfigRepository;
import cn.teleinfo.idycprovince.infrastructure.redis.RedisCache;
import cn.teleinfo.idycprovince.server.session.TobaccoSessionRepository;
import org.apache.commons.lang3.ObjectUtils;
import org.springframework.boot.autoconfigure.condition.ConditionalOnProperty;
import org.springframework.boot.autoconfigure.session.SessionProperties;
import org.springframework.context.annotation.Bean;
import org.springframework.context.annotation.Configuration;
import org.springframework.session.config.annotation.web.http.SpringHttpSessionConfiguration;

import java.time.Duration;

/**
 * 多云适配spring sessions配置
 */
@ConditionalOnProperty(prefix = "app", name = "cache", havingValue = "tbc")
@Configuration(proxyBeanMethods = false)
public class TobaccoSessionConfig extends SpringHttpSessionConfiguration {

    private String redisNamespace;
    private Duration maxInactiveIntervalInSeconds;
    private final RedisCache redisCache;
    private final LoginConfigRepository loginConfigRepository;

    public TobaccoSessionConfig(RedisCache redisCache, SessionProperties sessionProperties,
            LoginConfigRepository loginConfigRepository) {
        super();
        this.redisCache = redisCache;
        this.loginConfigRepository = loginConfigRepository;
        this.redisNamespace = "province:session";
        this.maxInactiveIntervalInSeconds = sessionProperties.getTimeout();
    }


    @Bean
    public TobaccoSessionRepository tobaccoSessionRepository() {
        TobaccoSessionRepository repository = new TobaccoSessionRepository(redisCache);
        repository.setRedisKeyNamespace(this.redisNamespace);
        LoginConfigEntity entity = loginConfigRepository.findAll().get(0);
        if(ObjectUtils.isEmpty(entity)){
            repository.setDefaultMaxInactiveInterval(this.maxInactiveIntervalInSeconds);
        } else {
            Duration duration = Duration.ofMinutes(entity.getStaticLogoutTime());
            repository.setDefaultMaxInactiveInterval(duration);
        }
        
        return repository;
    }
}