package cn.teleinfo.idycprovince.server.config;

import cn.tobacco.tcaf.cache.Caching;
import cn.tobacco.tcaf.spring.cacheManager.TcafCacheManager;
import org.springframework.boot.autoconfigure.condition.ConditionalOnProperty;
import org.springframework.cache.CacheManager;
import org.springframework.cache.annotation.CachingConfigurerSupport;
import org.springframework.cache.annotation.EnableCaching;
import org.springframework.cache.interceptor.KeyGenerator;
import org.springframework.context.annotation.Bean;
import org.springframework.context.annotation.Configuration;
import org.springframework.context.annotation.Primary;

import java.util.HashMap;
import java.util.Map;



@Configuration
@EnableCaching
@ConditionalOnProperty(prefix = "app", name = "cache", havingValue = "tbc")
public class CacheConfig extends CachingConfigurerSupport {

    private final Caching caching;

    /**
     * @see cn.tobacco.tcaf.spring.ConfigLoader#getCache
     */
    public CacheConfig(Caching caching) {
        this.caching = caching;
    }

    @Bean
    @Primary
    @Override
    public CacheManager cacheManager() {
        Map<String,Integer> initCacheInfo = new HashMap<>();
        // cacheName  ttl
        initCacheInfo.put("cuser:vcode",300);
        //不同cache s设置不同的 失效时间
        TcafCacheManager cacheManager = new TcafCacheManager(86400,caching.getCache(),initCacheInfo);
        //全部chache 统一一个失效时间
       // TcafCacheManager cacheManager = new TcafCacheManager(50,caching.getCache());
        return cacheManager;
    }

    // 可以不用我的这个 keyGenerator
    @Override
    @Bean
    public KeyGenerator keyGenerator() {
        return (o, method, objects) -> {
            StringBuilder sb = new StringBuilder();
            sb.append(o.getClass().getName()).append(".");
            sb.append(method.getName()).append(".");
            for (Object obj : objects) {
                sb.append(obj.toString());
            }
            return sb.toString();
        };
    }
}
