package cn.teleinfo.idycprovince.server.enums;

public enum RequestResolveTypeEnum {

    // 1-正向解析；2-反向追溯
    RESOLVE(1, "正向解析"),
    BACKUP(2, "反向追溯"),
    ;

    private Integer code;
    private String desc;

    RequestResolveTypeEnum(Integer code, String desc) {
        this.code = code;
        this.desc = desc;
    }

    public int getCode() {
        return this.code;
    }

    public String getDesc() {
        return this.desc;
    }
}
