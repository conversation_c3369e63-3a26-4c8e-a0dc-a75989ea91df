package cn.teleinfo.idycprovince.server.config;

import cn.teleinfo.idycprovince.server.feign.ubcp.TenantAwareMyClient;
import com.alibaba.cloudapi.sdk.model.HttpClientBuilderParams;
import com.bizworks.ubcp.apiclient.HttpApiClientubcpCenters;
import com.bizworks.ubcp.apiclient.feign.ApiGatewayAuthenticationProperty;
import com.bizworks.ubcp.apiclient.feign.BizworksApiGateway;
import com.bizworks.ubcp.apiclient.feign.MyClient;
import com.bizworks.ubcp.apiclient.feign.UbcpFeign;
import feign.Feign;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections4.CollectionUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.boot.autoconfigure.condition.ConditionalOnExpression;
import org.springframework.context.EnvironmentAware;
import org.springframework.context.annotation.Bean;
import org.springframework.context.annotation.ComponentScan;
import org.springframework.context.annotation.Configuration;
import org.springframework.context.annotation.Primary;
import org.springframework.core.env.Environment;
import org.springframework.util.Assert;

import java.util.*;

/**
 * 阿里用户中台接入网关配置
 *
 * <AUTHOR>
 * @date 2025/3/1 14:12
 */
@Slf4j
@Configuration
@ComponentScan("com.bizworks.ubcp.apiclient.feign")
@ConditionalOnExpression("'tenant'.equals('${bizworks-api-gateway.type}') and 'true'.equals('${bizworks-api-gateway.enable}') ")
public class TenantApiGatewayConfiguration implements EnvironmentAware {
    @Override
    public void setEnvironment(Environment environment) {
        log.info("开启阿里网关多租户模式");
    }

    @Bean
    @Primary
    public Feign.Builder builder(@Autowired BizworksApiGateway bizworksApiGateway) {
        Assert.isTrue(CollectionUtils.isNotEmpty(bizworksApiGateway.getSignature()), "网关配置不能为空");

        String[] disSignServiceArray = bizworksApiGateway.getDisSignService().split(",");
        List<String> disSignServiceList = Arrays.asList(disSignServiceArray);

        // 构建租户服务客户端映射
        Map<String, Map<String, MyClient>> tenantServiceClientMap = buildTenantServiceClientMap(bizworksApiGateway.getSignature());

        // 构建服务客户端映射
        Map<String, MyClient> clientMap = buildServiceClientMap(tenantServiceClientMap);

        // 使用我们的自定义Builder
        UbcpFeign.Builder builder = UbcpFeign.builder();
        builder.myClientMap(clientMap);
        builder.disSignServiceList(disSignServiceList);
        return builder;
    }

    /**
     * 构建租户服务客户端映射
     * 结构：Map<租户ID, Map<服务名, MyClient>>
     */
    private Map<String, Map<String, MyClient>> buildTenantServiceClientMap(List<ApiGatewayAuthenticationProperty> properties) {
        Map<String, Map<String, MyClient>> result = new HashMap<>();
        for (ApiGatewayAuthenticationProperty property : properties) {
            String services = property.getService();
            if (Objects.nonNull(services)) {
                String[] serviceArray = services.split(",");
                for (String service : serviceArray) {
                    service = service.trim();
                    // 解析租户ID和服务名，格式：tenant:tenantId:serviceName
                    String[] parts = service.split(":");
                    if (parts.length >= 3 && "tenant".equals(parts[0])) {
                        String tenantId = parts[1];
                        String serviceName = parts[2];
                        // 为该租户创建服务客户端映射
                        Map<String, MyClient> serviceClientMap = result.computeIfAbsent(
                                tenantId, k -> new HashMap<>());
                        // 为该租户的指定服务创建客户端
                        MyClient client = buildClient(property);
                        serviceClientMap.put(serviceName, client);
                    }
                }
            }
        }
        return result;
    }

    /**
     * 构建服务客户端映射
     * 结构：Map<服务名, TenantAwareMyClient>
     */
    private Map<String, MyClient> buildServiceClientMap(Map<String, Map<String, MyClient>> tenantServiceClientMap) {
        Map<String, MyClient> result = new HashMap<>();

        // 收集所有不同的服务名
        for (Map<String, MyClient> serviceClientMap : tenantServiceClientMap.values()) {
            for (String serviceName : serviceClientMap.keySet()) {
                // 为每个服务名创建一个TenantAwareMyClient
                if (!result.containsKey(serviceName)) {
                    result.put(serviceName, new TenantAwareMyClient(tenantServiceClientMap, serviceName));
                }
            }
        }

        // 添加DEFAULT_SERVICE
        result.put("DEFAULT_SERVICE", new TenantAwareMyClient(tenantServiceClientMap, "DEFAULT_SERVICE"));

        return result;
    }

    private MyClient buildClient(ApiGatewayAuthenticationProperty property) {
        HttpApiClientubcpCenters httpApiClientubcpCenters = HttpApiClientubcpCenters.initEnv(
                property.getHost(),
                property.getAppKey(),
                property.getAppSecret()
        );
        HttpClientBuilderParams httpParam = new HttpClientBuilderParams();
        httpApiClientubcpCenters.init(httpParam);
        return new MyClient(httpApiClientubcpCenters, property.getBizId());
    }

}
