package cn.teleinfo.idycprovince.server.enums;

/**
 * MQ消息处理失败枚举类型
 * CODE为50x开头，定时器不重复发请求
 * CODE为40x开头，定时器会重复发请求
 */
public class MqResult {

    static String MQ_RESULT_SUCCESS_MSG = "操作成功";
    static String MQ_RESULT_ERROR_MSG = "操作失败";


    private int code;
    private String msg;

    private Object data;

    MqResult(int code, String msg) {
        this.code = code;
        this.msg = msg;
    }

    public MqResult(int code, String msg, Object data) {
        this.code = code;
        this.msg = msg;
        this.data = data;
    }

    public static MqResult success() {
        return new MqResult(MqResultCodeEnum.MQ_RESULT_CODE_ENUM_SUCCESS.code, MqResult.MQ_RESULT_SUCCESS_MSG);
    }

    public static MqResult success(String msg) {
        return new MqResult(MqResultCodeEnum.MQ_RESULT_CODE_ENUM_SUCCESS.code, msg);
    }

    public static MqResult success(Object data) {
        return new MqResult(MqResultCodeEnum.MQ_RESULT_CODE_ENUM_SUCCESS.code, MqResult.MQ_RESULT_SUCCESS_MSG, data);
    }

    public static MqResult error() {
        return new MqResult(MqResultCodeEnum.MQ_RESULT_CODE_ENUM_SUCCESS.code, MqResult.MQ_RESULT_ERROR_MSG);
    }

    /**
     * 默认不重试错误
     * @param errorDesc
     * @return
     */
    public static MqResult error(String errorDesc) {
        return new MqResult(MqResultCodeEnum.MQ_RESULT_CODE_ENUM_NOTRY.code, errorDesc);
    }

    public static MqResult error(Boolean retry, String errorDesc) {
        return new MqResult(retry?MqResultCodeEnum.MQ_RESULT_CODE_ENUM_RETRY.code:MqResultCodeEnum.MQ_RESULT_CODE_ENUM_NOTRY.code, errorDesc);
    }

    public static MqResult error(Object data) {
        return new MqResult(MqResultCodeEnum.MQ_RESULT_CODE_ENUM_NOTRY.code, MqResult.MQ_RESULT_ERROR_MSG, data);
    }

    public static MqResult error(Boolean retry, String errorDesc, Object data) {
        return new MqResult(retry?MqResultCodeEnum.MQ_RESULT_CODE_ENUM_RETRY.code:MqResultCodeEnum.MQ_RESULT_CODE_ENUM_NOTRY.code, errorDesc, data);
    }

    public static MqResult error(MqResultCodeEnum mqResultCodeEnum, String errorDesc) {
        return new MqResult(mqResultCodeEnum.code, errorDesc);
    }

    public static MqResult error(int errorCode, String errorDesc, Object data) {
        return new MqResult(errorCode, errorDesc, data);
    }
}
