package cn.teleinfo.idycprovince.server.modules.auth.service;

import com.abluepoint.summer.common.util.EncryptionUtils;
import com.abluepoint.summer.common.util.KeyConverter;
import lombok.RequiredArgsConstructor;
import org.springframework.cache.Cache;
import org.springframework.cache.CacheManager;

import java.security.KeyPair;
import java.security.NoSuchAlgorithmException;
import java.security.PrivateKey;
import java.security.PublicKey;

/*
 * File: PublicKeyService.java
 * <AUTHOR>
 * @since 2022-07-03
 *
 * Copyright (c) 2022 abluepoint teleinfo All Rights Reserved.
 */
@RequiredArgsConstructor
//@Service
public class CachedPublicKeyService implements PublicKeyServiceInterface {

    private final CacheManager cacheManager;
    private int defaultTTL = 24 * 60 * 60;
    private String defaultKeyName = "sys.pwd.key";

    public KeyInfo getKeyInfoFromCache(String key) {
        Cache cache = getCache();
        KeyInfo keyInfo = cache.get(key, KeyInfo.class);
        return keyInfo;
    }

    private Cache getCache() {
        Cache cache = cacheManager.getCache("sys:key");
        return cache;
    }

    public KeyInfo getKeyInfo(String key) throws NoSuchAlgorithmException {
        Cache cache = getCache();
        KeyInfo keyInfo = cache.get(key, KeyInfo.class);

        // ttl 失效清理缓存
        if (keyInfo != null) {
            if (System.currentTimeMillis() - keyInfo.getTimestamp() > (keyInfo.getTtl() * 1000)) {
                // 清理缓存
                cache.evict(key);
                keyInfo = null;
            }
        }

        if (keyInfo == null) {
            synchronized (this) {
                if (keyInfo == null) {
                    KeyPair keyPair = EncryptionUtils.generateKeyPair();
                    PublicKey publicKey = keyPair.getPublic();
                    PrivateKey privateKey = keyPair.getPrivate();
                    keyInfo = new KeyInfo(KeyConverter.toX509Pem(publicKey), KeyConverter.toPkcs8UnencryptedPem(privateKey), defaultTTL, System.currentTimeMillis());
                    cache.putIfAbsent(key, keyInfo);
                }
            }
        }
        return keyInfo;
    }

    public PrivateKey getPrivateKey(String key) throws Exception {
        KeyInfo keyInfo = getKeyInfo(key);
        String privateKeyPem = keyInfo.getPrivateKeyPem();
        return KeyConverter.fromPkcs8Pem(privateKeyPem, null);
    }

    @Override
    public KeyInfo getKeyInfo() throws NoSuchAlgorithmException {
        return getKeyInfo(defaultKeyName);
    }

    @Override
    public PrivateKey getPrivateKey() throws Exception {
        return getPrivateKey(defaultKeyName);
    }




}