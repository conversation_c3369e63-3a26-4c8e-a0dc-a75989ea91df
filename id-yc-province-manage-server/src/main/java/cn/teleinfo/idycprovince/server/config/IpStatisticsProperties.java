package cn.teleinfo.idycprovince.server.config;

import lombok.Data;
import org.springframework.boot.context.properties.ConfigurationProperties;
import org.springframework.context.annotation.Configuration;

@Data
@Configuration
@ConfigurationProperties(prefix = "api.statistics")
public class IpStatisticsProperties {
    /**
     * ip地址
     */
    private String ip;
    /**
     * 前缀统计接收
     */
    private String entPrefixUrl = "/api/v1/public/statistics/data/accept";

    /**
     * 应用接入数量统计接收
     */
    private String appNumUrl = "/api/v1/public/statistics/app/data/accept";

}
