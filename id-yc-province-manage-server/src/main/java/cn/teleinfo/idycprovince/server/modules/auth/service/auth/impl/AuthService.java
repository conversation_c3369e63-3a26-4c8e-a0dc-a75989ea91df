package cn.teleinfo.idycprovince.server.modules.auth.service.auth.impl;

import cn.teleinfo.idycprovince.infrastructure.db.entity.AppInfoEntity;
import cn.teleinfo.idycprovince.infrastructure.db.entity.AuthEntity;
import cn.teleinfo.idycprovince.infrastructure.db.entity.RoleEntity;
import cn.teleinfo.idycprovince.infrastructure.db.model.TreeModel;
import cn.teleinfo.idycprovince.infrastructure.db.repository.AppInfoRepository;
import cn.teleinfo.idycprovince.infrastructure.db.repository.AuthRepository;
import cn.teleinfo.idycprovince.infrastructure.db.repository.RoleAuthRepository;
import cn.teleinfo.idycprovince.infrastructure.db.to.AuthTo;
import cn.teleinfo.idycprovince.server.enums.AppInfoEnum;
import cn.teleinfo.idycprovince.server.modules.auth.controller.vo.AuthVo;
import cn.teleinfo.idycprovince.server.modules.auth.service.auth.AuthServiceInterface;
import cn.teleinfo.idycprovince.server.modules.auth.service.auth.RoleServiceInterface;
import cn.teleinfo.idycprovince.server.modules.auth.util.SecurityUtil;
import lombok.AllArgsConstructor;
import org.apache.commons.lang3.ObjectUtils;
import org.springframework.beans.BeanUtils;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import java.util.*;
import java.util.stream.Collectors;

@Service
@AllArgsConstructor
public class AuthService implements AuthServiceInterface {
    private final AuthRepository authRepository;
    private final RoleAuthRepository roleAuthRepository;

    private final RoleServiceInterface roleService;

    private final AppInfoRepository appInfoRepository;

    @Override
    public List<AuthVo> getAuthTreeByRoleId(Long roleId) {

        RoleEntity role = roleService.getRoleById(roleId);
        //角色中的roleType 与 auth中的levelType是一致的
        Integer undelete = role.getUndelete();
        List<AuthTo> authToList = authRepository.findAllAuthListByLevelTypeAndRoleIdAndUndelete(role.getRoleType(), roleId, undelete);
        List<AuthVo> authVoList = authToList.stream().map(e -> {
            AuthVo authVo = new AuthVo();
            BeanUtils.copyProperties(e, authVo);
            return authVo;
        }).collect(Collectors.toList());
        return (List<AuthVo>) TreeModel.buildTree(authVoList, AuthEntity.ROOT_ID);
    }

    @Override
    public List<AuthVo> getAuthTreeByRoleIds(Collection<Long> roleIds,boolean mpDmmEnable) {
        List<AuthTo> authToList = authRepository.findRoleAuthListBySystemTypeAndRoleIds(roleIds);
        //判断是否为应用账号登录
        Long appId = SecurityUtil.getCurrentAppId();
        AppInfoEntity appInfoEntity = null;
        if(ObjectUtils.isNotEmpty(appId)){
             appInfoEntity = appInfoRepository.findById(appId).orElse(null);

        }
        AppInfoEntity finalAppInfoEntity = appInfoEntity;
        List<AuthVo> authVoList = authToList.stream().map(e -> {
            AuthVo authVo = new AuthVo();
            //判断中台还是非中台，中台去除元数据管理菜单
            if(ObjectUtils.isNotEmpty(finalAppInfoEntity)){
                if(mpDmmEnable && (finalAppInfoEntity.getAppType() == AppInfoEnum.MP_DMM_APP.getCode())){
                    if(e.getAuthCode().indexOf("METADATA_MANAGEMENT") != -1){
                        return null;
                    }
                }
            }

            BeanUtils.copyProperties(e, authVo);
            return authVo;
        }).filter(Objects::nonNull).collect(Collectors.toList());
        return (List<AuthVo>) TreeModel.buildTree(authVoList, AuthEntity.ROOT_ID);
    }


    @Override
    @Transactional
    public void removeRoleAuth(Long roleId) {
        roleService.checkRoleUndelete(roleId);

        roleAuthRepository.deleteByRoleId(roleId);
    }


}
