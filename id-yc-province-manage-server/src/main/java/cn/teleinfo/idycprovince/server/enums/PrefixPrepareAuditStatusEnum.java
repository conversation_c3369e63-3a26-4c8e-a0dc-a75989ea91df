package cn.teleinfo.idycprovince.server.enums;

public enum PrefixPrepareAuditStatusEnum {

    // 0-待认领；1-已认领；2-已驳回
    PASS(1, "审核通过"),
    REJECT(2, "审核驳回"),
    ;

    private Integer code;
    private String desc;

    PrefixPrepareAuditStatusEnum(Integer code, String desc) {
        this.code = code;
        this.desc = desc;
    }

    public int getCode() {
        return this.code;
    }

    public String getDesc() {
        return this.desc;
    }

}
