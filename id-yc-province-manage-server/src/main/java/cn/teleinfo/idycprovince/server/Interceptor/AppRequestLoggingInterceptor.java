package cn.teleinfo.idycprovince.server.Interceptor;

import com.abluepoint.summer.mvc.filter.CachingSupportServletRequestWrap;
import com.abluepoint.summer.mvc.interceptor.CommonRequestLoggingInterceptor;
import com.abluepoint.summer.mvc.interceptor.RequestLoggingSupport;
import org.springframework.web.util.WebUtils;

import javax.servlet.http.HttpServletRequest;
import javax.servlet.http.HttpServletResponse;

/**
 * <AUTHOR>
 * @version 1.0
 * @date Created in 2023/6/25
 * @description
 */
public class AppRequestLoggingInterceptor extends CommonRequestLoggingInterceptor {

    private RequestLoggingSupport requestLoggingSupport;

    public AppRequestLoggingInterceptor(RequestLoggingSupport requestLoggingSupport) {
        super(requestLoggingSupport);
        this.requestLoggingSupport = requestLoggingSupport;
    }

    @Override
    public boolean preHandle(HttpServletRequest request, HttpServletResponse response, Object handler) throws Exception {
        CachingSupportServletRequestWrap wrapper = WebUtils.getNativeRequest(request, CachingSupportServletRequestWrap.class);
        if (wrapper == null) {
            return true;
        }
        wrapper.cacheRequest();
        beforeRequest(request, requestLoggingSupport.getBeforeMessage(request));

        return true;
    }

}
