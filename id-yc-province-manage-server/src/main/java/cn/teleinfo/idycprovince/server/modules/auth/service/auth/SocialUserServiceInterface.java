package cn.teleinfo.idycprovince.server.modules.auth.service.auth;

import cn.teleinfo.idycprovince.server.modules.auth.controller.dto.SocialUserBindingDto;
import cn.teleinfo.idycprovince.server.modules.auth.controller.dto.SocialUserDto;
import cn.teleinfo.idycprovince.server.modules.auth.controller.dto.SocialUserExistDto;
import cn.teleinfo.idycprovince.server.modules.auth.controller.vo.SocialLoginVo;
import cn.teleinfo.idycprovince.server.modules.auth.controller.vo.SocialUserVo;

/**
 * <AUTHOR>
 */
public interface SocialUserServiceInterface {
    
    /**
     * 用户中台登录判断是否绑定
     *
     * @param socialUserExistDto
     * @return 结果
     **/
    SocialLoginVo loginExist(SocialUserExistDto socialUserExistDto);
    
    /**
     * 用户中台登录绑定
     *
     * @param socialUserDto
     * @return 结果
     **/
    void loginBinding(SocialUserDto socialUserDto);
    
    /**
     * 绑定
     *
     * @param socialUserBindingDto
     * @return 结果
     **/
    void binding(SocialUserBindingDto socialUserBindingDto);
    
    /**
     * 解绑
     *
     * @param id
     * @return 结果
     **/
    void unbinding(Long id);
    
    /**
     * 详情
     *
     * @param id
     * @return 结果
     **/
    SocialUserVo detail(Long id);
    
    /**
     * 用户中台登录 获取系统用户id
     *
     * @param username
     * @return 结果
     **/
    Long userCenterLogin(String username);

    String socialUserLogin(SocialUserExistDto socialUserExistDto);

}
