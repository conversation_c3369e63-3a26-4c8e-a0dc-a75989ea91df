package cn.teleinfo.idycprovince.server.config;

import cn.teleinfo.idycprovince.infrastructure.db.entity.LoginConfigEntity;
import cn.teleinfo.idycprovince.infrastructure.db.repository.LoginConfigRepository;
import org.apache.commons.lang3.ObjectUtils;
import org.springframework.boot.autoconfigure.condition.ConditionalOnProperty;
import org.springframework.boot.autoconfigure.session.SessionProperties;
import org.springframework.context.annotation.Configuration;
import org.springframework.session.data.redis.config.annotation.web.http.RedisHttpSessionConfiguration;

import java.time.Duration;

/**
 * 修复@EnableRedisHttpSession, session过期配置不生效的问题
 */
@Configuration
@ConditionalOnProperty(prefix = "app", name = "cache", havingValue = "redis")
public class RedisHttpSessionConfig extends RedisHttpSessionConfiguration {
    
    private final LoginConfigRepository loginConfigRepository;

    public RedisHttpSessionConfig(SessionProperties sessionProperties, LoginConfigRepository loginConfigRepository) {
        super();
        this.loginConfigRepository = loginConfigRepository;
        Duration timeout = sessionProperties.getTimeout();
        super.setRedisNamespace("province:session");
        
        LoginConfigEntity entity = loginConfigRepository.findAll().get(0);
        if(ObjectUtils.isEmpty(entity)){
            super.setMaxInactiveIntervalInSeconds((int)timeout.getSeconds());
        } else {
            Duration duration = Duration.ofMinutes(entity.getStaticLogoutTime());
            super.setMaxInactiveIntervalInSeconds((int)duration.getSeconds());
        }
    }
}
