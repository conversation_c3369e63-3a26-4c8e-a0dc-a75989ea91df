package cn.teleinfo.idycprovince.server.modules.auth.controller;

import cn.teleinfo.idycprovince.server.modules.auth.controller.dto.SocialUserBindingDto;
import cn.teleinfo.idycprovince.server.modules.auth.controller.dto.SocialUserDto;
import cn.teleinfo.idycprovince.server.modules.auth.controller.dto.SocialUserExistDto;
import cn.teleinfo.idycprovince.server.modules.auth.controller.dto.SocialUserUnBindingDto;
import cn.teleinfo.idycprovince.server.modules.auth.controller.vo.SocialUserVo;
import cn.teleinfo.idycprovince.server.modules.auth.service.auth.SocialUserServiceInterface;
import cn.teleinfo.idycprovince.server.modules.logcallback.RestfulLogCallback;
import cn.teleinfo.summer.log.annotation.OpLog;
import com.abluepoint.summer.mvc.domain.R;
import com.abluepoint.summer.mvc.domain.Result;
import lombok.RequiredArgsConstructor;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.validation.annotation.Validated;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.PutMapping;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;

/**
 * <AUTHOR>
 */
@RestController
@RequestMapping("/api/v1/social/user")
@RequiredArgsConstructor
    public class SocialUserController {

    private final SocialUserServiceInterface socialUserService;
    
    /**
     * 业务中台登录 判断是否绑定
     *
     * @param socialUserExistDto
     * @return
     */
    @PostMapping("/login/exist")
    @OpLog(value = "业务中台登录 判断是否绑定", callback = RestfulLogCallback.class)
    public Result loginExist(@RequestBody SocialUserExistDto socialUserExistDto) {
        return R.ok(socialUserService.loginExist(socialUserExistDto));
    }

    /**
     * 业务中台登录 绑定
     *
     * @param socialUserDto
     * @return
     */
    @PostMapping("/login/binding")
    @OpLog(value = "业务中台登录 绑定", callback = RestfulLogCallback.class)
    public Result loginBinding(@RequestBody SocialUserDto socialUserDto) {
        socialUserService.loginBinding(socialUserDto);
        return R.ok();
    }
    
    /**
     * 账户管理 绑定
     *
     * @param socialUserBindingDto
     * @return
     */
    @PostMapping("/binding")
    @OpLog(value = "账户管理 绑定", callback = RestfulLogCallback.class)
    public Result binding(@RequestBody SocialUserBindingDto socialUserBindingDto) {
        socialUserService.binding(socialUserBindingDto);
        return R.ok();
    }
    
    /**
     * 账户管理 解绑
     *
     * @param socialUserUnBindingDto
     * @return
     */
    @PutMapping("/unbinding")
    @OpLog(value = "账户管理 解绑", callback = RestfulLogCallback.class)
    public Result unbinding(@RequestBody SocialUserUnBindingDto socialUserUnBindingDto) {
        socialUserService.unbinding(socialUserUnBindingDto.getId());
        return R.ok();
    }
    
    /**
     * 账户管理  解绑 详情
     *
     * @param id
     * @return
     */
    @GetMapping("/detail")
    @OpLog(value = "账户管理 解绑 详情", callback = RestfulLogCallback.class)
    public Result<SocialUserVo> detail(Long id) {
        return R.ok(socialUserService.detail(id));
    }

}
