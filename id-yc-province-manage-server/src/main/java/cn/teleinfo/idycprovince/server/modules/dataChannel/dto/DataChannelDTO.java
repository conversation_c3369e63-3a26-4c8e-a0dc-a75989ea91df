package cn.teleinfo.idycprovince.server.modules.dataChannel.dto;

import lombok.Data;

import javax.persistence.Column;

@Data
public class DataChannelDTO {

    private Long id;

    /**
     * 数据通道名称*
     */
    private String dataChannelName;

    /**
     * 对象标识id*
     */
    private Long objectHandleId;

    /**
     * 对象标识类型 1主数据，2非主数据*
     */
    private Integer objectHandleType;

    /**
     * 所属数据服务*
     */
    private Long dataServiceId;

    /**
     * 数据通道id*
     */
    private Long dataChannelId;

    /**
     * 实例数据类型 1基础2数组*
     */
    private Integer dataType;

    /**
     * 数据库id*
     */
    private Long databaseId;

    /**
     * 解析sql*
     */
    private String resolveSql;

    /**
     * 查询sql*
     */
    private String querySql;

    /**
     * 下发状态 1成功2失败*
     */
    private Integer sendStatus;

    /**
     * 企业id*
     */
    private Long entId;

    /**
     * 应用id*
     */
    private Long appId;
    /**
     * 是否共享 1-是 2-否
     */
    private Integer isShare;

}
