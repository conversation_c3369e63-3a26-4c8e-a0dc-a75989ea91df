package cn.teleinfo.idycprovince.server.modules.auth.service.auth.impl;

import cn.teleinfo.idycprovince.common.constant.BusinessConstant;
import cn.teleinfo.idycprovince.common.exception.BusinessCodeMessage;
import cn.teleinfo.idycprovince.infrastructure.db.entity.*;
import cn.teleinfo.idycprovince.infrastructure.db.repository.*;
import cn.teleinfo.idycprovince.infrastructure.db.to.EntRoleInfoTO;
import cn.teleinfo.idycprovince.infrastructure.db.to.ProvinceRoleInfoTO;
import cn.teleinfo.idycprovince.server.enums.RoleTypeEnum;
import cn.teleinfo.idycprovince.server.modules.auth.controller.dto.RoleDto;
import cn.teleinfo.idycprovince.server.modules.auth.controller.vo.RoleVo;
import cn.teleinfo.idycprovince.server.modules.auth.service.auth.RoleServiceInterface;
import cn.teleinfo.idycprovince.server.modules.auth.util.SecurityUtil;
import cn.teleinfo.idycprovince.server.modules.util.Assert;
import com.abluepoint.summer.mvc.domain.PageResult;
import com.abluepoint.summer.mvc.exception.BusinessRuntimeException;
import lombok.RequiredArgsConstructor;
import org.springframework.beans.BeanUtils;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.data.domain.Page;
import org.springframework.data.domain.PageRequest;
import org.springframework.data.domain.Sort;
import org.springframework.data.jpa.domain.Specification;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;
import org.springframework.util.CollectionUtils;

import javax.persistence.criteria.Predicate;
import java.beans.beancontext.BeanContext;
import java.time.LocalDateTime;
import java.util.*;
import java.util.stream.Collectors;

@Service
@RequiredArgsConstructor
public class RoleSerivce implements RoleServiceInterface {

    private final RoleRepository roleRepository;
    private final UserRepository userRepository;

    private final UserRoleRepository userRoleRepository;
    private final RoleAuthRepository roleAuthRepository;
    private final AuthRepository authRepository;

    private final static int UNDELETE = 1;
    // 默认省角色provinceId
    private final static int SYS_ROLE_PROVINCE_ID = -1;
    private final static long PROVINCE_ROLE_ID = 1;
    // 默认企业角色entId
    private final static int SYS_ROLE_ENT_ID = -1;
    private final static long ENT_ROLE_ID = 2;
    private final static long ENT_APP_ROLE_ID = 3;

    private static final String ENT_ROLE_NAME = "企业管理员";
    private static final String PROVINCE_ROLE_NAME = "省级管理员";
    private static final String SUPER_ADMIN_ROLE_NAME = "省级管理员";

    private static final String ENT_APP_ROLE_NAME = "企业应用角色";

    /**
     * 1省级节点 2企业节点
     */
    @Value("${app.system-type}")
    private Integer systemType;

    @Override
    public void checkRoleUndelete(Long roleId) {

        // 判断角色是否是不可变的
        RoleEntity role = roleRepository.findById(roleId)
                .orElseThrow(() -> new BusinessRuntimeException(BusinessCodeMessage.DATA_NOT_EXIST));
        if (Objects.nonNull(role.getUndelete()) && UNDELETE == role.getUndelete()) {
            Assert.throwEx(BusinessCodeMessage.ROLE_IS_UNDELETE);
        }
    }

    @Override
    public RoleEntity getRoleById(Long roleId) {
        return roleRepository.findById(roleId).orElse(null);
    }

    @Override
    public PageResult<RoleVo> getRoleList(RoleDto roleDto, Integer currentPage, Integer pageSize) {
        Long entId = SecurityUtil.getCurrentEntId();
        roleDto.setProvinceId(SecurityUtil.getCurrentProvinceId());
        Long currentUserId = SecurityUtil.getCurrentUserId();
        List<String> roleName = roleRepository.findRoleNameByUserId(currentUserId);

        List<String> userNameList = new ArrayList<>();
        if(systemType.equals(BusinessConstant.PROVINCE)){
            if (roleName.contains(PROVINCE_ROLE_NAME)) {
                userNameList.add(ENT_ROLE_NAME);
                userNameList.add(PROVINCE_ROLE_NAME);
                userNameList.add(ENT_APP_ROLE_NAME);
            }else {
                userNameList.add(ENT_ROLE_NAME);
                userNameList.add(ENT_APP_ROLE_NAME);
            }
        }else if (systemType.equals(BusinessConstant.ENT)) {
            userNameList.add(ENT_ROLE_NAME);
            userNameList.add(ENT_APP_ROLE_NAME);
        }
        if (Objects.isNull(entId) || entId == 0) {
            return getRoleListPro(roleDto, currentUserId, userNameList, currentPage, pageSize);
        }
        roleDto.setEntId(entId);
        return getRoleListEnt(roleDto, currentUserId, userNameList, currentPage, pageSize);
    }

    private PageResult<RoleVo> getRoleListEnt(RoleDto roleDto, Long currentUserId, List<String> userNameList, Integer currentPage, Integer pageSize) {
        Page<EntRoleInfoTO> pageResult = roleRepository.getEntRoleInfo(roleDto.getEntId(), roleDto.getProvinceId()
                , roleDto.getRoleName(), roleDto.getEntName(), currentUserId, userNameList, PageRequest.of(currentPage, pageSize
                        , Sort.by(Sort.Direction.ASC, "sort")));
        Page<RoleVo> finalPage = pageResult.map(source -> {
            RoleVo roleVo = new RoleVo();
            BeanUtils.copyProperties(source, roleVo);
            roleVo.setEntId(source.getEntId());
            roleVo.setEntName(source.getEntName());
            return roleVo;
        });
        PageResult<RoleVo> finalResult = PageResult.of(finalPage);

        // 结果按角色sort排序
        finalResult.setContent(finalResult.getContent().stream().sorted(Comparator.comparingInt(RoleVo::getSort)).collect(Collectors.toList()));
        return finalResult;
    }

    private PageResult<RoleVo> getRoleListPro(RoleDto roleDto, Long currentUserId, List<String> userNameList, Integer currentPage, Integer pageSize) {
        Page<ProvinceRoleInfoTO> pageResult = roleRepository.getProvinceRoleInfo(roleDto.getProvinceId()
                , roleDto.getRoleName(), currentUserId, userNameList, PageRequest.of(currentPage, pageSize, Sort.by(Sort.Direction.ASC, "sort")));
        Page<RoleVo> finalPage = pageResult.map(source -> {
            RoleVo roleVo = new RoleVo();
            BeanUtils.copyProperties(source, roleVo);
            return roleVo;
        });
        PageResult<RoleVo> finalResult = PageResult.of(finalPage);

        // 结果按角色sort排序
        finalResult.setContent(finalResult.getContent().stream().sorted(Comparator.comparingInt(RoleVo::getSort)).collect(Collectors.toList()));
        return finalResult;
    }

    public List<RoleVo> getRoleListByRoleType(RoleDto roleDto, Long currentUserId, List<String> userNameList) {

        List<ProvinceRoleInfoTO> pageResult = roleRepository.getProvinceRoleByRoleType(roleDto.getProvinceId(), roleDto.getEntId(), currentUserId, userNameList);
        List<RoleVo> finalResult = new ArrayList<>();
        pageResult.forEach(source -> {
            RoleVo roleVo = new RoleVo();
            BeanUtils.copyProperties(source, roleVo);
            finalResult.add(roleVo);
        });

        return finalResult;
    }

    @Override
    public void addRole(RoleDto roleDto) {
        Assert.notEquals(SUPER_ADMIN_ROLE_NAME, roleDto.getRoleName(), BusinessCodeMessage.ROLE_NAME_ALREADY_SET);
        Assert.notEquals(PROVINCE_ROLE_NAME, roleDto.getRoleName(), BusinessCodeMessage.ROLE_NAME_ALREADY_SET);
        Assert.notEquals(ENT_ROLE_NAME, roleDto.getRoleName(), BusinessCodeMessage.ROLE_NAME_ALREADY_SET);
        Assert.notEquals(ENT_APP_ROLE_NAME, roleDto.getRoleName(), BusinessCodeMessage.ROLE_NAME_ALREADY_SET);

        Long entId = SecurityUtil.getCurrentEntId();
        Long provinceId = SecurityUtil.getCurrentProvinceId();
        roleDto.setProvinceId(provinceId);
        roleDto.setEntId(entId);

        Assert.assertFalse(roleRepository.existsByRoleCodeAndProvinceIdAndEntId(roleDto.getRoleCode(), provinceId, entId)
                , BusinessCodeMessage.ROLE_CODE_IS_EXIST);
        if (Objects.isNull(entId) || entId == 0) {
            List<EntRoleInfoTO> proInfoList = roleRepository.findRoleProvinceType(roleDto.getRoleName(), provinceId);
            Assert.assertTrue(CollectionUtils.isEmpty(proInfoList), BusinessCodeMessage.ROLE_NAME_IS_EXIST);
        } else {
            List<EntRoleInfoTO> entInfoList = roleRepository.findRoleEntType(roleDto.getRoleName(), entId);
            if (Objects.nonNull(entInfoList)) {
                Assert.assertTrue(CollectionUtils.isEmpty(entInfoList), BusinessCodeMessage.ROLE_NAME_IS_EXIST);
            }
        }

        roleDto.setCreatedBy(SecurityUtil.getCurrentUserId());
        roleDto.setUpdatedBy(SecurityUtil.getCurrentUserId());
        RoleEntity roleEntity = new RoleEntity();
        BeanUtils.copyProperties(roleDto, roleEntity);
        RoleTypeEnum roleTypeEnum = null!=entId?RoleTypeEnum.ROLE_TYPE_ENUM_ENT:RoleTypeEnum.ROLE_TYPE_ENUM_PROVINCE;
        roleEntity.setRoleType(roleTypeEnum.ordinal());

        roleRepository.save(roleEntity);
    }

    @Override
    public Set<Integer> getRoleSort() {
        Long provinceId = SecurityUtil.getCurrentProvinceId();
        Long entId = SecurityUtil.getCurrentEntId();
        Specification<RoleEntity> querySp = (root, query, cb) -> {
            List<Predicate> predicates = new ArrayList<>();
            Predicate[] p = new Predicate[2];
            // 当前租户下的数据
            if (null != entId && 0 != entId) {
                p[0] = cb.equal(root.get("entId").as(Long.class), entId);
                p[1] = cb.equal(root.get("entId").as(Long.class), SYS_ROLE_ENT_ID);
            } else {
                p[0] = cb.equal(root.get("provinceId").as(Long.class), provinceId);
                p[1] = cb.equal(root.get("provinceId").as(Long.class), SYS_ROLE_PROVINCE_ID);
            }
            predicates.add(cb.or(p));
            query.where(cb.and(predicates.toArray(new Predicate[predicates.size()])));
            return query.getRestriction();
        };

        List<RoleEntity> roleEntity = roleRepository.findAll(querySp);
        return roleEntity.stream().map(RoleEntity::getSort).collect(Collectors.toSet());
    }

    @Override
    public RoleVo getRoleDetail(Long id) {
        RoleEntity entity = roleRepository.findById(id)
                .orElseThrow(() -> new BusinessRuntimeException(BusinessCodeMessage.ROLE_NOT_EXIST));
        RoleVo roleVo = new RoleVo();
        BeanUtils.copyProperties(entity, roleVo);
        // 父类字段单独赋值
        roleVo.setCreatedBy(entity.getCreatedBy());
        roleVo.setUpdatedBy(entity.getUpdatedBy());
        // 获取角色其他信息
        userRepository.findById(roleVo.getCreatedBy()).ifPresent(index -> roleVo.setCreatorName(index.getUsername()));
        userRepository.findById(roleVo.getUpdatedBy()).ifPresent(index -> roleVo.setReviserName(index.getUsername()));
        return roleVo;
    }

    @Override
    public RoleEntity getSuperAdminRole(Long provinceId) {
        return roleRepository.findByProvinceIdAndUndeleteAndRoleType(provinceId, 1, BusinessConstant.PROVINCE_ROLE);
    }

    @Override
    public List<RoleVo> getAllRole(Integer type) {
        Long currentUserId = SecurityUtil.getCurrentUserId();
        List<String> roleName = roleRepository.findRoleNameByUserId(currentUserId);
        //-1为公共角色
        RoleDto roleDto = new RoleDto();
        roleDto.setProvinceId(SecurityUtil.getCurrentProvinceId());
        Long entId = SecurityUtil.getCurrentEntId();
        if (null!=entId) {
            roleDto.setEntId(entId);
        }

        List<String> userNameList = new ArrayList<>();
        if(systemType.equals(BusinessConstant.PROVINCE)){
            if (roleName.contains(PROVINCE_ROLE_NAME)) {
                userNameList.add(ENT_ROLE_NAME);
                userNameList.add(PROVINCE_ROLE_NAME);
                userNameList.add(ENT_APP_ROLE_NAME);
            }else {
                userNameList.add(ENT_ROLE_NAME);
                userNameList.add(ENT_APP_ROLE_NAME);
            }
        }else if (systemType.equals(BusinessConstant.ENT)) {
            userNameList.add(ENT_ROLE_NAME);
            userNameList.add(ENT_APP_ROLE_NAME);
        }

        List<RoleVo> roleVos = getRoleListByRoleType(roleDto, currentUserId, userNameList);

//        if (type == 1 ){
//
//            Long userId = SecurityUtil.getCurrentUserId();
//            //判断当前用户角色
//            Integer roleType = userRoleRepository.selectRoleTypeByUserId(userId,SecurityUtil.getCurrentProvinceId());
//            if (BusinessConstant.PROVINCE_ROLE.equals(roleType)) {
//                roleVos = roleVos.stream().filter(s -> s.getRoleType() != BusinessConstant.APP_ROLE).collect(Collectors.toList());
//            }
//        }

        return roleVos;

    }

    @Override
    public void modifyRole(RoleDto roleDto) {
        this.checkRoleUndelete(roleDto.getId());

        Long entId = SecurityUtil.getCurrentEntId();
        Long provinceId = SecurityUtil.getCurrentProvinceId();
        RoleEntity role = roleRepository.findById(roleDto.getId()).orElse(null);
        if (Objects.isNull(entId) || entId == 0) {
            List<EntRoleInfoTO> proInfoList = roleRepository.findRoleProvinceType(roleDto.getRoleName(), provinceId);
            //判断角色名称是否修改

            if(!roleDto.getRoleName().equals(role.getRoleName())){
                if (Objects.nonNull(proInfoList)) {
                    Assert.assertTrue(CollectionUtils.isEmpty(proInfoList), BusinessCodeMessage.ROLE_NAME_IS_EXIST);
                }

            }
        } else {
            List<EntRoleInfoTO> entInfoList = roleRepository.findRoleEntType(roleDto.getRoleName(), entId);
            if(!roleDto.getRoleName().equals(role.getRoleName())){
                if (Objects.nonNull(entInfoList)) {
                    Assert.assertTrue(CollectionUtils.isEmpty(entInfoList), BusinessCodeMessage.ROLE_NAME_IS_EXIST);
                }
            }
        }

        RoleEntity entity = roleRepository.findById(roleDto.getId())
                .orElseThrow(() -> new BusinessRuntimeException(BusinessCodeMessage.DATA_NOT_EXIST));
        entity.setUpdatedBy(SecurityUtil.getCurrentUserId());
        entity.setRoleName(roleDto.getRoleName());
        entity.setSort(roleDto.getSort());
        roleRepository.save(entity);
    }

    @Override
    @Transactional
    public void removeRole(Long id) {
        List<UserRoleEntity> urEntities = userRoleRepository.findUserRoleEntitiesByRoleId(id);
        if (!CollectionUtils.isEmpty(urEntities)) {
            Assert.throwEx(BusinessCodeMessage.ROLE_HAS_USERS);
        }
        this.checkRoleUndelete(id);

        roleAuthRepository.deleteByRoleId(id);
        userRoleRepository.deleteByRoleId(id);
        roleRepository.deleteById(id);
    }
    @Override
    public void presetProvinceRole(Long provinceId) {

        ArrayList<Integer> roleTypeEnumList = new ArrayList<>();
        roleTypeEnumList.add(RoleTypeEnum.ROLE_TYPE_ENUM_PROVINCE.ordinal());
        roleTypeEnumList.add(RoleTypeEnum.ROLE_TYPE_ENUM_ENT.ordinal());
        roleTypeEnumList.add(RoleTypeEnum.ROLE_TYPE_ENUM_APP.ordinal());

        List<RoleEntity> oriRoleList;
        oriRoleList = roleRepository.findAllByProvinceIdAndUndeleteAndRoleTypeIn(0L, 1, roleTypeEnumList);

        List<RoleAuthEntity> toSaveRoleAuthList = new ArrayList<>();
        oriRoleList.forEach(e-> {
            Long roleId = e.getId();
            RoleEntity preRoleEntity = new RoleEntity();
            preRoleEntity.setRoleCode(e.getRoleCode());
            preRoleEntity.setRoleName(e.getRoleName());
            preRoleEntity.setRoleDesc(e.getRoleDesc());
            preRoleEntity.setRoleType(e.getRoleType());
            preRoleEntity.setUndelete(e.getUndelete());
            preRoleEntity.setSort(e.getSort());
            preRoleEntity.setProvinceId(provinceId);
            preRoleEntity.setEntId(e.getEntId());
            roleRepository.save(preRoleEntity);

            Long preRoleId = preRoleEntity.getId();

            List<RoleAuthEntity> allByRoleId = roleAuthRepository.findAllByRoleId(roleId);

            allByRoleId.forEach(aRoleAuth -> {
                RoleAuthEntity newRoleAuth = new RoleAuthEntity();
                newRoleAuth.setRoleId(preRoleId);
                newRoleAuth.setAuthId(aRoleAuth.getAuthId());
                toSaveRoleAuthList.add(newRoleAuth);
            });

        });
        roleAuthRepository.saveAll(toSaveRoleAuthList);
    }

    @Override
    public RoleEntity getRoleByProvinceIdAndRoleTypeEnum(Long provinceId, RoleTypeEnum roleTypeEnum) {

        return roleRepository.findRoleEntityByProvinceIdAndRoleTypeAndUndelete(provinceId, roleTypeEnum.ordinal(), 1);
    }

    @Override
    public List<RoleVo> getRoleListWhenAddUser() {

        //-1为公共角色
        RoleDto roleDto = new RoleDto();
        roleDto.setProvinceId(SecurityUtil.getCurrentProvinceId());
        Long entId = SecurityUtil.getCurrentEntId();
        if (null!=entId) {
            roleDto.setEntId(entId);
        }
        List<ProvinceRoleInfoTO> pageResult = roleRepository.getRoleListWhenAddUserByProvinceIdAndEntId(roleDto.getProvinceId(), roleDto.getEntId());
        List<RoleVo> finalResult = new ArrayList<>();
        pageResult.forEach(source -> {
            RoleVo roleVo = new RoleVo();
            BeanUtils.copyProperties(source, roleVo);
            finalResult.add(roleVo);
        });

        return finalResult;
    }

    @Override
    @Transactional
    public void removeUserRole(Long userId) {
        userRoleRepository.deleteByUserId(userId);
    }

    @Override
    @Transactional
    public void modifyRoleAuth(Long roleId, List<Long> idList) {
        this.checkRoleUndelete(roleId);

        List<AuthEntity> entities = authRepository.findAllById(idList);
        if (CollectionUtils.isEmpty(entities)) {
            Assert.throwEx(BusinessCodeMessage.AUTH_NOT_EXIST);
        }
        Set<Long> authIds = entities.stream().map(AuthEntity::getId).collect(Collectors.toSet());
        // 入角色表
        List<RoleAuthEntity> resultList = new ArrayList<>();
        Long userId = SecurityUtil.getCurrentUserId();
        final Long finalRoleId = roleId;
        authIds.forEach(index -> resultList.add(new RoleAuthEntity(finalRoleId, index, LocalDateTime.now()
                , userId, LocalDateTime.now(), userId)));
        roleAuthRepository.saveAll(resultList);
    }


}
