package cn.teleinfo.idycprovince.server.modules.auth.controller.dto;

import com.anji.captcha.model.vo.CaptchaVO;
import lombok.Data;

import javax.validation.constraints.Email;
import javax.validation.constraints.NotBlank;
import javax.validation.constraints.NotNull;
import javax.validation.constraints.Pattern;


@Data
public class ForgetDto {

    /**
     * 验证码
     */
    @NotBlank
    private String code;
    /**
     * 邮箱
     */
    @NotBlank
    @Email
    private String email;
    /**
     * 新密码
     */
    @NotBlank
    private String newPassword;
    /**
     * 重复密码
     */
    @NotBlank
    private String rePassword;
    @NotBlank
    private Long provinceId;
}
