package cn.teleinfo.idycprovince.server.config;

import cn.teleinfo.idpointer.sdk.client.GlobalIdClientFactory;
import cn.teleinfo.idpointer.sdk.client.IDClientFactory;
import cn.teleinfo.idpointer.sdk.config.IDClientConfig;
import cn.teleinfo.idycprovince.common.manager.S3FileManager;
import cn.teleinfo.idycprovince.common.manager.TobaccoS3FileManager;
import cn.teleinfo.idycprovince.server.modules.idclient.RecursionProperties;
import cn.tobacco.tcaf.api.file.AFileService;
import com.abluepoint.summer.common.util.AppContextUtil;
import lombok.RequiredArgsConstructor;
import okhttp3.OkHttpClient;
import org.springframework.boot.autoconfigure.condition.ConditionalOnProperty;
import org.springframework.boot.context.properties.ConfigurationProperties;
import org.springframework.boot.context.properties.EnableConfigurationProperties;
import org.springframework.cache.annotation.EnableCaching;
import org.springframework.context.annotation.Bean;
import org.springframework.context.annotation.ComponentScan;
import org.springframework.context.annotation.Configuration;
import org.springframework.core.annotation.Order;
import org.springframework.security.crypto.bcrypt.BCryptPasswordEncoder;
import org.springframework.security.crypto.password.PasswordEncoder;

import java.util.concurrent.TimeUnit;

@Configuration
@ComponentScan(basePackages = {"cn.teleinfo.idycprovince"})
@EnableConfigurationProperties({AppConfig.AppProperties.class, MinioProperties.class})
@EnableCaching
@RequiredArgsConstructor
public class AppConfig {

    private final RecursionProperties recursionProperties;

    @ConfigurationProperties(prefix = "app")
    public static class AppProperties {
        private String fileManager;

        public String getFileManager() {
            return fileManager;
        }

        public void setFileManager(String fileManager) {
            this.fileManager = fileManager;
        }
    }

    @Bean
    public PasswordEncoder passwordEncoder() {
        return new BCryptPasswordEncoder();
    }

    @Bean
    public AppContextUtil.ContextHolder applicationContextHolder() {
        return new AppContextUtil.ContextHolder();
    }

    @Order(Integer.MIN_VALUE)
    @Bean
    public IDClientFactory idClientFactory() {
        IDClientConfig idClientConfig = IDClientConfig.builder()
                .recursionServerIp(recursionProperties.getRecursionIp())
                .recursionServerPort(recursionProperties.getRecursionPort())
                .build();
        GlobalIdClientFactory.init(idClientConfig);
        return GlobalIdClientFactory.getIdClientFactory();
    }

    @Bean
    public OkHttpClient okHttpClient() {
        return new OkHttpClient.Builder()
//                .retryOnConnectionFailure(false)
                .connectTimeout(30, TimeUnit.SECONDS)
//                .writeTimeout(10, TimeUnit.SECONDS)
                .readTimeout(30, TimeUnit.SECONDS)
                .build();
    }

    @ConditionalOnProperty(prefix = "app", name = "file-manager", havingValue = "tbc")
    @Bean
    public S3FileManager configS3Manager(AFileService aFileService, MinioProperties minioProperties) {
        return new TobaccoS3FileManager(aFileService, minioProperties.getRootPath());
    }

}
