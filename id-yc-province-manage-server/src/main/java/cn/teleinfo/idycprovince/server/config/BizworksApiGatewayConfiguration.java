package cn.teleinfo.idycprovince.server.config;

import lombok.extern.slf4j.Slf4j;
import org.springframework.boot.autoconfigure.condition.ConditionalOnExpression;
import org.springframework.context.EnvironmentAware;
import org.springframework.context.annotation.ComponentScan;
import org.springframework.context.annotation.Configuration;
import org.springframework.core.env.Environment;

/**
 * 阿里用户中台接入网关配置
 *
 * <AUTHOR>
 * @date 2023/3/28 14:12
 */
@Slf4j
@Configuration
@ComponentScan("com.bizworks.ubcp.apiclient.feign")
@ConditionalOnExpression("'ali'.equals('${bizworks-api-gateway.type}') and 'true'.equals('${bizworks-api-gateway.enable}') ")
public class BizworksApiGatewayConfiguration implements EnvironmentAware {
    @Override
    public void setEnvironment(Environment environment) {
        log.info("开启阿里网关模式");
    }
}
