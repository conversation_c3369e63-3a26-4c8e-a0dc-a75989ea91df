package cn.teleinfo.idycprovince.server.enums;

public enum PrefixPrepareClaimStatusEnum {

    // 0-待认领；1-已认领；2-已驳回
    INIT(0, "待认领"),
    CONFIRMED(1, "已认领"),
    REJECT(2, "已驳回"),
    ;

    private Integer code;
    private String desc;

    PrefixPrepareClaimStatusEnum(Integer code, String desc) {
        this.code = code;
        this.desc = desc;
    }

    public int getCode() {
        return this.code;
    }

    public String getDesc() {
        return this.desc;
    }
}
