package cn.teleinfo.idycprovince.server.modules.app.controller;

import cn.hutool.core.lang.tree.Tree;
import cn.teleinfo.idycprovince.server.modules.app.dto.AppInfoDTO;
import cn.teleinfo.idycprovince.server.modules.app.dto.AppInfoKeyDTO;
import cn.teleinfo.idycprovince.server.modules.app.service.AppInfoService;
import cn.teleinfo.idycprovince.server.modules.app.vo.AppInfoVO;
import cn.teleinfo.idycprovince.server.modules.app.vo.EntPrefixListVO;
import cn.teleinfo.idycprovince.server.modules.app.vo.KeyPairVO;
import cn.teleinfo.idycprovince.server.modules.logcallback.RestfulLogCallback;
import cn.teleinfo.idycprovince.server.modules.system.vo.EntPrefixVO;
import cn.teleinfo.summer.log.annotation.OpLog;
import com.abluepoint.summer.mvc.domain.PageResult;
import com.abluepoint.summer.mvc.domain.R;
import com.abluepoint.summer.mvc.domain.Result;
import com.fasterxml.jackson.core.JsonProcessingException;
import lombok.RequiredArgsConstructor;
import org.springframework.data.domain.Pageable;
import org.springframework.data.domain.Sort;
import org.springframework.data.web.PageableDefault;
import org.springframework.security.access.prepost.PreAuthorize;
import org.springframework.web.bind.annotation.*;

import java.util.List;


@RestController
@RequestMapping("/api/v1/appInfo")
@RequiredArgsConstructor
public class AppInfoController {

    private final AppInfoService appInfoService;

    /**
     * 应用管理--分页列表
     * @param appName
     * @param pageable
     * @return
     */
    @GetMapping("/pageAppInfo")
    @PreAuthorize("hasAuthority('SYS:APP:PAGE')")
    public Result pageAppInfo(String appName,Integer appType,@PageableDefault(sort = "updatedTime", direction = Sort.Direction.DESC) Pageable pageable){
        PageResult<AppInfoVO> pageResult =  appInfoService.pageAppInfo(appName,appType,pageable);
        return R.ok(pageResult);
    }

    @GetMapping("/mp-dmm-app/tree")
    @PreAuthorize("hasAuthority('SYS:APP:PAGE')")
    public Result dmmAppList(){
        List<Tree<String>> tree = appInfoService.dmmAppTree();
        return R.ok(tree);
    }

    /**
     * 应用管理--新建应用
     * @return
     */
    @PostMapping("/createAppInfo")
    @OpLog(value = "新增应用", callback = RestfulLogCallback.class)
    @PreAuthorize("hasAuthority('SYS:APP:ADD')")
    public Result createAppInfo(@RequestBody AppInfoDTO appInfoDTO) throws Exception {

        return R.ok(appInfoService.createAppInfo(appInfoDTO));
    }

    /**
     * 应用管理--编辑应用
     * @return
     */
    @PostMapping("/updateAppInfo")
    @PreAuthorize("hasAuthority('SYS:APP:EDIT')")
    public Result updateAppInfo(@RequestBody AppInfoDTO appInfoDTO) throws JsonProcessingException {
        appInfoService.updateAppInfo(appInfoDTO);
        return R.ok();
    }

    /**
     * 应用管理--开启关闭审核
     * @return
     */
    @PostMapping("/audit")
    @PreAuthorize("hasAuthority('SYS:APP:EDIT')")
    public Result audit(@RequestBody AppInfoDTO appInfoDTO) throws JsonProcessingException {
        appInfoService.audit(appInfoDTO.getId());
        return R.ok();
    }

    /**
     * 应用管理--查看详情
     * @param id
     * @return
     */
    @GetMapping("/detail")
    @PreAuthorize("hasAuthority('SYS:APP:DETAIL')")
    public Result detail(@RequestParam Long id){
        AppInfoVO appInfoVO = appInfoService.detail(id);
        return R.ok(appInfoVO);
    }

    /**
     * 应用管理--删除应用
     * @param id
     * @return
     */
    @DeleteMapping("/removeAppInfo")
    @PreAuthorize("hasAuthority('SYS:APP:DELETE')")
    public Result removeAppInfo(@RequestParam Long id) throws JsonProcessingException {
       appInfoService.removeAppInfo(id);
       return R.ok();
    }

    @GetMapping("/appList")
    @PreAuthorize("hasAuthority('SYS:APP:LIST')")
    public Result appList(String appName){
        List<AppInfoVO> appInfoVOS = appInfoService.getAppList(appName);
        return R.ok(appInfoVOS);
    }

    /**
     * 企业前缀下拉框
     * @param prefix
     * @return
     */
    @GetMapping("/queryPrefixList")
    @PreAuthorize("hasAuthority('SYS:APP:PREFIXLIST')")
    public Result queryPrefixList(String prefix){
        List<EntPrefixVO> entPrefixVOS = appInfoService.queryPrefixList(prefix);
        return R.ok(entPrefixVOS);
    }

    @GetMapping("/list")
    public Result list(Long entId){
        List<AppInfoVO> appInfoVOS = appInfoService.list(entId);
        return R.ok(appInfoVOS);
    }


    @GetMapping("/entPrefixList")
    public Result getEntPrefixList(Long entId){
        List<EntPrefixListVO> entPrefixList = appInfoService.getEntPrefixList(entId);
        return R.ok(entPrefixList);
    }

    @GetMapping("/createKeyPair")
    public Result createKeyPair() {
        KeyPairVO keyPair = appInfoService.createKeyPair();
        return R.ok(keyPair);
    }

    @PostMapping("/securityCertificate")
    public Result securityCertificate(@RequestBody AppInfoKeyDTO appInfoKeyDTO){
        appInfoService.securityCertificate(appInfoKeyDTO);
        return R.ok();
    }
}
