package cn.teleinfo.idycprovince.server.config;

import cn.teleinfo.idycprovince.server.Interceptor.AppRequestLoggingInterceptor;
import cn.teleinfo.idycprovince.server.exception.AppHandlerExceptionResolver;
import cn.teleinfo.summer.log.aspect.OpLogAspect;
import cn.teleinfo.summer.log.resolver.WebContext;
import com.abluepoint.summer.mvc.context.ContextArgumentResolver;
import com.abluepoint.summer.mvc.converter.StringToDateConverter;
import com.abluepoint.summer.mvc.filter.CachingSupportWrapFilter;
import com.abluepoint.summer.mvc.interceptor.*;
import com.abluepoint.summer.mvc.manager.HandlerMappingsManager;
import com.abluepoint.summer.mvc.view.ResourceViewFactory;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.boot.context.properties.ConfigurationProperties;
import org.springframework.boot.context.properties.EnableConfigurationProperties;
import org.springframework.boot.web.servlet.FilterRegistrationBean;
import org.springframework.context.ApplicationContext;
import org.springframework.context.annotation.Bean;
import org.springframework.context.annotation.Configuration;
import org.springframework.context.annotation.Lazy;
import org.springframework.core.Ordered;
import org.springframework.format.FormatterRegistry;
import org.springframework.security.core.session.SessionRegistry;
import org.springframework.security.core.session.SessionRegistryImpl;
import org.springframework.web.method.support.HandlerMethodArgumentResolver;
import org.springframework.web.servlet.DispatcherServlet;
import org.springframework.web.servlet.HandlerExceptionResolver;
import org.springframework.web.servlet.HandlerInterceptor;
import org.springframework.web.servlet.config.annotation.InterceptorRegistration;
import org.springframework.web.servlet.config.annotation.InterceptorRegistry;
import org.springframework.web.servlet.config.annotation.ResourceHandlerRegistry;
import org.springframework.web.servlet.config.annotation.WebMvcConfigurer;

import javax.servlet.DispatcherType;
import java.util.List;

@Configuration
@EnableConfigurationProperties({AppMvcConfig.AppMvcProperties.class})
public class AppMvcConfig implements WebMvcConfigurer {

    private final ApplicationContext applicationContext;
    private final AppMvcProperties appMvcProperties;

    @Autowired
    public AppMvcConfig(ApplicationContext applicationContext, AppMvcProperties appMvcProperties) {
        this.applicationContext = applicationContext;
        this.appMvcProperties = appMvcProperties;
    }

    @Bean
    public FilterRegistrationBean<CachingSupportWrapFilter> filterRegistrationBean() {
        FilterRegistrationBean<CachingSupportWrapFilter> filterRegistrationBean = new FilterRegistrationBean<>(new CachingSupportWrapFilter());
        filterRegistrationBean.setOrder(Ordered.HIGHEST_PRECEDENCE);
        filterRegistrationBean.addInitParameter("exclusions", "*/resource/upload,*.js,*.gif,*.jpg,*.png,*.css,*.ico");
        filterRegistrationBean.addUrlPatterns("/*");
        filterRegistrationBean.setDispatcherTypes(DispatcherType.REQUEST);
        return filterRegistrationBean;
    }

    @Override
    public void addResourceHandlers(ResourceHandlerRegistry registry) {
        String staticPath = appMvcProperties.getStaticPath();
        if (staticPath != null) {
            StringBuilder staticPathBuilder = new StringBuilder(staticPath);
            if (!staticPath.endsWith("/")) {
                staticPathBuilder.append("/");
            }
            String pagePath = appMvcProperties.getPagePath();
            registry.setOrder(Ordered.LOWEST_PRECEDENCE).addResourceHandler(pagePath + "/**").addResourceLocations(staticPathBuilder.toString());
        }
    }

    @Override
    public void addInterceptors(InterceptorRegistry registry) {
        AppRequestLoggingInterceptor loggingInterceptor = new AppRequestLoggingInterceptor(new RequestLoggingSupport());
//        CommonRequestLoggingInterceptor loggingInterceptor = new CommonRequestLoggingInterceptor(new RequestLoggingSupport());
        registry.addInterceptor(loggingInterceptor).order(Integer.MIN_VALUE).addPathPatterns("/**");
        registry.addInterceptor(groupEndpointInterceptor()).order(Integer.MIN_VALUE+1).addPathPatterns("/**");

        String staticPath = appMvcProperties.getStaticPath();
        String pagePath = appMvcProperties.getPagePath();
        if (staticPath != null && pagePath != null) {
            StringBuilder indexPath = new StringBuilder(staticPath);
            if (!staticPath.endsWith("/")) {
                indexPath.append("/");
            }
            indexPath.append("index.html");

            IndexInterceptorFactory indexInterceptorFactory = new IndexInterceptorFactory(applicationContext, resourceViewFactory());
            HandlerInterceptor interceptor = indexInterceptorFactory.getIndexInterceptor(indexPath.toString(), System.currentTimeMillis());

            InterceptorRegistration registration = registry.addInterceptor(interceptor);
            registration.addPathPatterns(pagePath + "/**");
            registration.excludePathPatterns(pagePath + "/static/**");
            registration.excludePathPatterns(pagePath + "/loading/**");
            registration.excludePathPatterns(pagePath + "/assets/**");
            registration.excludePathPatterns(pagePath + "/avatar2.jpg");
            registration.excludePathPatterns(pagePath + "/logo.png");
        }
    }

    @Bean
    public ResourceViewFactory resourceViewFactory() {
        return new ResourceViewFactory();
    }

    @Bean
    public GroupEndpointInterceptor groupEndpointInterceptor() {
        return new GroupEndpointInterceptor(interceptorGroupManager());
    }

    @Bean
    public InterceptorGroupManager interceptorGroupManager() {
        return new InterceptorGroupManager();
    }

    @Bean
    @Lazy
    public HandlerMappingsManager handlerMappingsManager(DispatcherServlet dispatcherServlet) {
        return new HandlerMappingsManager(dispatcherServlet);
    }

    @Bean
    public HandlerExceptionResolver appHandlerExceptionResolver() {
        RequestLoggingSupport requestLoggingSupport = new RequestLoggingSupport();
        return new AppHandlerExceptionResolver(requestLoggingSupport);
    }

    @Override
    public void addFormatters(FormatterRegistry registry) {
        registry.addConverter(new StringToDateConverter());
    }

    @Override
    public void configureHandlerExceptionResolvers(List<HandlerExceptionResolver> resolvers) {
        resolvers.add(appHandlerExceptionResolver());
    }

    @Override
    public void addArgumentResolvers(List<HandlerMethodArgumentResolver> resolvers) {
        resolvers.add(new ContextArgumentResolver());
    }

    @ConfigurationProperties(prefix = "app.mvc")
    public static class AppMvcProperties {
        private String staticPath;
        private String pagePath;

        public String getStaticPath() {
            return staticPath;
        }

        public void setStaticPath(String staticPath) {
            this.staticPath = staticPath;
        }

        public String getPagePath() {
            return pagePath;
        }

        public void setPagePath(String pagePath) {
            this.pagePath = pagePath;
        }
    }

    @Bean
    public SessionRegistry sessionRegistry(){
        SessionRegistryImpl sessionRegistry = new SessionRegistryImpl();
        return sessionRegistry;
    }

    @Bean
    public WebContext webContext() {
        return new WebContext();
    }

    @Bean
    public OpLogAspect opLogAspect() {
        return new OpLogAspect();
    }

}
