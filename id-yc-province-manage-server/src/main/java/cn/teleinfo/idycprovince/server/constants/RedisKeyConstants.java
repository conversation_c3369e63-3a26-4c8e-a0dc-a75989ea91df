package cn.teleinfo.idycprovince.server.constants;

/**
 * MQ BUSINESS_CODE定义：
 */
public interface RedisKeyConstants {

    //---------------------------------MQ---------------------------------

    /**
     * businessId是否消费过
     */
    String MQ_CONSUMED_BUSINESS_ID_PREFIX = "MQ_CONSUMED_BUSINESS_ID:";

    /**
     * businessId是否发出过
     */
    String MQ_SEND_BUSINESS_ID_PREFIX = "MQ_SEND_BUSINESS_ID:";
    /**
     * 是否管理该前缀缓存
     */
    String CACHE_PREFIX_EXIST_PREFIX = "CACHE_PREFIX_EXIST_PREFIX:";
    /**
     * 忘记密码邮件缓存
     */
    String REDIS_KEY_FORGET_PASSWORD = "CACHE_PREFIX_EXIST:";
    /**
     * 忘记密码邮件发送时间限制key
     */
    String REDIS_KEY_FORGET_PASSWORD_SENT = "REDIS_KEY_FORGET_PASSWORD_SENT:";
    /**
     * 用户输入错误次数key
     */
    String REDIS_KEY_FORGET_PASSWORD_CHECK_ERROR_TIMES = "REDIS_KEY_FORGET_PASSWORD_CHECK_ERROR_TIMES:";
    /**
     * 用户锁定key，时长xmin
     */
    String REDIS_KEY_USER_CHECK_ERROR_LOCK = "REDIS_KEY_USER_CHECK_ERROR_LOCK:";

}
