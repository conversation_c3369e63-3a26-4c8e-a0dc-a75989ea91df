package cn.teleinfo.idycprovince.server.modules.auth.util;

import cn.teleinfo.idycprovince.common.exception.BusinessCodeMessage;
import cn.teleinfo.idycprovince.server.modules.auth.model.HandleUserTO;
import cn.teleinfo.idycprovince.server.modules.auth.model.UserDetail;
import cn.teleinfo.idycprovince.server.modules.security.apiSign.AppInfoDetail;
import cn.teleinfo.idycprovince.server.modules.util.Assert;
import com.abluepoint.summer.mvc.exception.BusinessRuntimeException;
import lombok.extern.slf4j.Slf4j;
import org.springframework.security.core.Authentication;
import org.springframework.security.core.context.SecurityContextHolder;

import java.util.List;
import java.util.Objects;

/**
 * 获取当前登录的用户
 *
 * <AUTHOR>
 * @date 2022-07-04
 */
@Slf4j
public class SecurityUtil {

    /**
     * 获取Authentication
     */
    private static Authentication getAuthentication() {
        return SecurityContextHolder.getContext().getAuthentication();
    }

    /**
     * 当前用户
     **/
    public static UserDetail getCurrentUser() {
        try {
            Assert.notNull(getAuthentication().getPrincipal(), BusinessCodeMessage.DATA_NOT_EXIST);
            return (UserDetail) getAuthentication().getPrincipal();
        } catch (Exception e) {
            log.info("用户信息获取失败");
            throw new BusinessRuntimeException(com.abluepoint.summer.common.exception.CodeMessageEnum.UNAUTHORIZED,e);
        }
    }

    /**
     * 当前用户ID
     * fixme: 空指针
     *
     * @return 当前用户ID
     *
     */
    public static Long getCurrentUserId() {
        return getCurrentUser().getUserId();
    }

    /**
     * 获取当前省级企业租户id
     * fixme: 空指针
     *
     * @return 当前用户租户id
     *
     */
    public static Long getCurrentProvinceId() {
        Long provinceId = TenantContextHolder.getTenantId();
        if (provinceId != null) {
            return provinceId;
        }
        Object principal = SecurityContextHolder.getContext().getAuthentication().getPrincipal();
        if (principal instanceof AppInfoDetail) {
            return ((AppInfoDetail) principal).getProvinceId();
        }
        if (principal instanceof UserDetail) {
            return ((UserDetail) principal).getProvinceId();
        }
        return null;
    }

    /**
     * 获取当前用户企业租户id
     * @return
     */
    public static Long getCurrentEntId() {
        Object principal = SecurityContextHolder.getContext().getAuthentication().getPrincipal();
        if (principal instanceof AppInfoDetail) {
            return ((AppInfoDetail) principal).getEntId();
        }
        if (principal instanceof UserDetail) {
            return ((UserDetail) principal).getEntId();
        }
        return null;
    }

    /**
     * 当前用户名称
     *
     * @return 当前用户名称
     */
    public static String getCurrentUsername() {
        return getCurrentUser().getUsername();
    }
    
    /**
     * 当前企业应用角色关联应用id
     *
     * @return 当前企业应用角色关联应用id
     */
    public static Long getCurrentAppId() {
        Object principal = SecurityContextHolder.getContext().getAuthentication().getPrincipal();
        if (principal instanceof AppInfoDetail) {
            return ((AppInfoDetail) principal).getId();
        }
        if (principal instanceof UserDetail) {
            return ((UserDetail) principal).getAppId();
        }
        return null;
    }

    /**
     * 获得当前的标识用户
     *
     * @return 返回当前的标识用户
     */
    public static List<HandleUserTO> getCurrentHandleUser() {
        return getCurrentUser().getHandleUserTOList();
    }
    
    /**
     * 水平越权校验
     *
     * 什么是横向越权/纵向越权？
     * 横向越权：横向越权(水平越权)指的是攻击者尝试访问与他拥有相同权限的用户的资源。
     * 纵向越权：纵向越权(垂直越权)指的是一个低级别攻击者尝试访问高级别用户的资源。
     *
     * @param endId
     * @return
     */
    public static void checkLevelAuth(Long endId) {
        Long currentEntId = getCurrentEntId();
        if (!Objects.equals(currentEntId, endId)){
            log.info("水平越权校验: 当前endId为[{}], 与权限需要的endId[{}]不匹配", currentEntId, endId);
            throw new BusinessRuntimeException(BusinessCodeMessage.USER_NO_PERMISSION);
        }
    }
    
    /**
     * 水平越权校验
     *
     * 什么是横向越权/纵向越权？
     * 横向越权：横向越权(水平越权)指的是攻击者尝试访问与他拥有相同权限的用户的资源。
     * 纵向越权：纵向越权(垂直越权)指的是一个低级别攻击者尝试访问高级别用户的资源。
     *
     * @param appId
     * @return
     */
    public static void checkLevelAuthApp(Long appId) {
        Long currentAppId = getCurrentAppId();
        if (!Objects.equals(currentAppId, appId)){
            log.info("水平越权校验: 当前appId为[{}], 与权限需要的appId[{}]不匹配", currentAppId, appId);
            throw new BusinessRuntimeException(BusinessCodeMessage.USER_NO_PERMISSION);
        }
    }

    public static void checkLevelAuthApp(Long appId,Long currentAppId) {
        if (!Objects.equals(currentAppId, appId)){
            log.info("水平越权校验: 当前appId为[{}], 与权限需要的appId[{}]不匹配", currentAppId, appId);
            throw new BusinessRuntimeException(BusinessCodeMessage.USER_NO_PERMISSION);
        }
    }
}
