package cn.teleinfo.idycprovince.server.modules.auth.service.auth.impl;

import cn.hutool.core.util.ObjectUtil;
import cn.hutool.json.JSONObject;
import cn.hutool.json.JSONUtil;
import cn.teleinfo.idycprovince.common.constant.BusinessConstant;
import cn.teleinfo.idycprovince.common.constant.RcEnum;
import cn.teleinfo.idycprovince.common.exception.BusinessCodeMessage;
import cn.teleinfo.idycprovince.infrastructure.db.entity.ProvinceTenantEntity;
import cn.teleinfo.idycprovince.infrastructure.db.entity.SocialUserEntity;
import cn.teleinfo.idycprovince.infrastructure.db.entity.UserEntity;
import cn.teleinfo.idycprovince.infrastructure.db.entity.UserSocialUserEntity;
import cn.teleinfo.idycprovince.infrastructure.db.repository.SocialUserRepository;
import cn.teleinfo.idycprovince.infrastructure.db.repository.UserRepository;
import cn.teleinfo.idycprovince.infrastructure.db.repository.UserSocialUserRepository;
import cn.teleinfo.idycprovince.server.config.AppSecurityConfig;
import cn.teleinfo.idycprovince.server.config.IpProperties;
import cn.teleinfo.idycprovince.server.modules.auth.controller.dto.SocialUserBindingDto;
import cn.teleinfo.idycprovince.server.modules.auth.controller.dto.SocialUserDto;
import cn.teleinfo.idycprovince.server.modules.auth.controller.dto.SocialUserExistDto;
import cn.teleinfo.idycprovince.server.modules.auth.controller.vo.SocialLoginVo;
import cn.teleinfo.idycprovince.server.modules.auth.controller.vo.SocialUserVo;
import cn.teleinfo.idycprovince.server.modules.auth.service.PasswordService;
import cn.teleinfo.idycprovince.server.modules.auth.service.auth.SocialUserServiceInterface;
import cn.teleinfo.idycprovince.server.modules.provinceTenant.service.ProvinceTenantService;
import cn.teleinfo.idycprovince.server.modules.util.Assert;
import cn.teleinfo.idycprovince.server.modules.util.JsonUtils;
import com.abluepoint.summer.common.exception.CodeMessageEnum;
import com.abluepoint.summer.mvc.exception.BusinessRuntimeException;
import com.alibaba.bizworks.core.runtime.common.MultiResponse;
import com.alibaba.bizworks.core.runtime.common.SingleResponse;
import com.tobacco.mp.uc.client.api.AuthServiceAPI;
import com.tobacco.mp.uc.client.api.EmployeeServiceAPI;
import com.tobacco.mp.uc.client.api.OrgServiceAPI;
import com.tobacco.mp.uc.client.api.UserServiceAPI;
import com.tobacco.mp.uc.client.api.auth.dto.LoginAuthRespDTO;
import com.tobacco.mp.uc.client.api.auth.req.LoginAuthRequestV2;
import com.tobacco.mp.uc.client.api.employee.dto.CreateBizEmployeeDTO;
import com.tobacco.mp.uc.client.api.employee.dto.DeleteEmployeeDTO;
import com.tobacco.mp.uc.client.api.employee.dto.EmployeeInfoDTO;
import com.tobacco.mp.uc.client.api.employee.dto.ListEmployeeByOrgBizCodeAndOrgUnitIdDTO;
import com.tobacco.mp.uc.client.api.employee.req.CreateBizEmployeeRequest;
import com.tobacco.mp.uc.client.api.employee.req.DeleteBizEmployeeRequest;
import com.tobacco.mp.uc.client.api.employee.req.GetEmployeeByUserIdRequest;
import com.tobacco.mp.uc.client.api.employee.req.ListEmployeeByOrgBizCodeAndOrgUnitIdRequest;
import com.tobacco.mp.uc.client.api.org.dto.OrgUnitDTO;
import com.tobacco.mp.uc.client.api.org.req.GetOrgUnitByBizCodeRequest;
import com.tobacco.mp.uc.client.api.user.dto.GetUserDTO;
import com.tobacco.mp.uc.client.api.user.req.GetUserRequest;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import okhttp3.*;
import org.apache.commons.lang3.ObjectUtils;
import org.apache.commons.lang3.StringUtils;
import org.springframework.beans.BeanUtils;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.cloud.context.config.annotation.RefreshScope;
import org.springframework.security.authentication.BadCredentialsException;
import org.springframework.security.crypto.password.PasswordEncoder;
import org.springframework.session.SessionRepository;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import javax.servlet.http.HttpSession;
import java.io.IOException;
import java.util.ArrayList;
import java.util.Arrays;
import java.util.List;

/**
 * <AUTHOR>
 */
@Service
@RequiredArgsConstructor
@Slf4j
@RefreshScope
public class SocialUserService implements SocialUserServiceInterface {
    
    private final PasswordService passwordService;
    private final AppSecurityConfig.AppSecurityProperties appSecurityProperties;
    private final PasswordEncoder passwordEncoder;
    private final UserRepository userRepository;
    private final SocialUserRepository socialUserRepository;
    private final UserSocialUserRepository userSocialUserRepository;
    private final ProvinceTenantService provinceTenantService;

    @Value("${bizworks-api-gateway.orgBizCode}")
    private String orgBizCode;
    @Value("${app.on-off.tenant}")
    private Integer isTenant;

    private final EmployeeServiceAPI employeeServiceAPI;
    private final OrgServiceAPI orgServiceAPI;
    private final AuthServiceAPI authServiceAPI;
    private final UserServiceAPI userServiceAPI;

    private final IpProperties ipProperties;

    private final OkHttpClient httpClient;

    @Value("${app.system-type}")
    private Integer systemType;

    private final String socialUserLogin = "/api/v1/social/user/login/exist";
    
    /**
     * 用户中台登录判断是否绑定
     *
     * @param socialUserExistDto
     * @return 结果
     **/
    @Override
    public SocialLoginVo loginExist(SocialUserExistDto socialUserExistDto) {
        String password = socialUserExistDto.getPassword();
        if (appSecurityProperties.isPasswordEncrypted()) {
            password = decrypt(password);
        }
        
        SocialLoginVo socialLoginVo = new SocialLoginVo();
        String userCenterId = "";

        //如果是企业系统并且是中台账号登录则调用省级中台登录
        if(systemType.equals(BusinessConstant.ENT)){
            socialUserExistDto.setPassword(password);
            userCenterId = login(socialUserExistDto);
            socialLoginVo.setUserCenterId(userCenterId);
        }else {
            //鉴权
            try {
                LoginAuthRequestV2 loginAuthRequestV2 = new LoginAuthRequestV2();
                List<String> userTypes = Arrays.asList("000001","000101","000102");
                loginAuthRequestV2.setUserTypes(userTypes);
                loginAuthRequestV2.setOuterIdentityCode("account");
                //对应外标编码所存的信息
                loginAuthRequestV2.setOuterIdentityValue(socialUserExistDto.getUsername());
                //认证方式(密码:password;其他方式暂时不支持)
                loginAuthRequestV2.setAuthWay(BusinessConstant.USER_CENTER_AUTH_PASSWORD);
                //认证信息
                loginAuthRequestV2.setPassword(password);
                SingleResponse<LoginAuthRespDTO> response = authServiceAPI.validatePasswordV2(loginAuthRequestV2);
                if (!response.getCode().equals(BusinessConstant.USER_CENTER_RESPONSE_CODE_SUCCESS)) {
                    throw new BusinessRuntimeException(BusinessCodeMessage.USER_PASSWORD_ERROR);
                }
                userCenterId = response.getData().getUserId();
            } catch (BusinessRuntimeException businessRuntimeException){
                throw new BusinessRuntimeException(businessRuntimeException);
            }  catch (Exception e){
                log.error(e.getMessage(), e);
                throw new BusinessRuntimeException(BusinessCodeMessage.USER_CENTER_ERROR);
            }
            if(StringUtils.isEmpty(userCenterId)){
                throw new BusinessRuntimeException(BusinessCodeMessage.USER_PASSWORD_ERROR);
            }

            //校验当前用户中台用户是否绑定系统用户
            SocialUserEntity socialUserEntity = socialUserRepository.findByUuid(userCenterId);
            if (ObjectUtil.isNotEmpty(socialUserEntity)){
                socialLoginVo.setBinding(Boolean.TRUE);
            } else {
                socialLoginVo.setBinding(Boolean.FALSE);
            }
            socialLoginVo.setUserCenterId(userCenterId);
        }

        return socialLoginVo;
    }
    
    /**
     * 用户中台登录绑定
     *
     * @param socialUserDto
     * @return 结果
     **/
    @Override
    @Transactional(rollbackFor = Exception.class)
    public void loginBinding(SocialUserDto socialUserDto) {

        //判断当前是否是多租户模式，如果是，provinceId必传
        if (1==isTenant&&null==socialUserDto.getProvinceId()) {
            throw new BusinessRuntimeException(BusinessCodeMessage.PROVINCE_TENANT_NOT_EXIST);
        }

        String password = socialUserDto.getPassword();
        if (appSecurityProperties.isPasswordEncrypted()) {
            password = decrypt(password);
        }
        
        // 校验用户是否存在
        UserEntity userEntity = userRepository.findByUsernameAndProvinceId(socialUserDto.getUsername(), socialUserDto.getProvinceId());
        Assert.notNull(userEntity, BusinessCodeMessage.USER_PASSWORD_ERROR);
    
        //校验用户是否已绑定用户中心用户
        if(ObjectUtils.isNotEmpty(userSocialUserRepository.findByUserId(userEntity.getId()))){
            throw new BusinessRuntimeException(BusinessCodeMessage.USER_EXIST_BINDING);
        }
        
        // 校验用户名密码是否正确
        if(!passwordEncoder.matches(password, userEntity.getPassword())) {
            throw new BusinessRuntimeException(BusinessCodeMessage.USER_PASSWORD_ERROR);
        }
    
        //绑定
        bindingCenter(socialUserDto.getUserCenterId(), userEntity.getId(), socialUserDto.getProvinceId());
    }
    
    /**
     * 绑定
     *
     * @param socialUserBindingDto
     * @return 结果
     **/
    @Override
    @Transactional(rollbackFor = Exception.class)
    public void binding(SocialUserBindingDto socialUserBindingDto) {
        // 校验用户是否存在
        UserEntity userEntity = userRepository.findById(socialUserBindingDto.getId())
                .orElseThrow(() -> new BusinessRuntimeException(BusinessCodeMessage.USER_NOT_EXIST));
        
        //校验用户是否已绑定用户中心用户
        if(ObjectUtils.isNotEmpty(userSocialUserRepository.findByUserId(userEntity.getId()))){
            throw new BusinessRuntimeException(BusinessCodeMessage.USER_EXIST_BINDING);
        }
    
        String password = socialUserBindingDto.getPassword();
//        if (appSecurityProperties.isPasswordEncrypted()) {
//            password = decrypt(password);
//        }
        
        String userCenterId = "";
        //鉴权
        try {
            LoginAuthRequestV2 loginAuthRequestV2 = new LoginAuthRequestV2();
            loginAuthRequestV2.setOuterIdentityCode("account");
            //对应外标编码所存的信息
            loginAuthRequestV2.setOuterIdentityValue(socialUserBindingDto.getUsername());
            //认证方式(密码:password;其他方式暂时不支持)
            loginAuthRequestV2.setAuthWay(BusinessConstant.USER_CENTER_AUTH_PASSWORD);
            //认证信息
            loginAuthRequestV2.setPassword(password);
            SingleResponse<LoginAuthRespDTO> response = authServiceAPI.validatePasswordV2(loginAuthRequestV2);
            if (!response.getCode().equals(BusinessConstant.USER_CENTER_RESPONSE_CODE_SUCCESS)) {
                throw new BusinessRuntimeException(BusinessCodeMessage.USER_PASSWORD_ERROR);
            }
            userCenterId = response.getData().getUserId();
        } catch (BusinessRuntimeException businessRuntimeException){
            throw new BusinessRuntimeException(businessRuntimeException);
        } catch (Exception e){
            log.error(e.getMessage(), e);
            throw new BusinessRuntimeException(BusinessCodeMessage.USER_CENTER_ERROR);
        }
    
        if(StringUtils.isEmpty(userCenterId)){
            throw new BusinessRuntimeException(BusinessCodeMessage.USER_PASSWORD_ERROR);
        }
        
        //绑定
        bindingCenter(userCenterId, userEntity.getId(), userEntity.getProvinceId());
    }
    
    /**
     * 解绑
     *
     * @param id
     * @return 结果
     **/
    @Override
    @Transactional(rollbackFor = Exception.class)
    public void unbinding(Long id) {
        SocialUserEntity socialUserEntity = socialUserRepository.findById(id)
                .orElseThrow(() -> new BusinessRuntimeException(BusinessCodeMessage.USER_CENTER_NOT_EXIST_BINDING));
    
        //校验是否有绑定关系
        if (ObjectUtils.isEmpty(userSocialUserRepository.findBySocialUserId(id))) {
            throw new BusinessRuntimeException(BusinessCodeMessage.USER_CENTER_NOT_EXIST_BINDING);
        }
        
        //删除 业务中台员工信息和绑定关系
        userSocialUserRepository.deleteBySocialUserId(socialUserEntity.getId());
        socialUserRepository.deleteById(socialUserEntity.getId());

        Long provinceId = socialUserEntity.getProvinceId();
        ProvinceTenantEntity provinceTenant = provinceTenantService.detail(provinceId);
        //从数据库中读取
        String bizCode = provinceTenant.getBizCode();

        try {
            GetOrgUnitByBizCodeRequest request = new GetOrgUnitByBizCodeRequest();
            request.setBizCode(bizCode);
            SingleResponse<OrgUnitDTO> unitResponse = orgServiceAPI.getOrgUnitByBizCode(request);
            OrgUnitDTO orgUnitDTO = unitResponse.getData();
            
            //查询组织信息
            ListEmployeeByOrgBizCodeAndOrgUnitIdRequest listEmployeeByOrgBizCodeAndOrgUnitIdRequest = new ListEmployeeByOrgBizCodeAndOrgUnitIdRequest();
            listEmployeeByOrgBizCodeAndOrgUnitIdRequest.setOrgBizCode(orgBizCode);
            listEmployeeByOrgBizCodeAndOrgUnitIdRequest.setOrgUnitId(orgUnitDTO.getId());
            //查询模式(1:只查当前节点,2:查询所有下级节点)
            listEmployeeByOrgBizCodeAndOrgUnitIdRequest.setQueryMode(1);
            ListEmployeeByOrgBizCodeAndOrgUnitIdRequest.PageQueryEmployeeFilterCondition filterCondition = new ListEmployeeByOrgBizCodeAndOrgUnitIdRequest.PageQueryEmployeeFilterCondition();
            //搜索词:目前支持按姓名、按工号查询
            filterCondition.setSearchTerm(socialUserEntity.getName());
            listEmployeeByOrgBizCodeAndOrgUnitIdRequest.setFilterCondition(filterCondition);
            //查询页号，最大不能超过200
            listEmployeeByOrgBizCodeAndOrgUnitIdRequest.setPageNumber(1);
            //每页返回的数量，最大不能超过200
            listEmployeeByOrgBizCodeAndOrgUnitIdRequest.setPageSize(10);
    
            MultiResponse<ListEmployeeByOrgBizCodeAndOrgUnitIdDTO> listResponse = employeeServiceAPI.listEmployeeByOrgBizCodeAndOrgUnitId(listEmployeeByOrgBizCodeAndOrgUnitIdRequest);
            if(listResponse.getData().getTotalCount()==0){
                throw new BusinessRuntimeException(BusinessCodeMessage.USER_CENTER_UNBINDING_FAIL);
            }
            String employeeJobIdentityId = listResponse.getData().getItems().stream().findFirst().map(item -> item.getEmployeeJobIdentityId()).get();
            
            //删除组织关系
            DeleteBizEmployeeRequest deleteBizEmployeeRequest = new DeleteBizEmployeeRequest();
            List<DeleteBizEmployeeRequest.DeleteBizEmployeeInfo> deleteBizEmployeeInfoList = new ArrayList<>();
            DeleteBizEmployeeRequest.DeleteBizEmployeeInfo deleteBizEmployeeInfo = new DeleteBizEmployeeRequest.DeleteBizEmployeeInfo();
            deleteBizEmployeeInfo.setEmployeeJobIdentityId(employeeJobIdentityId);
            deleteBizEmployeeInfoList.add(deleteBizEmployeeInfo);
            deleteBizEmployeeRequest.setDeleteBizEmployeeInfoList(deleteBizEmployeeInfoList);
    
            SingleResponse<DeleteEmployeeDTO> response = employeeServiceAPI.deleteBizEmployee(deleteBizEmployeeRequest);
            if(!response.getCode().equals(BusinessConstant.USER_CENTER_RESPONSE_CODE_SUCCESS)){
                throw new BusinessRuntimeException(BusinessCodeMessage.USER_CENTER_UNBINDING_FAIL);
            }
        }catch (Exception e){
            log.error(e.getMessage(), e);
            throw new BusinessRuntimeException(BusinessCodeMessage.USER_CENTER_ERROR);
        }
    }
    
    @Override
    public SocialUserVo detail(Long id) {
        // 校验用户是否存在
        UserEntity userEntity = userRepository.findById(id)
                .orElseThrow(() -> new BusinessRuntimeException(BusinessCodeMessage.USER_NOT_EXIST));
    
        UserSocialUserEntity userSocialUserEntity = userSocialUserRepository.findByUserId(userEntity.getId());
        Assert.notNull(userSocialUserEntity, BusinessCodeMessage.USER_CENTER_NOT_EXIST_BINDING);
    
        SocialUserEntity socialUserEntity = socialUserRepository.findById(Long.valueOf(userSocialUserEntity.getSocialUserId()))
                .orElseThrow(() -> new BusinessRuntimeException(BusinessCodeMessage.USER_CENTER_NOT_EXIST_BINDING));
        //查询详情是，先同步业务中台用户信息
        EmployeeInfoDTO employeeInfoDTO = null;
        try {
            //调用用户中台 获取员工信息
            GetEmployeeByUserIdRequest getEmployeeByUserIdRequest = new GetEmployeeByUserIdRequest();
            getEmployeeByUserIdRequest.setUserId(socialUserEntity.getUuid());
            SingleResponse<EmployeeInfoDTO> response = employeeServiceAPI.getEmployeeByUserId(getEmployeeByUserIdRequest);
            if(!response.getCode().equals(BusinessConstant.USER_CENTER_RESPONSE_CODE_SUCCESS)){
                throw new BusinessRuntimeException(BusinessCodeMessage.USER_CENTER_NOT_EXIST_USER);
            }
            employeeInfoDTO = response.getData();
            log.info("同步业务中台用户信息：{}", employeeInfoDTO);
        }catch (Exception e){
            log.error(e.getMessage(), e);
            throw new BusinessRuntimeException(BusinessCodeMessage.USER_CENTER_ERROR);
        }
    
        // 业务中台员工信息入库并绑定系统用户
        socialUserEntity.setPhone(employeeInfoDTO.getMobilePhone());
        socialUserEntity.setEmail(employeeInfoDTO.getEmail());
        socialUserEntity.setName(employeeInfoDTO.getName());
        socialUserRepository.save(socialUserEntity);
        
        SocialUserVo socialUserVo = new SocialUserVo();
        BeanUtils.copyProperties(socialUserEntity, socialUserVo);
        socialUserVo.setUsername(userEntity.getUsername());
        return socialUserVo;
    }
    
    /**
     * 用户中台登录
     *
     * @param username
     * @return 结果
     **/
    @Override
    public Long userCenterLogin(String username) {
        SocialUserEntity socialUserEntity = socialUserRepository.findByAccount(username);
        UserSocialUserEntity userSocialUserEntity = userSocialUserRepository.findBySocialUserId(socialUserEntity.getId());
        return userSocialUserEntity.getUserId();
    }

    @Override
    public String socialUserLogin(SocialUserExistDto socialUserExistDto) {
        String userCenterId = "";
        try {
            LoginAuthRequestV2 loginAuthRequestV2 = new LoginAuthRequestV2();
            List<String> userTypes = Arrays.asList("000001","000101","000102");
            loginAuthRequestV2.setUserTypes(userTypes);
            loginAuthRequestV2.setOuterIdentityCode("account");
            //对应外标编码所存的信息
            loginAuthRequestV2.setOuterIdentityValue(socialUserExistDto.getUsername());
            //认证方式(密码:password;其他方式暂时不支持)
            loginAuthRequestV2.setAuthWay(BusinessConstant.USER_CENTER_AUTH_PASSWORD);
            //认证信息
            loginAuthRequestV2.setPassword(socialUserExistDto.getPassword());
            SingleResponse<LoginAuthRespDTO> response = authServiceAPI.validatePasswordV2(loginAuthRequestV2);
            if (!response.getCode().equals(BusinessConstant.USER_CENTER_RESPONSE_CODE_SUCCESS)) {
                throw new BusinessRuntimeException(BusinessCodeMessage.USER_PASSWORD_ERROR);
            }
            userCenterId = response.getData().getUserId();
        } catch (BusinessRuntimeException businessRuntimeException){
            throw new BusinessRuntimeException(businessRuntimeException);
        }  catch (Exception e){
            log.error(e.getMessage(), e);
            throw new BusinessRuntimeException(BusinessCodeMessage.USER_CENTER_ERROR);
        }
        if(StringUtils.isEmpty(userCenterId)){
            throw new BusinessRuntimeException(BusinessCodeMessage.USER_PASSWORD_ERROR);
        }
        return userCenterId;
    }

    /**
     * 解密
     *
     * @param password
     * @return 结果
     **/
    private String decrypt(String password) {
        try {
            return passwordService.getOriPassword(password);
        } catch (Exception e) {
            throw new BadCredentialsException(RcEnum.VALIDATE_LOGIN_ILLEGAL_PASSWORD.getMessageKey(), e);
        }
    }
    
    /**
     * 用户中心绑定
     *
     * @param userCenterId
     * @param userId
     * @param provinceId
     * @return 结果
     **/
    private void bindingCenter(String userCenterId, Long userId, Long provinceId){
        //校验用户中心id是否已绑定系统用户
        SocialUserEntity socialUserEntity = socialUserRepository.findByUuid(userCenterId);
        if(ObjectUtils.isNotEmpty(socialUserEntity)){
            throw new BusinessRuntimeException(BusinessCodeMessage.USER_CENTER_EXIST_BINDING);
        }
        ProvinceTenantEntity provinceTenant = provinceTenantService.detail(provinceId);
        //从数据库中读取
        String bizCode = provinceTenant.getBizCode();
        //调用用户中台 获取员工信息
        EmployeeInfoDTO employeeInfoDTO = null;
        OrgUnitDTO orgUnitDTO = null;
        GetUserDTO getUserDTO = null;
        try {
            // 获取员工信息
            GetEmployeeByUserIdRequest getEmployeeByUserIdRequest = new GetEmployeeByUserIdRequest();
            getEmployeeByUserIdRequest.setUserId(userCenterId);
            SingleResponse<EmployeeInfoDTO> response = employeeServiceAPI.getEmployeeByUserId(getEmployeeByUserIdRequest);
            if(!response.getCode().equals(BusinessConstant.USER_CENTER_RESPONSE_CODE_SUCCESS)){
                throw new BusinessRuntimeException(BusinessCodeMessage.USER_CENTER_NOT_EXIST_USER);
            }
            employeeInfoDTO = response.getData();
        
            GetOrgUnitByBizCodeRequest request = new GetOrgUnitByBizCodeRequest();
            request.setBizCode(bizCode);
            SingleResponse<OrgUnitDTO> unitResponse = orgServiceAPI.getOrgUnitByBizCode(request);
            orgUnitDTO = unitResponse.getData();
    
            GetUserRequest getUserRequest = new GetUserRequest();
            getUserRequest.setUserId(userCenterId);
            SingleResponse<GetUserDTO> userResponse = userServiceAPI.getUser(getUserRequest);
            getUserDTO = userResponse.getData();
        }catch (Exception e){
            log.error(e.getMessage(), e);
            throw new BusinessRuntimeException(BusinessCodeMessage.USER_CENTER_ERROR);
        }


        // 业务中台员工信息入库并绑定系统用户
        SocialUserEntity socialUser = new SocialUserEntity();
        socialUser.setSource(BusinessConstant.THIRD_SOURCE_ALI);
        socialUser.setUuid(userCenterId);
        socialUser.setAccount(getUserDTO.getLoginName());
        socialUser.setPhone(employeeInfoDTO.getMobilePhone());
        socialUser.setEmail(employeeInfoDTO.getEmail());
        socialUser.setOrgCode(bizCode);
        socialUser.setOrgName(orgUnitDTO.getOrgUnitName());
        socialUser.setName(employeeInfoDTO.getName());
        socialUser.setProvinceId(provinceId);
        socialUserRepository.save(socialUser);
        UserSocialUserEntity userSocialUserEntity = new UserSocialUserEntity();
        userSocialUserEntity.setSocialUserId(String.valueOf(socialUser.getId()));
        userSocialUserEntity.setUserId(userId);
        userSocialUserRepository.save(userSocialUserEntity);
    
        //创建业务组织员工(引用行政组织员工)
        try {
            CreateBizEmployeeRequest createBizEmployeeRequest = new CreateBizEmployeeRequest();
            List<CreateBizEmployeeRequest.CreateBizEmployeeInfo> createBizEmployeeInfos = new ArrayList<>();
            CreateBizEmployeeRequest.CreateBizEmployeeInfo createBizEmployeeInfo = new CreateBizEmployeeRequest.CreateBizEmployeeInfo();
            createBizEmployeeInfo.setEmployeeId(employeeInfoDTO.getEmployeeId());
            createBizEmployeeInfo.setOrgUnitId(orgUnitDTO.getId());
            createBizEmployeeInfo.setOrgBizCode(orgBizCode);
            createBizEmployeeInfo.setSourceOrgUnitId(employeeInfoDTO.getOrgUnitId());
            createBizEmployeeInfos.add(createBizEmployeeInfo);
            createBizEmployeeRequest.setCreateBizEmployeeInfoList(createBizEmployeeInfos);
            SingleResponse<CreateBizEmployeeDTO> response = employeeServiceAPI.createBizEmployee(createBizEmployeeRequest);
            if (!response.getCode().equals(BusinessConstant.USER_CENTER_RESPONSE_CODE_SUCCESS) && !response.getCode()
                    .equals(BusinessConstant.USER_CENTER_RESPONSE_CODE_REPEAT_BINGING)) {
                throw new BusinessRuntimeException(BusinessCodeMessage.USER_CENTER_BINGING_ERROR);
            }
        }catch (Exception e){
            log.error(e.getMessage(), e);
            throw new BusinessRuntimeException(BusinessCodeMessage.USER_CENTER_ERROR);
        }
    }

    public String login(SocialUserExistDto socialUserExistDto){
        String userId = "";
        Request request;
        try {
            String value = JsonUtils.toJson(socialUserExistDto);
            RequestBody requestBody = RequestBody.create(MediaType.parse("application/json"), value);
            request = new Request.Builder().url(ipProperties.getIp() + socialUserLogin).post(requestBody).build();

            Response response = httpClient.newCall(request).execute();
            boolean successful = response.isSuccessful();
            if (!successful) {
                log.error("调用省级中台登录接口异常, code:{}, body:{}", response == null ? "null" : response.code(),
                        response == null ? "null" : response.body());
                throw new BusinessRuntimeException(BusinessCodeMessage.SOCIAL_LOGIN_ERROR);
            }
            String result = response.body().string();
            log.info("===result=== {}", result);
            JSONObject jsonNode = JSONUtil.parseObj(result);
            int code = jsonNode.getInt("code");
            JSONObject dataNode = jsonNode.getJSONObject("data");
            if (code == CodeMessageEnum.OK.getCode()) {
                userId = dataNode.getStr("userCenterId");
            }
        }catch (IOException io){
            throw new BusinessRuntimeException(BusinessCodeMessage.SOCIAL_LOGIN_ERROR);
        }

        return userId;
    }
    
}
