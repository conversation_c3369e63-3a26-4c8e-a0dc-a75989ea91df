package cn.teleinfo.idycprovince.server.modules.auth.service.auth.impl;

import cn.hutool.core.date.LocalDateTimeUtil;
import cn.hutool.core.lang.UUID;
import cn.hutool.core.util.ObjectUtil;
import cn.hutool.core.util.StrUtil;
import cn.teleinfo.idycprovince.common.constant.BusinessConstant;
import cn.teleinfo.idycprovince.common.constant.LevelTypeEnum;
import cn.teleinfo.idycprovince.common.exception.BusinessCodeMessage;
import cn.teleinfo.idycprovince.infrastructure.db.entity.*;
import cn.teleinfo.idycprovince.infrastructure.db.repository.*;
import cn.teleinfo.idycprovince.infrastructure.db.to.EntUserInfoTO;
import cn.teleinfo.idycprovince.infrastructure.redis.RedisCache;
import cn.teleinfo.idycprovince.server.config.AppSecurityConfig;
import cn.teleinfo.idycprovince.server.constants.RedisKeyConstants;
import cn.teleinfo.idycprovince.server.enums.RoleTypeEnum;
import cn.teleinfo.idycprovince.server.enums.UpdatePasswordTypeEnum;
import cn.teleinfo.idycprovince.server.modules.auth.controller.dto.ForgetDto;
import cn.teleinfo.idycprovince.server.modules.auth.controller.dto.InternalAccountDto;
import cn.teleinfo.idycprovince.server.modules.auth.controller.dto.SocialUserAddDTO;
import cn.teleinfo.idycprovince.server.modules.auth.controller.dto.SocialUserUpdateDTO;
import cn.teleinfo.idycprovince.server.modules.auth.controller.dto.UserDto;
import cn.teleinfo.idycprovince.server.modules.auth.controller.vo.UserVo;
import cn.teleinfo.idycprovince.server.modules.auth.service.PasswordService;
import cn.teleinfo.idycprovince.server.modules.auth.service.auth.SocialUserServiceInterface;
import cn.teleinfo.idycprovince.server.modules.auth.service.auth.UserServiceInterface;
import cn.teleinfo.idycprovince.server.modules.auth.util.SecurityUtil;
import cn.teleinfo.idycprovince.server.modules.entprefixhosting.constant.HostingApplyEnum;
import cn.teleinfo.idycprovince.server.modules.prefix.vo.EntDropDownVO;
import cn.teleinfo.idycprovince.server.modules.system.service.LoginConfigService;
import cn.teleinfo.idycprovince.server.modules.system.vo.LoginConfigVO;
import cn.teleinfo.idycprovince.server.modules.util.Assert;
import com.abluepoint.summer.mvc.domain.PageResult;
import com.abluepoint.summer.mvc.exception.BusinessRuntimeException;
import lombok.RequiredArgsConstructor;
import lombok.SneakyThrows;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.ObjectUtils;
import org.apache.commons.lang3.StringUtils;
import org.springframework.beans.BeanUtils;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.cloud.context.config.annotation.RefreshScope;
import org.springframework.data.domain.Page;
import org.springframework.data.domain.PageRequest;
import org.springframework.data.domain.Sort;
import org.springframework.security.crypto.password.PasswordEncoder;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import java.time.LocalDateTime;
import java.time.temporal.ChronoUnit;
import java.util.*;
import java.util.stream.Collectors;

@Service
@RefreshScope
@RequiredArgsConstructor
@Slf4j
public class UserService implements UserServiceInterface {

    private final UserRepository userRepository;

    private final UserRoleRepository userRoleRepository;
    private final RoleRepository roleRepository;

    private final EntRepository entRepository;

    private final PasswordService passwordService;

    private final AuthService authService;

    private final PasswordEncoder passwordEncoder;
    private final ProvinceTenantRepository provinceTenantRepository;

    private final AppSecurityConfig.AppSecurityProperties appSecurityProperties;
    private final UserAppRepository userAppRepository;
    private final AppInfoRepository appInfoRepository;
    private final RoleSerivce roleSerivce;
    private final LoginConfigService loginConfigService;
    private final LoginConfigRepository loginConfigRepository;
    private static final Long ADMIN_ROLE_ID = 2L;

    private final EntPrefixHostingApplyRepository  entPrefixHostingApplyRepository;

    //    @Value("${app.security.default-pwd}")
    private String DEFAULT_PWD = "Admin@123";
    
    private final UserSocialUserRepository userSocialUserRepository;
    
    private final SocialUserServiceInterface socialUserService;

    private final RedisCache redisCache;

    private final int expiredSecond = 3*60;

    /**
     * 5分钟内输入次数超过x次，锁定x分钟
     */
    private final int codeCheckLockDuration = 5*60;

    @Value("${app.mp-dmm.enable}")
    private boolean mpDmmEnable;

    @Value("${app.system-type}")
    private Integer systemType;

    @Override
    @Transactional
    public void addUserAdmin(UserDto userDto) {
        // 校验手机号和username
        boolean existsByUsername = userRepository.existsByUsernameAndProvinceId(userDto.getUsername(), userDto.getProvinceId());
        if (existsByUsername) {
            Assert.throwEx(BusinessCodeMessage.USERNAME_NOT_EXIST);
        }
        boolean existsByPhone = userRepository.existsByPhoneAndProvinceId(userDto.getPhone(), userDto.getProvinceId());
        if (existsByPhone) {
            Assert.throwEx(BusinessCodeMessage.PHONE_IS_EXIST);
        }

        boolean existsByEmail = userRepository.existsByEmailAndProvinceId(userDto.getEmail(), userDto.getProvinceId());
        if (existsByEmail) {
            Assert.throwEx(BusinessCodeMessage.EMAIL_IS_EXIST);
        }

        // 添加默认密码
        userDto.setPassword(passwordEncoder.encode(DEFAULT_PWD));

        // 入用户表
        UserEntity user = new UserEntity();
        BeanUtils.copyProperties(userDto, user);
        UserEntity userEntity = userRepository.save(user);

        // 入角色表
        List<UserRoleEntity> resultList = new ArrayList<>();
        userDto.getRoles().forEach(index -> resultList.add(new UserRoleEntity(userEntity.getId(), index)));
        userRoleRepository.saveAll(resultList);
    }


    @Override
    @Transactional
    public void addUserEntAdmin(UserDto userDto) {
        // 校验手机号和username
        boolean existsByUsername = userRepository.existsByUsernameAndProvinceId(userDto.getUsername(), userDto.getProvinceId());
        if (existsByUsername) {
            Assert.throwEx(BusinessCodeMessage.USERNAME_NOT_EXIST);
        }
        boolean existsByPhone = userRepository.existsByPhoneAndProvinceId(userDto.getPhone(), userDto.getProvinceId());
        if (existsByPhone) {
            Assert.throwEx(BusinessCodeMessage.PHONE_IS_EXIST);
        }

        boolean existsByEmail = userRepository.existsByEmailAndProvinceId(userDto.getEmail(), userDto.getProvinceId());
        if (existsByEmail) {
            Assert.throwEx(BusinessCodeMessage.EMAIL_IS_EXIST);
        }

        // 添加默认密码
        userDto.setPassword(passwordEncoder.encode(DEFAULT_PWD));
        Long currentProvinceId = SecurityUtil.getCurrentProvinceId();
        if (Objects.isNull(userDto.getEntId()) || userDto.getEntId() == 0) {
            Assert.notBlankStr(userDto.getEntName(), BusinessCodeMessage.ENT_NAME_NOT_NULL);
            // 添加到企业用户表
            EntEntity entEntity = new EntEntity();
            entEntity.setOrgName(userDto.getEntName());
            entEntity.setCreatedBy(SecurityUtil.getCurrentUserId());
            entEntity.setProvinceId(currentProvinceId);
            EntEntity save = entRepository.save(entEntity);
            userDto.setEntId(save.getId());
        }

        // 入用户表
        UserEntity user = new UserEntity();
        BeanUtils.copyProperties(userDto, user);
        UserEntity userEntity = userRepository.save(user);

        //获取角色信息
        RoleEntity roleEntity = roleSerivce.getRoleByProvinceIdAndRoleTypeEnum(userDto.getProvinceId(), RoleTypeEnum.ROLE_TYPE_ENUM_ENT);
        // 入角色表
        userRoleRepository.save(new UserRoleEntity(userEntity.getId(), roleEntity.getId()));
    }

    @Override
    @Transactional
    public void modifyUserInfo(UserDto userDto) {
        UserEntity orgUser = userRepository.findById(userDto.getId())
                .orElseThrow(() -> new BusinessRuntimeException(BusinessCodeMessage.USER_NOT_EXIST));

        //验证手机号是否修改
        if (!userDto.getPhone().equals(orgUser.getPhone())) {
            if (userRepository.existsByPhoneAndProvinceId(userDto.getPhone(), userDto.getProvinceId())) {
                Assert.throwEx(BusinessCodeMessage.PHONE_IS_EXIST);
            }
        }

        if (!userDto.getEmail().equals(orgUser.getEmail())) {
            if (userRepository.existsByEmailAndProvinceId(userDto.getEmail(), userDto.getProvinceId())) {
                Assert.throwEx(BusinessCodeMessage.EMAIL_IS_EXIST);
            }
        }

        BeanUtils.copyProperties(userDto, orgUser, "password", "provinceId", "entId", "username");
        orgUser.setUpdatedBy(SecurityUtil.getCurrentUserId());
        UserEntity user = userRepository.save(orgUser);

        // 入角色表
        List<UserRoleEntity> resultList = new ArrayList<>();
        userDto.getRoles().forEach(index -> resultList.add(new UserRoleEntity(user.getId(), index)));
        userRoleRepository.saveAll(resultList);
    }

    @Override
    @SneakyThrows
    public UserEntity modifyUserInfoBySelf(UserDto userDto) {
        UserEntity orgUser = userRepository.findByUsernameAndProvinceId(userDto.getUsername(), userDto.getProvinceId());
        if (null==orgUser) {
            throw new BusinessRuntimeException(BusinessCodeMessage.USER_NOT_EXIST);
        }
        String oriPassword = userDto.getPassword();
        if (appSecurityProperties.isPasswordEncrypted()) {
            try {
                oriPassword = passwordService.getOriPassword(userDto.getPassword());

            } catch (Exception e) {
                Assert.throwEx(BusinessCodeMessage.PWD_IDENTIFY_FAIL, e);
            }
        }
        if (!passwordEncoder.matches(oriPassword, orgUser.getPassword())) {
            throw new BusinessRuntimeException(BusinessCodeMessage.PASSWORD_CHECKED_FAILED);
        }

        //验证手机号是否修改
        if (!userDto.getPhone().equals(orgUser.getPhone())) {
            if (userRepository.existsByPhoneAndProvinceId(userDto.getPhone(), userDto.getProvinceId())) {
                Assert.throwEx(BusinessCodeMessage.PHONE_IS_EXIST);
            }
        }

        //验证邮箱是否修改
        if (!userDto.getEmail().equals(orgUser.getEmail())) {
            if (userRepository.existsByEmailAndProvinceId(userDto.getEmail(), userDto.getProvinceId())) {
                Assert.throwEx(BusinessCodeMessage.EMAIL_IS_EXIST);
            }
        }

        orgUser.setEmail(userDto.getEmail());
        orgUser.setPhone(userDto.getPhone());

        orgUser.setUpdatedBy(SecurityUtil.getCurrentUserId());
        UserEntity user = userRepository.save(orgUser);
        return user;
    }

    @Override
    public void modifyUserPwd(UserDto userDto) {
        UserEntity orgEntity = userRepository.findById(userDto.getId())
                .orElseThrow(() -> new BusinessRuntimeException(BusinessCodeMessage.USER_NOT_EXIST));

        String newOriPassword = userDto.getNewPwd();
        String duplicatePwd = userDto.getDuplicatePwd();
        String entityPassword = orgEntity.getPassword();
        if (appSecurityProperties.isPasswordEncrypted()) {
            try {
                newOriPassword = passwordService.getOriPassword(userDto.getNewPwd());
                duplicatePwd = passwordService.getOriPassword(userDto.getDuplicatePwd());
            } catch (Exception e) {
                Assert.throwEx(BusinessCodeMessage.PWD_IDENTIFY_FAIL, e);
            }
        }

        Assert.assertTrue(newOriPassword.equals(duplicatePwd), BusinessCodeMessage.PWD_NOT_DUPLICATE);
        Assert.checkPassword(newOriPassword);

        if (passwordEncoder.matches(newOriPassword, entityPassword)) {
            Assert.throwEx(BusinessCodeMessage.PWD_EQUAL_OLD);
        }
        orgEntity.setPassword(passwordEncoder.encode(newOriPassword));
        orgEntity.setPasswordResetTime(LocalDateTime.now());
        orgEntity.setUpdatedBy(SecurityUtil.getCurrentUserId());

        userRepository.save(orgEntity);

    }
    @Override
    public void forgetUserPasssword(ForgetDto forgetDto) {

        String provinceIdAndEmail =  forgetDto.getProvinceId()+forgetDto.getEmail();

        checkEmailLock(provinceIdAndEmail);

        String rediskey = RedisKeyConstants.REDIS_KEY_FORGET_PASSWORD + forgetDto.getEmail();
        String codeTime;
        try {
            codeTime = redisCache.get(rediskey);
        } catch (Exception e) {
            log.error(e.getMessage(), e);
            throw new BusinessRuntimeException(BusinessCodeMessage.CACHE_READ_FAILED);
        }

        if (StrUtil.isBlank(codeTime)) {
            addEmailCodeCheckErrorTimes(provinceIdAndEmail);
            throw new BusinessRuntimeException(BusinessCodeMessage.CAPTCHA_CHECK_NO_SENT);
        }
        String code = codeTime.split("-")[0];
        String time = codeTime.split("-")[1];

        if (!code.equals(forgetDto.getCode())) {
            addEmailCodeCheckErrorTimes(provinceIdAndEmail);
            throw new BusinessRuntimeException(BusinessCodeMessage.CAPTCHA_CHECK_FAILE);
        }

        Long timeInterval = System.currentTimeMillis()-Long.parseLong(time);
        if (timeInterval>expiredSecond*1000) {
            addEmailCodeCheckErrorTimes(provinceIdAndEmail);
            throw new BusinessRuntimeException(BusinessCodeMessage.CAPTCHA_CHECK_TIME_OUT);
        }

        String newPassword = forgetDto.getNewPassword();
        String rePassword = forgetDto.getRePassword();
        if (appSecurityProperties.isPasswordEncrypted()) {
            try {
                newPassword = passwordService.getOriPassword(forgetDto.getNewPassword());
                rePassword = passwordService.getOriPassword(forgetDto.getRePassword());
            } catch (Exception e) {
                Assert.throwEx(BusinessCodeMessage.PWD_IDENTIFY_FAIL, e);
            }
        }

        if (!rePassword.equals(newPassword)) {
            throw new BusinessRuntimeException(BusinessCodeMessage.PWD_NOT_DUPLICATE);
        }

        Assert.checkPassword(newPassword);

        boolean delete = redisCache.delete(rediskey);
        if (!delete) {
            throw new BusinessRuntimeException(BusinessCodeMessage.CACHE_READ_FAILED);
        }

        UserEntity oriUser = userRepository.findByEmailAndProvinceId(forgetDto.getEmail(), forgetDto.getProvinceId());
        if (oriUser == null) {
            throw new BusinessRuntimeException(BusinessCodeMessage.USER_NOT_EXIST);
        }
        if (passwordEncoder.matches(newPassword, oriUser.getPassword())) {
            Assert.throwEx(BusinessCodeMessage.PWD_EQUAL_OLD);
        }

        oriUser.setPassword(passwordEncoder.encode(newPassword));
        oriUser.setPasswordResetTime(LocalDateTime.now());

        userRepository.save(oriUser);

        clearEmailCodeCheckErrorTimes(provinceIdAndEmail);
    }

    void addEmailCodeCheckErrorTimes(String username) {
        if (StrUtil.isBlank(username)) {
            return;
        }
        String redisKeyForgetPasswordCheckErrorTimes = RedisKeyConstants.REDIS_KEY_FORGET_PASSWORD_CHECK_ERROR_TIMES+username;
        Integer currentTimesNum = 0;
        String currentTimes = redisCache.get(redisKeyForgetPasswordCheckErrorTimes);
        if (null!=currentTimes) {
            currentTimesNum = Integer.valueOf(currentTimes);
        }
        currentTimesNum++;
        redisCache.set(redisKeyForgetPasswordCheckErrorTimes, currentTimesNum.toString(), codeCheckLockDuration);

        LoginConfigVO loginConfigVO = loginConfigService.detail();
        if (currentTimesNum>=loginConfigVO.getEmialErrLockCount()) {
            userLock(username);
            redisCache.set(redisKeyForgetPasswordCheckErrorTimes, "0");
        }
    }

    void clearEmailCodeCheckErrorTimes(String key) {
        if (StrUtil.isBlank(key)) {
            return;
        }
        String redisKeyForgetPasswordCheckErrorTimes = RedisKeyConstants.REDIS_KEY_FORGET_PASSWORD_CHECK_ERROR_TIMES+key;
        Boolean hasKey = redisCache.hasKey(redisKeyForgetPasswordCheckErrorTimes);
        if (hasKey) {
            redisCache.delete(redisKeyForgetPasswordCheckErrorTimes);
        }
    }

    void userLock(String key) {
        if (StrUtil.isBlank(key)) {
            return;
        }
        LoginConfigVO loginConfigVO = loginConfigService.detail();
        String userLockKey = RedisKeyConstants.REDIS_KEY_USER_CHECK_ERROR_LOCK+key;
        redisCache.set(userLockKey, "1", loginConfigVO.getEmialErrLockTime()*60);
    }

    @Override
    public void checkEmailLock(String key) {
        String userLockKey = RedisKeyConstants.REDIS_KEY_USER_CHECK_ERROR_LOCK+key;
        Boolean hasKey = redisCache.hasKey(userLockKey);
        if (hasKey) {
            LoginConfigEntity entity = loginConfigRepository.findAll().get(0);
            throw new BusinessRuntimeException(BusinessCodeMessage.EMAIL_LOCKED,entity.getEmialErrLockTime());
        }
    }

    @Override
    @Transactional(rollbackFor = Exception.class)
    public void addSocialUser(SocialUserAddDTO socialUserAddDTO) {
        //校验用户登录名是否重复
        UserEntity username = userRepository.findByUsernameAndProvinceId(socialUserAddDTO.getUsername(),SecurityUtil.getCurrentProvinceId());
        if(ObjectUtil.isNotEmpty(username)){
            throw new BusinessRuntimeException(BusinessCodeMessage.USERNAME_IS_REPETITION_ERROR);
        }
        Long provinceId = SecurityUtil.getCurrentProvinceId();
        //封装用户信息
        UserEntity user = new UserEntity();
        BeanUtils.copyProperties(socialUserAddDTO,user);
        //随机生成密码
        user.setPassword(passwordEncoder.encode(UUID.randomUUID().toString()));
        user.setProvinceId(provinceId);

        Long currentUserId = SecurityUtil.getCurrentUserId();
        RoleEntity roleEntity1 = roleRepository.findById(userRoleRepository.findUserRoleEntitiesByUserId(currentUserId).get(0).getRoleId()).get();

        if (ObjectUtil.isNotEmpty(roleEntity1)){

            if (roleEntity1.getRoleType().equals(BusinessConstant.ENT_ROLE)){
                user.setEntId(SecurityUtil.getCurrentEntId());
            }

        }


        UserEntity userEntity = userRepository.save(user);
        //封装中台关联表数据
        UserSocialUserEntity userSocialUserEntity = new UserSocialUserEntity();
        userSocialUserEntity.setSocialUserId(socialUserAddDTO.getUserId());
        userSocialUserEntity.setUserId(userEntity.getId());
        userSocialUserRepository.save(userSocialUserEntity);
        //添加用户角色信息
        UserRoleEntity userRole = new UserRoleEntity();
        userRole.setUserId(userEntity.getId());
        userRole.setRoleId(socialUserAddDTO.getRoleId());
        userRoleRepository.save(userRole);

        RoleEntity roleEntity = roleRepository.findById(socialUserAddDTO.getRoleId()).get();
        if(roleEntity.getRoleCode().equals("sys_app_user")){

            UserAppEntity userAppEntity = new UserAppEntity();
            userAppEntity.setUserId(userEntity.getId());
            userAppEntity.setAppId(socialUserAddDTO.getAppId());
            userAppRepository.save(userAppEntity);
        }
    }

    @Override
    public void updateSocialUser(SocialUserUpdateDTO socialUserUpdateDTO) {
        UserEntity user = userRepository.findById(socialUserUpdateDTO.getId()).get();
//        if (systemType == 1){
//
//        }else {
//
//        }

        if (ObjectUtil.isNotEmpty(socialUserUpdateDTO.getEntId())){
            user.setEntId(socialUserUpdateDTO.getEntId());
        }else {
            user.setEntId(SecurityUtil.getCurrentEntId());
        }


        //修改用户角色信息
        UserRoleEntity userRole = userRoleRepository.findByUserId(socialUserUpdateDTO.getId());
        userRole.setRoleId(socialUserUpdateDTO.getRoleId());
        userRoleRepository.save(userRole);
        //修改应用用户
        RoleEntity roleEntity = roleRepository.findById(socialUserUpdateDTO.getRoleId()).get();
        if(roleEntity.getRoleCode().equals("sys_app_user")){
            UserAppEntity userAppEntity = userAppRepository.findByUserId(socialUserUpdateDTO.getId());
            if(ObjectUtils.isEmpty(userAppEntity)){
                UserAppEntity userApp = new UserAppEntity();
                userApp.setUserId(socialUserUpdateDTO.getId());
                userApp.setAppId(socialUserUpdateDTO.getAppId());
                userAppRepository.save(userApp);
            }else {
                userAppEntity.setAppId(socialUserUpdateDTO.getAppId());
                userAppRepository.save(userAppEntity);
            }

        } else if(roleEntity.getRoleCode().equals("sys_admin")){
            UserAppEntity userAppEntity = userAppRepository.findByUserId(socialUserUpdateDTO.getId());
            if(ObjectUtils.isNotEmpty(userAppEntity)){
                userAppRepository.deleteById(userAppEntity.getId());
                user.setEntId(null);
                userRepository.save(user);
            }

        }else if(roleEntity.getRoleCode().equals("sys_ent_admin")) {

            UserAppEntity userAppEntity = userAppRepository.findByUserId(socialUserUpdateDTO.getId());
            if (ObjectUtils.isNotEmpty(userAppEntity)) {
                userAppRepository.delete(userAppEntity);
            }

        }else {
            UserAppEntity userAppEntity = userAppRepository.findByUserId(socialUserUpdateDTO.getId());
            if (ObjectUtils.isNotEmpty(userAppEntity)) {
                userAppRepository.delete(userAppEntity);
            }
        }

    }



    @Override
    public void resetPwdUser(UserDto userDto) {
        UserEntity orgEntity = userRepository.findById(userDto.getId())
                .orElseThrow(() -> new BusinessRuntimeException(BusinessCodeMessage.USER_NOT_EXIST));

        String newOriPassword = userDto.getNewPwd();
        String duplicatePwd = userDto.getDuplicatePwd();

        if (appSecurityProperties.isPasswordEncrypted()) {
            try {
                newOriPassword = passwordService.getOriPassword(userDto.getNewPwd());
                duplicatePwd = passwordService.getOriPassword(userDto.getDuplicatePwd());
            } catch (Exception e) {
                Assert.throwEx(BusinessCodeMessage.PWD_IDENTIFY_FAIL, e);
            }
        }

        Assert.assertTrue(newOriPassword.equals(duplicatePwd), BusinessCodeMessage.PWD_NOT_DUPLICATE);
        Assert.checkPassword(newOriPassword);

        orgEntity.setPassword(passwordEncoder.encode(newOriPassword));
        orgEntity.setPasswordResetTime(LocalDateTime.now());
        orgEntity.setUpdatedBy(SecurityUtil.getCurrentUserId());

        userRepository.save(orgEntity);
    }

    @Override
    public Long addInternalAccount(InternalAccountDto internalAccountDto) {

        UserEntity username = userRepository.findByUsername(internalAccountDto.getUsername());
        if(ObjectUtil.isNotEmpty(username)){
            throw new BusinessRuntimeException(BusinessCodeMessage.USERNAME_IS_REPETITION_ERROR);
        }

        Long currentUserId = SecurityUtil.getCurrentUserId();
        RoleEntity roleEntity = roleRepository.findById(userRoleRepository.findUserRoleEntitiesByUserId(currentUserId).get(0).getRoleId()).get();

        Long provinceId = SecurityUtil.getCurrentProvinceId();
        UserEntity userEntity = new UserEntity();
        //保存用户
        userEntity.setUsername(internalAccountDto.getUsername());
        userEntity.setPassword(passwordEncoder.encode(UUID.randomUUID().toString()));
        userEntity.setAddress(internalAccountDto.getAddress());
        userEntity.setRemark(internalAccountDto.getRemark());
        userEntity.setIsDeleted(0);
        userEntity.setProvinceId(provinceId);

        userRepository.save(userEntity);

        UserRoleEntity userRoleEntity = new UserRoleEntity();
        userRoleEntity.setUserId(userEntity.getId());
        userRoleEntity.setRoleId(internalAccountDto.getRoleId());
        userRoleEntity.setIsDeleted(0);
        userRoleRepository.save(userRoleEntity);


        if (ObjectUtil.isNotEmpty(roleEntity)){
            RoleEntity byId = roleRepository.findById(internalAccountDto.getRoleId()).get();

            if (roleEntity.getRoleType().equals(BusinessConstant.PROVINCE_ROLE)){




                //根据角色判断 设置关联信息
                if (BusinessConstant.ENT_ROLE.equals(byId.getRoleType())) {
                    userEntity.setEntId(internalAccountDto.getEntId());
                    userRepository.save(userEntity);

                }else if (BusinessConstant.APP_ROLE.equals(byId.getRoleType())){

                    userEntity.setEntId(internalAccountDto.getEntId());
                    userRepository.save(userEntity);

                    UserAppEntity userAppEntity = new UserAppEntity();
                    userAppEntity.setUserId(userEntity.getId());
                    userAppEntity.setAppId(internalAccountDto.getAppId());

                    userAppRepository.save(userAppEntity);

                }
            }else if (roleEntity.getRoleType().equals(BusinessConstant.ENT_ROLE)){
                //根据角色判断 设置关联信息
//                if (BusinessConstant.ENT_ROLE.equals(internalAccountDto.getRoleId())) {
                    userEntity.setEntId(SecurityUtil.getCurrentEntId());
                    userRepository.save(userEntity);

                if (BusinessConstant.APP_ROLE.equals(byId.getRoleType())){

                    UserAppEntity userAppEntity = new UserAppEntity();
                    userAppEntity.setUserId(userEntity.getId());
                    userAppEntity.setAppId(internalAccountDto.getAppId());
                    userAppRepository.save(userAppEntity);

                }
            }

        }

        return userEntity.getId();
    }

    @Override
    public Boolean existsAllByProvinceId(Long provinceId) {

        return userRepository.existsAllByProvinceId(provinceId);
    }

    public PageResult<UserVo> getUserList(String username,Integer type, Integer currentPage, Integer pageSize, Boolean isSupperAdmin) {
        Long provinceId = SecurityUtil.getCurrentProvinceId();
        Long entId = SecurityUtil.getCurrentEntId();
        Page<EntUserInfoTO> pageResult;
        if (isSupperAdmin) {
            pageResult = userRepository.listUserBySuperAdmin(null, StrUtil.isNotBlank(username)?username: null
                    , PageRequest.of(currentPage, pageSize, Sort.by(Sort.Direction.DESC, "updatedTime")));
        }else {
            //if (LevelTypeEnum.PROVINCE.getCode() == userDto.getType()) {
//                pageResult = userRepository.listUserByProvince(provinceId, username,entId, PageRequest.of(currentPage, pageSize, Sort.by(Sort.Direction.DESC, "updatedTime")));
//            } else {
//                pageResult = userRepository.listUserByEnt(userDto.getEntName(), provinceId, entId, userDto.getUsername()
//                        , PageRequest.of(currentPage, pageSize, Sort.by(Sort.Direction.DESC, "updatedTime")));
//            }

            if (type == 2){
                pageResult = userRepository.listUserByMiddleGround(provinceId, username,entId, PageRequest.of(currentPage, pageSize, Sort.by(Sort.Direction.DESC, "updatedTime")));
            }else{
                pageResult = userRepository.listUserByProvince(provinceId, username,entId, PageRequest.of(currentPage, pageSize, Sort.by(Sort.Direction.DESC, "updatedTime")));
            }


        }

        Page<UserVo> result = pageResult.map(source -> {
            UserVo userVo = new UserVo();

            BeanUtils.copyProperties(source, userVo);
            return userVo;
        });

        // 角色信息赋值
        if (result.isEmpty()) {
            return PageResult.of(result);
        }
        result.forEach(index -> {
            Set<Long> roles = userRoleRepository.findUserRoleEntitiesByUserId(index.getId())
                    .stream().map(UserRoleEntity::getRoleId).collect(Collectors.toSet());
            String roleNames = roleRepository.findAllById(roles).stream()
                    .map(RoleEntity::getRoleName).collect(Collectors.joining(","));
            index.setRoles(roleNames);
//            UserSocialUserEntity userSocialUserEntity = userSocialUserRepository.findByUserId(index.getId());

//            ProvinceTenantEntity provinceTenant = provinceTenantRepository.findById(index.getProvinceId()).orElse(null);
//            if (null!=provinceTenant) {
//                index.setNodeName(provinceTenant.getNodeName());
//            }

        });
        return PageResult.of(result);
    }

    @Override
    public void bindHandleUser(UserDto userDto) {
        UserEntity orgEntity = userRepository.findById(userDto.getId())
                .orElseThrow(() -> new BusinessRuntimeException(BusinessCodeMessage.DATA_NOT_EXIST));
        orgEntity.setHandleUser(userDto.getHandleUser());
        orgEntity.setUpdatedBy(SecurityUtil.getCurrentUserId());
        userRepository.save(orgEntity);
    }

    @Override
    public void unbindHandleUser(UserDto userDto) {
        UserEntity orgEntity = userRepository.findById(userDto.getId())
                .orElseThrow(() -> new BusinessRuntimeException(BusinessCodeMessage.DATA_NOT_EXIST));
        orgEntity.setHandleUser("");
        orgEntity.setUpdatedBy(SecurityUtil.getCurrentUserId());
        userRepository.save(orgEntity);
    }

    @Override
    public UserVo getUserDetail(Long id) {
        UserEntity orgEntity = userRepository.findById(id)
                .orElseThrow(() -> new BusinessRuntimeException(BusinessCodeMessage.DATA_NOT_EXIST));
        UserVo userVo = new UserVo();
        BeanUtils.copyProperties(orgEntity, userVo);
        Set<Long> roles = userRoleRepository.findUserRoleEntitiesByUserId(id)
                .stream().map(UserRoleEntity::getRoleId).collect(Collectors.toSet());
        List<UserVo.RoleInfo> roleInfoList = roleRepository.findAllById(roles).stream()
                .map(index -> new UserVo.RoleInfo(index.getId(), index.getRoleName(), index.getRoleType())).collect(Collectors.toList());
        userVo.setRoleInfos(roleInfoList);

        userVo.setLevelType(Objects.nonNull(SecurityUtil.getCurrentEntId())
                ? LevelTypeEnum.ENT.getCode() : LevelTypeEnum.PROVINCE.getCode());

        if (ObjectUtil.isNotEmpty(userVo.getEntId())){
            userVo.setOrgName(entRepository.findById(userVo.getEntId()).orElseThrow(()->new BusinessRuntimeException(BusinessCodeMessage.ENT_NOT_EXIST)).getOrgName());
        }
        if (BusinessConstant.APP_ROLE.equals(roleInfoList.get(0).getRoleType())){
            UserAppEntity userApp = userAppRepository.findByUserId(id);
            userVo.setAppId(userApp.getAppId());
            userVo.setAppName(appInfoRepository.findById(userApp.getAppId()).orElseThrow(()->new BusinessRuntimeException(BusinessCodeMessage.APP_NOT_EXIST)).getAppName());
        }

        return userVo;
    }

    @Override
    public UserEntity getUserDetailByEmailAndProvinceId(String email, Long provinceId) {
        UserEntity userEntity = userRepository.findByEmailAndProvinceId(email, provinceId);
        if (null==userEntity) {
            return null;
        }
        return userEntity;
    }

    public UserEntity getUserDetailByEmail(String  email) {
        UserEntity userEntity = userRepository.findByEmail(email);
        if (null==userEntity) {
            return null;
        }
        return userEntity;
    }

    @Override
    public UserVo getUserInfo(Long id) {
        UserEntity orgEntity = userRepository.findById(id)
                .orElseThrow(() -> new BusinessRuntimeException(BusinessCodeMessage.DATA_NOT_EXIST));
        UserVo userVo = new UserVo();
        BeanUtils.copyProperties(orgEntity, userVo);
        Set<Long> roles = userRoleRepository.findUserRoleEntitiesByUserId(id)
                .stream().map(UserRoleEntity::getRoleId).collect(Collectors.toSet());
        List<RoleEntity> roleEntities = roleRepository.findAllById(roles);
        List<UserVo.RoleInfo> roleInfoList = roleEntities.stream()
                .map(index -> new UserVo.RoleInfo(index.getId(), index.getRoleName(), index.getRoleType())).collect(Collectors.toList());
        userVo.setRoleInfos(roleInfoList);

        userVo.setLevelType(Objects.nonNull(SecurityUtil.getCurrentEntId())
                ? LevelTypeEnum.ENT.getCode() : LevelTypeEnum.PROVINCE.getCode());

        Set<Long> collect = roleEntities.stream()
                .map(RoleEntity::getId).collect(Collectors.toSet());
        userVo.setAuthTree(authService.getAuthTreeByRoleIds(collect,mpDmmEnable));

        userVo.setIsHosting(entPrefixHostingApplyRepository.existsByEntIdAndHostingState(SecurityUtil.getCurrentEntId(),HostingApplyEnum.HOSTING.getCode()));

        UserAppEntity userAppEntity = userAppRepository.findByUserId(orgEntity.getId());
        if (null!=userAppEntity&&null!=userAppEntity.getAppId()) {
            Long appId = userAppEntity.getAppId();
            AppInfoEntity appInfoEntity = appInfoRepository.findById(appId).orElse(null);
            if (null!=appInfoEntity) {
                userVo.setAppName(appInfoEntity.getAppName());
                userVo.setAppType(appInfoEntity.getAppType());
            }
        }

        //增加30天修改密码的开关，和天数的判断
        LoginConfigVO detail = loginConfigService.detail();
        //如果为0，则为不限制
        Integer changePasswordDays = detail.getChangePasswordTime();
        if (null!=changePasswordDays&&0!=changePasswordDays) {
            //判断是否需要修改密码
            LocalDateTime passwordResetTime = orgEntity.getPasswordResetTime();
            if (null==passwordResetTime) {
                passwordResetTime = orgEntity.getCreatedTime();
            }
            LocalDateTime offsetDateTime = LocalDateTimeUtil.offset(passwordResetTime, changePasswordDays, ChronoUnit.DAYS);
            boolean needUpdatePassword = offsetDateTime.isBefore(LocalDateTime.now());
            userVo.setUpdatePasswordType(needUpdatePassword? UpdatePasswordTypeEnum.UPDATE_PASSWORD_TYPE_ENUM_TOO_LONG_UPDATE.ordinal() :UpdatePasswordTypeEnum.UPDATE_PASSWORD_TYPE_ENUM_NONE.ordinal());
        }

        return userVo;
    }


    @Override
    @Transactional(rollbackFor = Exception.class)
    public void removeUser(Long id) {
        // 删除用户表
        userRepository.deleteById(id);
        // 删除用户关联角色表
        userRoleRepository.deleteByUserId(id);
        
        //若系统用户有关联业务中台用户，则解除绑定关系
        UserSocialUserEntity userSocialUserEntity = userSocialUserRepository.findByUserId(id);
        if (ObjectUtils.isNotEmpty(userSocialUserEntity)) {
            //删除用户与中台用户关联信息
            userSocialUserRepository.deleteById(userSocialUserEntity.getId());
//            socialUserService.unbinding(userSocialUserEntity.getSocialUserId());
        }
    }

    @Override
    public List<EntDropDownVO> entDropDowntList() {
        Long currentProvinceId = SecurityUtil.getCurrentProvinceId();
        List<EntEntity> entEntityList = entRepository.findAllByProvinceId(currentProvinceId);
        List<EntDropDownVO> entDropDownVOList = entEntityList.stream()
                .filter(index -> StringUtils.isNotBlank(index.getOrgName()) && StringUtils.isNotBlank(index.getOrgCode()) )
                .map(entEntity -> {
                    EntDropDownVO entDropDownVO = new EntDropDownVO();
                    BeanUtils.copyProperties(entEntity, entDropDownVO);
                    return entDropDownVO;
                }).collect(Collectors.toList());
        return entDropDownVOList;
    }
}
