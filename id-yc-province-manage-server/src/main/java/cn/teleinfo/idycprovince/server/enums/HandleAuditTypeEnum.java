package cn.teleinfo.idycprovince.server.enums;

import lombok.Getter;

// 申请类型 0 注册 1 编辑 2 删除
// 审核类型 0 注册 1 编辑 2 删除
@Getter
public enum HandleAuditTypeEnum {

    CREATE(0, "注册"),
    UPDATE(1, "编辑"),
    DEL(2, "删除"),
    NOT(3, "未修改");

    private final int code;
    private final String desc;

    HandleAuditTypeEnum(int code, String desc) {
        this.code = code;
        this.desc = desc;
    }

    public static HandleAuditTypeEnum findByCode(int code) {
        for (HandleAuditTypeEnum handleAuditTypeEnum : HandleAuditTypeEnum.values()) {
            if (handleAuditTypeEnum.getCode() == code) {
                return handleAuditTypeEnum;
            }
        }
        return null;
    }
}
