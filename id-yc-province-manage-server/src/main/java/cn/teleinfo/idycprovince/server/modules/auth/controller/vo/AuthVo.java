package cn.teleinfo.idycprovince.server.modules.auth.controller.vo;

import cn.teleinfo.idycprovince.infrastructure.db.entity.AuthEntity;
import cn.teleinfo.idycprovince.infrastructure.db.model.TreeModel;
import com.fasterxml.jackson.annotation.JsonIgnore;
import com.fasterxml.jackson.annotation.JsonInclude;
import lombok.Data;

@Data
public class AuthVo extends TreeModel {

    @JsonInclude(JsonInclude.Include.NON_EMPTY)
    private Long id;

    @JsonInclude(JsonInclude.Include.NON_EMPTY)
    private String authCode;

    private String authName;

    @JsonInclude(JsonInclude.Include.NON_EMPTY)
    private String authDesc;

    private Long parentId;

    private Integer authType;
    @JsonIgnore
    private String levelType;

    @JsonInclude(JsonInclude.Include.NON_EMPTY)
    private Integer selected;

    public static AuthVo transfer(AuthEntity authTreeEntity) {
        AuthVo authVo = new AuthVo();
        authVo.setAuthCode(authTreeEntity.getAuthCode());
        authVo.setAuthName(authTreeEntity.getAuthName());
        authVo.setAuthDesc(authTreeEntity.getAuthDesc());
        authVo.setParentId(authTreeEntity.getParentId());
        authVo.setId(authTreeEntity.getId());
        authVo.setLevelType(authTreeEntity.getLevelType());
        return authVo;
    }
}
