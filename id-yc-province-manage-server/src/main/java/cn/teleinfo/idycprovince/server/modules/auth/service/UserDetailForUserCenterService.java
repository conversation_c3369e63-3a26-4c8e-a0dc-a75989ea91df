package cn.teleinfo.idycprovince.server.modules.auth.service;/*
 * File: AppUserDetailService.java
 * <AUTHOR>
 * @since 2022-07-01
 *
 * Copyright (c) 2022 abluepoint teleinfo All Rights Reserved.
 */

import cn.hutool.core.util.ObjectUtil;
import cn.hutool.core.util.StrUtil;
import cn.teleinfo.idycprovince.common.constant.BusinessConstant;
import cn.teleinfo.idycprovince.common.exception.BusinessCodeMessage;
import cn.teleinfo.idycprovince.infrastructure.db.entity.AuthEntity;
import cn.teleinfo.idycprovince.infrastructure.db.entity.RoleAuthEntity;
import cn.teleinfo.idycprovince.infrastructure.db.entity.RoleEntity;
import cn.teleinfo.idycprovince.infrastructure.db.entity.UserAppEntity;
import cn.teleinfo.idycprovince.infrastructure.db.entity.UserEntity;
import cn.teleinfo.idycprovince.infrastructure.db.entity.UserRoleEntity;
import cn.teleinfo.idycprovince.infrastructure.db.repository.AuthRepository;
import cn.teleinfo.idycprovince.infrastructure.db.repository.RoleAuthRepository;
import cn.teleinfo.idycprovince.infrastructure.db.repository.RoleRepository;
import cn.teleinfo.idycprovince.infrastructure.db.repository.UserAppRepository;
import cn.teleinfo.idycprovince.infrastructure.db.repository.UserRepository;
import cn.teleinfo.idycprovince.infrastructure.db.repository.UserRoleRepository;
import cn.teleinfo.idycprovince.infrastructure.redis.RedisCache;
import cn.teleinfo.idycprovince.server.modules.auth.model.UserDetail;
import cn.teleinfo.idycprovince.server.modules.auth.service.auth.SocialUserServiceInterface;
import com.abluepoint.summer.mvc.exception.BusinessRuntimeException;
import lombok.RequiredArgsConstructor;
import org.springframework.security.core.GrantedAuthority;
import org.springframework.security.core.authority.SimpleGrantedAuthority;
import org.springframework.security.core.userdetails.UserDetails;
import org.springframework.security.core.userdetails.UsernameNotFoundException;
import org.springframework.stereotype.Service;

import java.util.HashSet;
import java.util.List;
import java.util.Set;
import java.util.stream.Collectors;

/**
 * <AUTHOR>
 */
@RequiredArgsConstructor
@Service
public class UserDetailForUserCenterService {

    private final UserRepository userRepository;
    private final UserRoleRepository userRoleRepository;
    private final RoleRepository roleRepository;
    private final UserAppRepository userAppRepository;
    private final SocialUserServiceInterface socialUserService;
    private final RoleAuthRepository roleAuthRepository;
    private final AuthRepository authRepository;
    private final RedisCache redisCache;

    public UserDetails loadUserByUsername(String username) throws UsernameNotFoundException {
        UserEntity userEntity = userRepository.findById(socialUserService.userCenterLogin(username))
                .orElseThrow(() -> new BusinessRuntimeException(BusinessCodeMessage.USER_NOT_EXIST));
        //判断登录用户是否属于应用账号，为应用账号则赋值应用id
        Set<Long> roles = userRoleRepository.findUserRoleEntitiesByUserId(userEntity.getId())
                .stream().map(UserRoleEntity::getRoleId).collect(Collectors.toSet());
        List<RoleEntity> roleEntities = roleRepository.findAllById(roles);
        UserAppEntity userAppEntity = null;
        if (roleEntities.stream().anyMatch(index -> ObjectUtil.equals(index.getRoleType(), BusinessConstant.APP_ROLE))) {
            //角色为企业应用角色,查询关联应用信息
            userAppEntity = userAppRepository.findByUserId(userEntity.getId());
        }
        //由角色查询出接口权限
        Set<Long> auths = roleAuthRepository.findRoleAuthEntitiesByRoleIdIn(roles).stream()
                .map(RoleAuthEntity::getAuthId).collect(Collectors.toSet());
        List<AuthEntity> authList = authRepository.findAllById(auths);
        Set<GrantedAuthority> authorities = new HashSet<>();
        authList.forEach(auth -> {
            String authPermission = auth.getAuthPermission();
            if (StrUtil.isNotBlank(authPermission)) {
                String[] permissions = authPermission.split(",");
                for (String permission : permissions) {
                    authorities.add(new SimpleGrantedAuthority(permission));
                }
            }
        });
    
        //处理账户是否锁定
        String accountKey = new StringBuilder().append(BusinessConstant.USER_LOGIN_ERROR).append(":").append(username).toString();
        boolean accountNonLocked = !redisCache.exists(accountKey);

        // @format off
        UserDetail userDetail = UserDetail.builder()
                .userId(userEntity.getId())
                .nickName(userEntity.getNickName())
                .username(userEntity.getUsername())
                .password(userEntity.getPassword())
                .accountNonExpired(true)
                .accountNonLocked(accountNonLocked)
                .credentialsNonExpired(true)
                .enabled(true)
                .authorities(authorities)
                .provinceId(userEntity.getProvinceId())
                .entId(userEntity.getEntId())
                .appId(ObjectUtil.isNotEmpty(userAppEntity) ? userAppEntity.getAppId() : null)
                .build();
        
        // @format on
        return userDetail;
    }

}
