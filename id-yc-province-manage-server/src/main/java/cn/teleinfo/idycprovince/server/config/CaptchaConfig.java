package cn.teleinfo.idycprovince.server.config;

import cn.teleinfo.idycprovince.infrastructure.redis.RedisCache;
import cn.teleinfo.idycprovince.infrastructure.redis.RedisTobaccoCache;
import com.anji.captcha.model.common.CaptchaTypeEnum;
import com.anji.captcha.model.common.Const;
import com.anji.captcha.service.CaptchaCacheService;
import com.anji.captcha.service.CaptchaService;
import com.anji.captcha.service.impl.BlockPuzzleCaptchaServiceImpl;
import com.anji.captcha.service.impl.CaptchaCacheServiceMemImpl;
import com.anji.captcha.util.ImageUtils;
import com.anji.captcha.util.StringUtils;
import lombok.extern.slf4j.Slf4j;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.boot.autoconfigure.condition.ConditionalOnProperty;
import org.springframework.boot.context.properties.ConfigurationProperties;
import org.springframework.boot.context.properties.EnableConfigurationProperties;
import org.springframework.context.annotation.Bean;
import org.springframework.context.annotation.Configuration;
import org.springframework.core.io.Resource;
import org.springframework.core.io.support.PathMatchingResourcePatternResolver;
import org.springframework.core.io.support.ResourcePatternResolver;
import org.springframework.util.Base64Utils;
import org.springframework.util.FileCopyUtils;

import java.awt.*;
import java.util.HashMap;
import java.util.Map;
import java.util.Properties;

import static cn.teleinfo.idycprovince.server.config.CaptchaConfig.CaptchaProperties.StorageType.local;

/*
 * File: CaptchaConfig.java
 * <AUTHOR>
 * @since 2022-07-08
 *
 * Copyright (c) 2022 abluepoint teleinfo All Rights Reserved.
 */

@Configuration
@EnableConfigurationProperties(CaptchaConfig.CaptchaProperties.class)
@Slf4j
public class CaptchaConfig {

    private static final Logger logger = LoggerFactory.getLogger(CaptchaConfig.class);

    @Bean
    public CaptchaService captchaService(CaptchaProperties prop) {
        logger.info("自定义配置项：{}", prop.toString());
        Properties config = new Properties();
        config.put(Const.CAPTCHA_CACHETYPE, prop.getCacheType().name());
        config.put(Const.CAPTCHA_WATER_MARK, prop.getWaterMark());
        config.put(Const.CAPTCHA_FONT_TYPE, prop.getFontType());
        config.put(Const.CAPTCHA_TYPE, prop.getType().getCodeValue());
        config.put(Const.CAPTCHA_INTERFERENCE_OPTIONS, prop.getInterferenceOptions());
        config.put(Const.ORIGINAL_PATH_JIGSAW, prop.getJigsaw());
        config.put(Const.ORIGINAL_PATH_PIC_CLICK, prop.getPicClick());
        config.put(Const.CAPTCHA_SLIP_OFFSET, prop.getSlipOffset());
        config.put(Const.CAPTCHA_AES_STATUS, String.valueOf(prop.getAesStatus()));
        config.put(Const.CAPTCHA_WATER_FONT, prop.getWaterFont());
        config.put(Const.CAPTCHA_CACAHE_MAX_NUMBER, prop.getCacheNumber());
        config.put(Const.CAPTCHA_TIMING_CLEAR_SECOND, prop.getTimingClear());

        config.put(Const.HISTORY_DATA_CLEAR_ENABLE, prop.isHistoryDataClearEnable() ? "1" : "0");

        config.put(Const.REQ_FREQUENCY_LIMIT_ENABLE, prop.getReqFrequencyLimitEnable() ? "1" : "0");
        config.put(Const.REQ_GET_LOCK_LIMIT, prop.getReqGetLockLimit() + "");
        config.put(Const.REQ_GET_LOCK_SECONDS, prop.getReqGetLockSeconds() + "");
        config.put(Const.REQ_GET_MINUTE_LIMIT, prop.getReqGetMinuteLimit() + "");
        config.put(Const.REQ_CHECK_MINUTE_LIMIT, prop.getReqCheckMinuteLimit() + "");
        config.put(Const.REQ_VALIDATE_MINUTE_LIMIT, prop.getReqVerifyMinuteLimit() + "");

        config.put(Const.CAPTCHA_FONT_SIZE, prop.getFontSize() + "");
        config.put(Const.CAPTCHA_FONT_STYLE, prop.getFontStyle() + "");
        config.put(Const.CAPTCHA_WORD_COUNT, prop.getClickWordCount() + "");

        if ((StringUtils.isNotBlank(prop.getJigsaw()) && prop.getJigsaw().startsWith("classpath:"))
                || (StringUtils.isNotBlank(prop.getPicClick()) && prop.getPicClick().startsWith("classpath:"))) {
            //自定义resources目录下初始化底图
            config.put(Const.CAPTCHA_INIT_ORIGINAL, "true");
            initializeBaseMap(prop.getJigsaw(), prop.getPicClick());
        }

        CaptchaService ret = new BlockPuzzleCaptchaServiceImpl();
        ret.init(config);
        return ret;
    }

    @ConditionalOnProperty(prefix = "spring.cache", name = "type", havingValue = "simple")
    @Bean(name = "AjCaptchaCacheService")
    public CaptchaCacheService captchaCacheService(CaptchaProperties ajCaptchaProperties) {
        return new CaptchaCacheServiceMemImpl();
    }

    @ConditionalOnProperty(prefix = "spring.cache", name = "type", havingValue = "redis")
    @Bean(name = "AjCaptchaCacheService")
    public CaptchaCacheService captchaRedisCacheService(CaptchaProperties ajCaptchaProperties, RedisCache redisCache) {
        return new CaptchaCacheServiceRedisImpl(redisCache);
    }

    private static void initializeBaseMap(String jigsaw, String picClick) {
        ImageUtils.cacheBootImage(getResourcesImagesFile(jigsaw + "/original/*.png"),
                getResourcesImagesFile(jigsaw + "/slidingBlock/*.png"),
                getResourcesImagesFile(picClick + "/*.png"));
    }

    public static Map<String, String> getResourcesImagesFile(String path) {
        Map<String, String> imgMap = new HashMap<>();
        ResourcePatternResolver resolver = new PathMatchingResourcePatternResolver();
        try {
            Resource[] resources = resolver.getResources(path);
            for (Resource resource : resources) {
                byte[] bytes = FileCopyUtils.copyToByteArray(resource.getInputStream());
                String string = Base64Utils.encodeToString(bytes);
                String filename = resource.getFilename();
                imgMap.put(filename, string);
            }
        } catch (Exception e) {
            log.error(e.getMessage(), e);
        }
        return imgMap;
    }

    /**
     * 对于分布式部署的应用，我们建议应用自己实现CaptchaCacheService，比如用Redis，参考service/spring-boot代码示例。
     * 如果应用是单点的，也没有使用redis，那默认使用内存。
     * 内存缓存只适合单节点部署的应用，否则验证码生产与验证在节点之间信息不同步，导致失败。
     * <p>
     * ☆☆☆ SPI： 在resources目录新建META-INF.services文件夹(两层)，参考当前服务resources。
     *
     * <AUTHOR>
     * @Title: 使用redis缓存
     * @date 2020-05-12
     */
    public static class CaptchaCacheServiceRedisImpl implements CaptchaCacheService {

        @Override
        public String type() {
            return "redis";
        }

        //        public CaptchaCacheServiceRedisImpl(StringRedisTemplate stringRedisTemplate) {
//            this.stringRedisTemplate = stringRedisTemplate;
//        }
        public CaptchaCacheServiceRedisImpl(RedisCache redisCache) {
            this.redisCache = redisCache;
        }

        private final RedisCache redisCache;
//        private final StringRedisTemplate stringRedisTemplate;

        @Override
        public void set(String key, String value, long expiresInSeconds) {
//            stringRedisTemplate.opsForValue().set(key, value, expiresInSeconds, TimeUnit.SECONDS);
            redisCache.set(key, value, (int) expiresInSeconds);
        }

        @Override
        public boolean exists(String key) {
//            return stringRedisTemplate.hasKey(key);
            return redisCache.exists(key);
        }

        @Override
        public void delete(String key) {
//            stringRedisTemplate.delete(key);
            redisCache.delete(key);
        }

        @Override
        public String get(String key) {
            return redisCache.get(key);
//            return stringRedisTemplate.opsForValue().get(key);
        }

        @Override
        public Long increment(String key, long val) {
            return redisCache.increment(key, (int) val);
//            return stringRedisTemplate.opsForValue().increment(key, val);
        }
    }

    @ConfigurationProperties("app.captcha")
    public static class CaptchaProperties {

        /**
         * 验证码类型.
         */
        private CaptchaTypeEnum type = CaptchaTypeEnum.DEFAULT;

        /**
         * 滑动拼图底图路径.
         */
        private String jigsaw = "";

        /**
         * 点选文字底图路径.
         */
        private String picClick = "";


        /**
         * 右下角水印文字(我的水印).
         */
        private String waterMark = "我的水印";

        /**
         * 右下角水印字体(文泉驿正黑).
         */
        private String waterFont = "WenQuanZhengHei.ttf";

        /**
         * 点选文字验证码的文字字体(文泉驿正黑).
         */
        private String fontType = "WenQuanZhengHei.ttf";

        /**
         * 校验滑动拼图允许误差偏移量(默认5像素).
         */
        private String slipOffset = "5";

        /**
         * aes加密坐标开启或者禁用(true|false).
         */
        private Boolean aesStatus = true;

        /**
         * 滑块干扰项(0/1/2)
         */
        private String interferenceOptions = "0";

        /**
         * local缓存的阈值
         */
        private String cacheNumber = "1000";

        /**
         * 定时清理过期local缓存(单位秒)
         */
        private String timingClear = "180";

        /**
         * 缓存类型redis/local/....
         */
        private StorageType cacheType = local;
        /**
         * 历史数据清除开关
         */
        private boolean historyDataClearEnable = false;

        /**
         * 一分钟内接口请求次数限制 开关
         */
        private boolean reqFrequencyLimitEnable = false;

        /***
         * 一分钟内check接口失败次数
         */
        private int reqGetLockLimit = 5;
        /**
         *
         */
        private int reqGetLockSeconds = 300;

        /***
         * get接口一分钟内限制访问数
         */
        private int reqGetMinuteLimit = 100;
        private int reqCheckMinuteLimit = 100;
        private int reqVerifyMinuteLimit = 100;

        /**
         * 点选字体样式
         */
        private int fontStyle = Font.BOLD;

        /**
         * 点选字体大小
         */
        private int fontSize = 25;

        /**
         * 点选文字个数，存在问题，暂不要使用
         */
        private int clickWordCount = 4;

        public int getFontStyle() {
            return fontStyle;
        }

        public void setFontStyle(int fontStyle) {
            this.fontStyle = fontStyle;
        }

        public int getFontSize() {
            return fontSize;
        }

        public void setFontSize(int fontSize) {
            this.fontSize = fontSize;
        }

        public int getClickWordCount() {
            return clickWordCount;
        }

        public void setClickWordCount(int clickWordCount) {
            this.clickWordCount = clickWordCount;
        }

        public boolean isHistoryDataClearEnable() {
            return historyDataClearEnable;
        }

        public void setHistoryDataClearEnable(boolean historyDataClearEnable) {
            this.historyDataClearEnable = historyDataClearEnable;
        }

        public boolean isReqFrequencyLimitEnable() {
            return reqFrequencyLimitEnable;
        }

        public boolean getReqFrequencyLimitEnable() {
            return reqFrequencyLimitEnable;
        }

        public void setReqFrequencyLimitEnable(boolean reqFrequencyLimitEnable) {
            this.reqFrequencyLimitEnable = reqFrequencyLimitEnable;
        }

        public int getReqGetLockLimit() {
            return reqGetLockLimit;
        }

        public void setReqGetLockLimit(int reqGetLockLimit) {
            this.reqGetLockLimit = reqGetLockLimit;
        }

        public int getReqGetLockSeconds() {
            return reqGetLockSeconds;
        }

        public void setReqGetLockSeconds(int reqGetLockSeconds) {
            this.reqGetLockSeconds = reqGetLockSeconds;
        }

        public int getReqGetMinuteLimit() {
            return reqGetMinuteLimit;
        }

        public void setReqGetMinuteLimit(int reqGetMinuteLimit) {
            this.reqGetMinuteLimit = reqGetMinuteLimit;
        }

        public int getReqCheckMinuteLimit() {
            return reqGetMinuteLimit;
        }

        public void setReqCheckMinuteLimit(int reqCheckMinuteLimit) {
            this.reqCheckMinuteLimit = reqCheckMinuteLimit;
        }

        public int getReqVerifyMinuteLimit() {
            return reqVerifyMinuteLimit;
        }

        public void setReqVerifyMinuteLimit(int reqVerifyMinuteLimit) {
            this.reqVerifyMinuteLimit = reqVerifyMinuteLimit;
        }

        public enum StorageType {
            /**
             * 内存.
             */
            local,
            /**
             * redis.
             */
            redis,
            /**
             * 其他.
             */
            other,
        }


        public CaptchaTypeEnum getType() {
            return type;
        }

        public void setType(CaptchaTypeEnum type) {
            this.type = type;
        }

        public String getJigsaw() {
            return jigsaw;
        }

        public void setJigsaw(String jigsaw) {
            this.jigsaw = jigsaw;
        }

        public String getPicClick() {
            return picClick;
        }

        public void setPicClick(String picClick) {
            this.picClick = picClick;
        }

        public String getWaterMark() {
            return waterMark;
        }

        public void setWaterMark(String waterMark) {
            this.waterMark = waterMark;
        }

        public String getWaterFont() {
            return waterFont;
        }

        public void setWaterFont(String waterFont) {
            this.waterFont = waterFont;
        }

        public String getFontType() {
            return fontType;
        }

        public void setFontType(String fontType) {
            this.fontType = fontType;
        }

        public String getSlipOffset() {
            return slipOffset;
        }

        public void setSlipOffset(String slipOffset) {
            this.slipOffset = slipOffset;
        }

        public Boolean getAesStatus() {
            return aesStatus;
        }

        public void setAesStatus(Boolean aesStatus) {
            this.aesStatus = aesStatus;
        }

        public StorageType getCacheType() {
            return cacheType;
        }

        public void setCacheType(StorageType cacheType) {
            this.cacheType = cacheType;
        }

        public String getInterferenceOptions() {
            return interferenceOptions;
        }

        public void setInterferenceOptions(String interferenceOptions) {
            this.interferenceOptions = interferenceOptions;
        }

        public String getCacheNumber() {
            return cacheNumber;
        }

        public void setCacheNumber(String cacheNumber) {
            this.cacheNumber = cacheNumber;
        }

        public String getTimingClear() {
            return timingClear;
        }

        public void setTimingClear(String timingClear) {
            this.timingClear = timingClear;
        }

        @Override
        public String toString() {
            return "\nAjCaptchaProperties{" +
                    "type=" + type +
                    ", jigsaw='" + jigsaw + '\'' +
                    ", picClick='" + picClick + '\'' +
                    ", waterMark='" + waterMark + '\'' +
                    ", waterFont='" + waterFont + '\'' +
                    ", fontType='" + fontType + '\'' +
                    ", slipOffset='" + slipOffset + '\'' +
                    ", aesStatus=" + aesStatus +
                    ", interferenceOptions='" + interferenceOptions + '\'' +
                    ", cacheNumber='" + cacheNumber + '\'' +
                    ", timingClear='" + timingClear + '\'' +
                    ", cacheType=" + cacheType +
                    ", reqFrequencyLimitEnable=" + reqFrequencyLimitEnable +
                    ", reqGetLockLimit=" + reqGetLockLimit +
                    ", reqGetLockSeconds=" + reqGetLockSeconds +
                    ", reqGetMinuteLimit=" + reqGetMinuteLimit +
                    ", reqCheckMinuteLimit=" + reqCheckMinuteLimit +
                    ", reqVerifyMinuteLimit=" + reqVerifyMinuteLimit +
                    '}';
        }
    }


}
