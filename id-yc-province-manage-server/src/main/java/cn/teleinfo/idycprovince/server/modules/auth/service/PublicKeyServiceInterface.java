package cn.teleinfo.idycprovince.server.modules.auth.service;/*
 * File: PublicKeyService.java
 * <AUTHOR>
 * @since 2022-07-03
 *
 * Copyright (c) 2022 abluepoint teleinfo All Rights Reserved.
 */

import java.io.Serializable;
import java.security.NoSuchAlgorithmException;
import java.security.PrivateKey;

public interface PublicKeyServiceInterface {


    KeyInfo getKeyInfo() throws NoSuchAlgorithmException;

    PrivateKey getPrivateKey() throws Exception;

    public static class KeyInfo implements Serializable {
        private String publicKeyPem;
        private String privateKeyPem;
        private int ttl;
        private Long timestamp;

        public KeyInfo() {

        }

        public KeyInfo(String publicKeyPem, String privateKeyPem, int ttl, Long timestamp) {
            this.publicKeyPem = publicKeyPem;
            this.privateKeyPem = privateKeyPem;
            this.ttl = ttl;
            this.timestamp = timestamp;
        }

        public String getPublicKeyPem() {
            return publicKeyPem;
        }

        public String getPrivateKeyPem() {
            return privateKeyPem;
        }

        public int getTtl() {
            return ttl;
        }

        public Long getTimestamp() {
            return timestamp;
        }


        public void setPublicKeyPem(String publicKeyPem) {
            this.publicKeyPem = publicKeyPem;
        }

        public void setPrivateKeyPem(String privateKeyPem) {
            this.privateKeyPem = privateKeyPem;
        }

        public void setTtl(int ttl) {
            this.ttl = ttl;
        }

        public void setTimestamp(Long timestamp) {
            this.timestamp = timestamp;
        }
    }
}
