package cn.teleinfo.idycprovince.server.feign.dmm.impl;

import cn.teleinfo.idycprovince.server.feign.dmm.IDmmClient;
import com.alibaba.bizworks.core.runtime.common.MultiResponse;
import com.alibaba.bizworks.core.runtime.common.SingleResponse;
import com.fasterxml.jackson.core.type.TypeReference;
import com.fasterxml.jackson.databind.ObjectMapper;
import com.tobacco.mp.dmm.client.api.irs.dto.CategorySimpleDTO;
import com.tobacco.mp.dmm.client.api.irs.dto.DataModelDTO;
import com.tobacco.mp.dmm.client.api.irs.dto.EntityObjectDTO;
import com.tobacco.mp.dmm.client.api.irs.dto.InfoCategoryDTO;
import com.tobacco.mp.dmm.client.api.irs.req.PageQueryCategorySimpleRequest;
import com.tobacco.mp.dmm.client.api.irs.req.QueryObjectRequest;
import com.tobacco.mp.dmm.client.api.irs.req.QuerySystemObjectRequest;
import lombok.SneakyThrows;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.boot.autoconfigure.condition.ConditionalOnProperty;
import org.springframework.core.io.Resource;
import org.springframework.core.io.ResourceLoader;
import org.springframework.stereotype.Component;

import java.util.ArrayList;
import java.util.List;

/**
 * 通过读resource目录下的json文件mock数据中台sdk
 */
@Component
@ConditionalOnProperty(prefix = "app.mock", name = "enable-dmm", havingValue = "true")
public class DmmMockClientImpl implements IDmmClient {

    @Autowired
    ResourceLoader resourceLoader;

    @Autowired
    private ObjectMapper objectMapper;

    @SneakyThrows
    @Override
    public MultiResponse<CategorySimpleDTO> pageSystemCategory(PageQueryCategorySimpleRequest pageQueryCategorySimpleRequest) {
        Resource resource = resourceLoader.getResource("classpath:mock/dmm/app-system-tree.json");
        return objectMapper.readValue(resource.getInputStream(),  new TypeReference<MultiResponse<CategorySimpleDTO>>() {});
    }

    @SneakyThrows
    @Override
    public SingleResponse<InfoCategoryDTO> getEntityObjectClassifyTree() {
        Resource resource = resourceLoader.getResource("classpath:mock/dmm/entity-object-classify-tree.json");
        return objectMapper.readValue(resource.getInputStream(), new TypeReference<SingleResponse<InfoCategoryDTO>>() {});
    }

    @SneakyThrows
    @Override
    public MultiResponse<EntityObjectDTO> getSystemSingleEntityObject(QuerySystemObjectRequest querySystemObjectRequest) {
        Resource resource = resourceLoader.getResource("classpath:mock/dmm/entity-object-list.json");
        return objectMapper.readValue(resource.getInputStream(), new TypeReference<MultiResponse<EntityObjectDTO>>() {});
    }

    @SneakyThrows
    @Override
    public SingleResponse<EntityObjectDTO> getEntityObjectInfoByCode(QueryObjectRequest queryObjectRequest) {
        Resource resource = resourceLoader.getResource("classpath:mock/dmm/entity-object-detail.json");
        return objectMapper.readValue(resource.getInputStream(), new TypeReference<SingleResponse<EntityObjectDTO>>() {});
    }

    @SneakyThrows
    @Override
    public MultiResponse<DataModelDTO> getDataModelAndColumnByModelIds(List<String> ids) {
        Resource resource;
        if (ids.contains("1544993910249209856")) {
            resource = resourceLoader.getResource("classpath:mock/dmm/basic-info-data-model-detail.json");
        } else if (ids.contains("1545236149455339520")) {
            resource = resourceLoader.getResource("classpath:mock/dmm/pack-data-model-detail.json");
        } else if (ids.contains("1531143839113457664")) {
            resource = resourceLoader.getResource("classpath:mock/dmm/price-data-model-detail.json");
        } else {
            return MultiResponse.buildSuccess(new ArrayList<>());
        }
        return objectMapper.readValue(resource.getInputStream(), new TypeReference<MultiResponse<DataModelDTO>>() {});
    }
}
