package cn.teleinfo.idycprovince.server.modules.auth.controller;

import cn.teleinfo.idycprovince.common.constant.RcEnum;
import cn.teleinfo.idycprovince.server.modules.auth.service.CachedPublicKeyService;
import cn.teleinfo.idycprovince.server.modules.auth.service.PublicKeyServiceInterface;
import com.abluepoint.summer.mvc.domain.R;
import com.abluepoint.summer.mvc.domain.Result;
import lombok.AllArgsConstructor;
import org.slf4j.Logger;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;

import java.util.HashMap;
import java.util.Map;

/*
 * File: KeyController.java
 * <AUTHOR>
 * @since 2022-06-30
 *
 * Copyright (c) 2022 abluepoint teleinfo All Rights Reserved.
 */
//@OpLog(value = "111111", callback = RestfulLogCallback.class)
@RestController
@RequestMapping("/api/v1/public")
@AllArgsConstructor
public class KeyController {

    private static final Logger log = org.slf4j.LoggerFactory.getLogger(KeyController.class);
    private final PublicKeyServiceInterface publicKeyService;

    @GetMapping("/publicKey")
    public Result<Map<String, Object>> publicKey() {
        try {
            CachedPublicKeyService.KeyInfo keyInfo = publicKeyService.getKeyInfo();
            Map<String, Object> data = new HashMap<>();
            data.put("publicKeyPem", keyInfo.getPublicKeyPem());
            data.put("ttl", keyInfo.getTtl());
            data.put("timestamp", keyInfo.getTimestamp());
            return R.ok(data);
        } catch (Exception e) {
            log.error("key error", e);
        }
        return R.fail(RcEnum.SYSTEM_UNDEFINED_ERROR);
    }

}