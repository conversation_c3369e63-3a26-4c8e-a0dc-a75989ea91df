package cn.teleinfo.idycprovince.server.modules.app.service.impl;

import cn.hutool.core.collection.CollectionUtil;
import cn.hutool.core.lang.tree.Tree;
import cn.hutool.core.lang.tree.TreeNode;
import cn.hutool.core.lang.tree.TreeUtil;
import cn.hutool.core.util.ObjectUtil;
import cn.hutool.core.util.StrUtil;
import cn.teleinfo.idpointer.sdk.client.ValueHelper;
import cn.teleinfo.idpointer.sdk.core.HandleValue;
import cn.teleinfo.idpointer.sdk.util.EncryptionUtils;
import cn.teleinfo.idpointer.sdk.util.KeyConverter;
import cn.teleinfo.idycprovince.common.constant.BusinessConstant;
import cn.teleinfo.idycprovince.common.constant.HandleConstant;
import cn.teleinfo.idycprovince.common.exception.BusinessCodeMessage;
import cn.teleinfo.idycprovince.infrastructure.db.entity.*;
import cn.teleinfo.idycprovince.infrastructure.db.repository.*;
import cn.teleinfo.idycprovince.server.feign.dmm.IDmmClient;
import cn.teleinfo.idycprovince.server.modules.app.dto.AppInfoDTO;
import cn.teleinfo.idycprovince.server.modules.app.dto.AppInfoKeyDTO;
import cn.teleinfo.idycprovince.server.modules.app.service.AppInfoService;
import cn.teleinfo.idycprovince.server.modules.app.to.AppInfoTO;
import cn.teleinfo.idycprovince.server.modules.app.vo.AppInfoVO;
import cn.teleinfo.idycprovince.server.modules.app.vo.EntPrefixListVO;
import cn.teleinfo.idycprovince.server.modules.app.vo.KeyPairVO;
import cn.teleinfo.idycprovince.server.modules.auth.util.SecurityUtil;
import cn.teleinfo.idycprovince.server.modules.handleUser.service.HandleUserService;
import cn.teleinfo.idycprovince.server.modules.idclient.IDClientLhsTemplate;
import cn.teleinfo.idycprovince.server.modules.resolve.service.impl.IdClientService;
import cn.teleinfo.idycprovince.server.modules.statistics.service.StatisticsDataReportService;
import cn.teleinfo.idycprovince.server.modules.system.vo.EntPrefixVO;
import cn.teleinfo.idycprovince.server.modules.util.AESUtil;
import cn.teleinfo.idycprovince.server.modules.util.Assert;
import com.abluepoint.summer.mvc.domain.PageResult;
import com.abluepoint.summer.mvc.exception.BusinessRuntimeException;
import com.alibaba.bizworks.core.runtime.common.MultiResponse;
import com.fasterxml.jackson.core.JsonProcessingException;
import com.fasterxml.jackson.databind.ObjectMapper;
import com.tobacco.mp.dmm.client.api.irs.dto.CategorySimpleDTO;
import com.tobacco.mp.dmm.client.api.irs.req.PageQueryCategorySimpleRequest;
import lombok.AllArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.StringUtils;
import org.springframework.beans.BeanUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.data.domain.Page;
import org.springframework.data.domain.Pageable;
import org.springframework.data.jpa.domain.Specification;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;
import org.springframework.util.Base64Utils;
import org.springframework.util.CollectionUtils;

import javax.persistence.criteria.Predicate;
import java.security.KeyPair;
import java.security.PublicKey;
import java.util.*;
import java.util.stream.Collectors;

@Slf4j
@Service
@AllArgsConstructor
public class AppInfoServiceImpl implements AppInfoService {

    private final AppInfoRepository appInfoRepository;

    private final EntPrefixRepository entPrefixRepository;

    private final ObjectMapper objectMapper;

    private final IDClientLhsTemplate idClientLhsTemplate;

    private final EntRepository entRepository;

    private final HandleRepository handleRepository;

    private final EntPrefixHostingRepository entPrefixHostingRepository;

    private final HandleUserRepository handleUserRepository;

    @Autowired
    private StatisticsDataReportService statisticsDataReportService;
    @Autowired
    private HandleUserService handleUserService;

    private final IdClientService idClientService;

    private final IDmmClient dmmClient;

    @Override
    @Transactional(rollbackFor = Exception.class)
    public AppInfoEntity createAppInfo(AppInfoDTO appInfoDTO) throws Exception {
        if (StringUtils.isNotBlank(appInfoDTO.getAppName())) {
            Assert.assertFalse(appInfoRepository.existsByAppNameAndProvinceIdAndEntId(appInfoDTO.getAppName()
                    , SecurityUtil.getCurrentProvinceId(), SecurityUtil.getCurrentEntId()), BusinessCodeMessage.APP_NAME_IS_EXIST);
        }
        //校验同一个企业下应用名称不能重复
        AppInfoEntity appInfoEntity = appInfoRepository.findByEntIdAndAppName(appInfoDTO.getEntId(), appInfoDTO.getAppName());
        if (ObjectUtil.isNotEmpty(appInfoEntity)) {
            throw new BusinessRuntimeException(BusinessCodeMessage.APP_NAME_IS_EXIST);
        }

        KeyPair keyPair = EncryptionUtils.generateKeyPair();
        //私钥
        String privateKeyPem = KeyConverter.toPkcs8UnencryptedPem(keyPair.getPrivate());
        //公钥
        String publicKeyPem = KeyConverter.toX509Pem(keyPair.getPublic());

        EntPrefixEntity entPrefix = entPrefixRepository.findById(appInfoDTO.getPrefixId()).orElse(null);
        String handleCode = handleUserService.generateAppHandleUser(entPrefix.getEntPrefix());
        AppInfoEntity appInfo = new AppInfoEntity();
        appInfo.setPrefixId(appInfoDTO.getPrefixId());
        appInfo.setAppName(appInfoDTO.getAppName());
        appInfo.setHandleCode(handleCode);
        appInfo.setDeployAddress(appInfoDTO.getDeployAddress());
        appInfo.setSysVersion(appInfoDTO.getSysVersion());
        appInfo.setEntId(appInfoDTO.getEntId());
        appInfo.setProvinceId(SecurityUtil.getCurrentProvinceId());
        appInfo.setIsDeleted(BaseEntity.INIT_STATUS);
        appInfo.setMpDmmAppId(appInfoDTO.getMpDmmAppId());//数据中台应用id
        appInfo.setAppType(appInfoDTO.getAppType());//应用类型
        appInfo.setMasterDataScope(appInfoDTO.getMasterDataScope());//主数据范围
        appInfo.setPublicKey(publicKeyPem);
        AppInfoEntity save = appInfoRepository.save(appInfo);
        //查询是否存在前缀
//        HandleValue[] handleValues = idClientService.resolveHandle(entPrefix.getEntPrefix());
//        if(handleValues == null || handleValues.length == 0){
//            throw new BusinessRuntimeException(BusinessCodeMessage.ENT_PREFIX_NOT_EXISTS);
//        }

        //创建应用标识身份
        AppInfoTO appInfoTO = new AppInfoTO();
        BeanUtils.copyProperties(save, appInfoTO);
        EntEntity entEntity = entRepository.findById(appInfoDTO.getEntId()).get();
        appInfoTO.setEntName(entEntity.getOrgName());
        try {
            // 创建对象标识
            String objectHandleJson = objectMapper.writeValueAsString(appInfoTO);
            HandleValue handleValue = new HandleValue(HandleConstant.APP_INSTANCE_INDEX, "APP_INSTANCE".getBytes(), Base64Utils.encode(objectHandleJson.getBytes()));
            ValueHelper valueHelper = ValueHelper.getInstance();
            HandleValue publicKeyHandleValue = valueHelper.newPublicKeyValue(300, keyPair.getPublic());
            idClientLhsTemplate.createHandle(save.getHandleCode(), new HandleValue[]{handleValue, publicKeyHandleValue});
        } catch (JsonProcessingException e) {
            log.info("ObjectHandle JSON序列号异常");
        }
//        handleUserService.createAppHandleUser(entRepository.findById(appInfoDTO.getEntId()).get(),entPrefix,save,handleValues);
        // 应用身份写入前缀
        statisticsDataReportService.countAppNumTotalDayToMysql();
        return save;
    }

    @Override
    public void updateAppInfo(AppInfoDTO appInfoDTO) throws JsonProcessingException {
        AppInfoEntity appInfo = appInfoRepository.findById(appInfoDTO.getId()).orElse(null);
        //横向越权
        SecurityUtil.checkLevelAuth(appInfo.getEntId());
        appInfo.setAppName(appInfoDTO.getAppName());
        appInfo.setDeployAddress(appInfoDTO.getDeployAddress());
        appInfo.setSysVersion(appInfoDTO.getSysVersion());
        appInfo.setMpDmmAppId(appInfoDTO.getMpDmmAppId());//数据中台应用id
        appInfo.setAppType(appInfoDTO.getAppType());//应用类型
        appInfo.setMasterDataScope(appInfoDTO.getMasterDataScope());//主数据范围
        appInfoRepository.save(appInfo);
        AppInfoTO appInfoTO = new AppInfoTO();
        BeanUtils.copyProperties(appInfo, appInfoTO);
        EntEntity ent = entRepository.findById(appInfo.getEntId()).orElse(null);
        appInfoTO.setEntName(ent.getOrgName());
        //更新应用标识身份名称
//        handleUserService.updateHandleNameByAppId(appInfoDTO.getId(),appInfoDTO.getAppName(),ent.getOrgName());
        try {
            // 创建对象标识
            String objectHandleJson = objectMapper.writeValueAsString(appInfoTO);
            HandleValue handleValue = new HandleValue(HandleConstant.APP_INSTANCE_INDEX, "APP_INSTANCE".getBytes(), Base64Utils.encode(objectHandleJson.getBytes()));
            idClientLhsTemplate.createHandle(appInfo.getHandleCode(), new HandleValue[]{handleValue});
        } catch (JsonProcessingException e) {
            log.info("ObjectHandle JSON序列号异常");
        }
    }

    @Override
    @Transactional(rollbackFor = Exception.class)
    public void audit(Long id) {
        Optional<AppInfoEntity> optional = appInfoRepository.findById(id);
        if (!optional.isPresent()) {
            throw new BusinessRuntimeException(BusinessCodeMessage.DATA_NOT_EXIST);
        }

        AppInfoEntity appInfo = optional.get();

        //横向越权
        SecurityUtil.checkLevelAuth(appInfo.getEntId());

        if (BusinessConstant.STATE_DISABLE.equals(appInfo.getIsAudit())) { // 禁用
            appInfo.setIsAudit(BusinessConstant.STATE_ENABLE);
        } else if (BusinessConstant.STATE_ENABLE.equals(appInfo.getIsAudit())) { // 启用
            appInfo.setIsAudit(BusinessConstant.STATE_DISABLE);
        }

        appInfoRepository.save(appInfo);
    }

    @Override
    public AppInfoVO detail(Long id) {
        AppInfoVO appInfoVO = new AppInfoVO();
        AppInfoEntity appInfo = appInfoRepository.findById(id).orElse(null);
        //横向越权
        SecurityUtil.checkLevelAuth(appInfo.getEntId());
        EntPrefixEntity entPrefix = entPrefixRepository.findById(appInfo.getPrefixId()).orElse(null);
        if (ObjectUtil.isNotNull(appInfo)) {
            BeanUtils.copyProperties(appInfo, appInfoVO);
            if (ObjectUtil.isNotNull(entPrefix)) {
                appInfoVO.setPrefixName(entPrefix.getEntPrefix());
            }
        }
        return appInfoVO;
    }

    @Override
    public void removeAppInfo(Long id) throws JsonProcessingException {
        AppInfoEntity appInfo = appInfoRepository.findById(id).orElse(null);

        //判断是否已经存在生效的关联对象标识
        List<HandleEntity> handleEntityList = handleRepository.findByAppId(id);
        if (CollectionUtil.isNotEmpty(handleEntityList)) {
            throw new BusinessRuntimeException(BusinessCodeMessage.APP_DELETE_ERROR);
        }
        //横向越权
        SecurityUtil.checkLevelAuth(appInfo.getEntId());
        appInfoRepository.deleteById(id);

        //删除应用标识
        idClientLhsTemplate.deleteHandle(appInfo.getHandleCode());

        // 统计应用接入数量
        statisticsDataReportService.countAppNumTotalDayToMysql();

        //删除应用标识身份
        handleUserService.deleteHandleUserByAppId(id);
    }

    @Override
    public PageResult<AppInfoVO> pageAppInfo(String appName, Integer appType, Pageable pageable) {
        // 省级ID
        Long provinceId = SecurityUtil.getCurrentProvinceId();
        // 企业ID
        Long entId = SecurityUtil.getCurrentEntId();
        // 条件查询
        Specification<AppInfoEntity> spec = (root, query, cb) -> {
            List<Predicate> predicates = new ArrayList<>();
            // 当前租户下的数据
            predicates.add(cb.equal(root.get("provinceId").as(Long.class), provinceId));
            // 当前企业下的数据
            predicates.add(cb.equal(root.get("entId").as(Long.class), entId));
            // 应用名称
            if (StrUtil.isNotBlank(appName)) {
                predicates.add(cb.like(root.get("appName").as(String.class), "%" + appName + "%"));
            }
            // 应用名称
            if (ObjectUtil.isNotEmpty(appType)) {
                predicates.add(cb.equal(root.get("appType").as(Integer.class), appType));
            }
            query.where(cb.and(predicates.toArray(new Predicate[predicates.size()])));
            return query.getRestriction();
        };
        Page<AppInfoEntity> appInfoPage = appInfoRepository.findAll(spec, pageable);
        Page<AppInfoVO> pageVO = appInfoPage.map(appInfoEntity -> {
            AppInfoVO appInfoVO = new AppInfoVO();
            BeanUtils.copyProperties(appInfoEntity, appInfoVO);
            EntPrefixEntity entPrefixEntity = entPrefixRepository.findById(appInfoEntity.getPrefixId()).orElse(null);
            if (entPrefixEntity != null) {
                appInfoVO.setPrefixName(entPrefixEntity.getEntPrefix());
            }
            return appInfoVO;
        });

        return PageResult.of(pageVO);
    }

    @Override
    public List<AppInfoVO> getAppList(String appName) {
        Long provinceId = SecurityUtil.getCurrentProvinceId();
        Long entId = SecurityUtil.getCurrentEntId();
        Long appId = SecurityUtil.getCurrentAppId();
        List<AppInfoVO> appInfoVOS = new ArrayList<>();
        List<AppInfoEntity> appInfos = appInfoRepository.findAllByAppNameAndProvinceIdAndEntIdAndAppId(appName, provinceId, entId, appId);
        appInfos.forEach(appInfoEntity -> {
            AppInfoVO appInfoVO = new AppInfoVO();
            BeanUtils.copyProperties(appInfoEntity, appInfoVO);
            appInfoVOS.add(appInfoVO);
        });

        return appInfoVOS;
    }

    @Override
    public List<EntPrefixVO> queryPrefixList(String prefix) {
        // 获取当前用户租户id
        List<EntPrefixVO> entPrefixVOS = new ArrayList<>();
        Long currentEntId = SecurityUtil.getCurrentEntId();
        List<Long> ids = entPrefixHostingRepository.findByEntIdAndHostingState(currentEntId, BusinessConstant.HOSTING)
                .stream().map(EntPrefixHostingEntity::getEntPrefixId).collect(Collectors.toList());
        if (CollectionUtils.isEmpty(ids)) {
            return Collections.emptyList();
        }
        List<EntPrefixEntity> entPrefixEntities = entPrefixRepository.findByIdIn(ids);
        entPrefixEntities.forEach(entPrefixEntity -> {
            EntPrefixVO entPrefixVO = new EntPrefixVO();
            BeanUtils.copyProperties(entPrefixEntity, entPrefixVO);
            entPrefixVOS.add(entPrefixVO);
        });
        return entPrefixVOS;
    }

    @Override
    public List<Tree<String>> dmmAppTree() {
        // 从数据中台查询应用系统列表
        PageQueryCategorySimpleRequest pageQueryCategorySimpleRequest = new PageQueryCategorySimpleRequest();
        pageQueryCategorySimpleRequest.setPageSize(1000000);
        MultiResponse<CategorySimpleDTO> response = dmmClient.pageSystemCategory(pageQueryCategorySimpleRequest);
        Collection<CategorySimpleDTO> items = response.getData().getItems();

        List<TreeNode<String>> nodeList = new ArrayList<>();
        for (CategorySimpleDTO categorySimpleDTO : items) {
            TreeNode<String> treeNode = new TreeNode<>();
            treeNode.setId(categorySimpleDTO.getId());
            treeNode.setName(categorySimpleDTO.getName());
            String parentId = categorySimpleDTO.getParentId();
            treeNode.setParentId(StrUtil.isEmpty(parentId) ? "" : parentId);
            nodeList.add(treeNode);
        }

        //转换器
        return TreeUtil.build(nodeList, "");
    }

    @Override
    public List<AppInfoVO> list(Long entId) {
        Long provinceId = SecurityUtil.getCurrentProvinceId();
        List<AppInfoVO> appInfoVOS = new ArrayList<>();
        List<AppInfoEntity> appInfoEntityList = appInfoRepository.findByProvinceIdAndEntId(provinceId, entId);
        for (AppInfoEntity appInfo : appInfoEntityList) {
            AppInfoVO appInfoVO = new AppInfoVO();
            BeanUtils.copyProperties(appInfo, appInfoVO);
            appInfoVOS.add(appInfoVO);
        }
        return appInfoVOS;
    }

    @Override
    public List<EntPrefixListVO> getEntPrefixList(Long entId) {

        List<Long> ids = entPrefixHostingRepository.findByEntIdAndHostingState(entId, BusinessConstant.HOSTING)
                .stream().map(EntPrefixHostingEntity::getEntPrefixId).collect(Collectors.toList());
        if (CollectionUtils.isEmpty(ids)) {
            return Collections.emptyList();
        }

        List<EntPrefixListVO> entPrefixVOS = new ArrayList<>();
        List<EntPrefixEntity> entPrefixEntities = entPrefixRepository.findByIdIn(ids);
        for (EntPrefixEntity entPrefixEntity : entPrefixEntities) {
            EntPrefixListVO entPrefixVO = new EntPrefixListVO();
            entPrefixVO.setId(entPrefixEntity.getId());
            entPrefixVO.setEntPrefix(entPrefixEntity.getEntPrefix());
            entPrefixVOS.add(entPrefixVO);
        }

        return entPrefixVOS;
    }

    @Override
    public void securityCertificate(AppInfoKeyDTO appInfoKeyDTO) {
        AppInfoEntity appInfo = appInfoRepository.findById(appInfoKeyDTO.getId()).get();
        if (ObjectUtil.isEmpty(appInfo)) {
            throw new BusinessRuntimeException(BusinessCodeMessage.DATA_NOT_EXIST);
        }
        appInfo.setPublicKey(appInfoKeyDTO.getPublicKey());
        appInfoRepository.save(appInfo);
        AppInfoTO appInfoTO = new AppInfoTO();
        BeanUtils.copyProperties(appInfo, appInfoTO);
        //更新应用标识身份公钥信息
        try {
            // 创建对象标识
            String objectHandleJson = objectMapper.writeValueAsString(appInfoTO);
            HandleValue handleValue = new HandleValue(HandleConstant.APP_INSTANCE_INDEX, "APP_INSTANCE".getBytes(), Base64Utils.encode(objectHandleJson.getBytes()));
            PublicKey publicKey = KeyConverter.fromX509Pem(appInfoKeyDTO.getPublicKey());
            ValueHelper valueHelper = ValueHelper.getInstance();
            HandleValue publicKeyHandleValue = valueHelper.newPublicKeyValue(300, publicKey);
            idClientLhsTemplate.updateHandle(appInfo.getHandleCode(), new HandleValue[]{handleValue, publicKeyHandleValue});
        } catch (Exception e) {
            log.error("公钥回溯发生错误", e.getStackTrace());
        }

    }

    @Override
    public KeyPairVO createKeyPair() {
        KeyPairVO keyPairVO = new KeyPairVO();
        // 生成Pem格式的公私钥
        KeyPair keyPair = null;
        try {
            keyPair = EncryptionUtils.generateKeyPair();
        } catch (Exception e) {
            log.error("生成公私钥失败");
        }
        //私钥
        String privateKeyPem = KeyConverter.toPkcs8UnencryptedPem(keyPair.getPrivate());
        //公钥
        String publicKeyPem = KeyConverter.toX509Pem(keyPair.getPublic());
        keyPairVO.setPublicKey(publicKeyPem);
        keyPairVO.setPrivateKey(privateKeyPem);
        return keyPairVO;
    }


}
