package cn.teleinfo.idycprovince.server.config.filter;

import lombok.extern.slf4j.Slf4j;

import javax.servlet.*;
import javax.servlet.http.HttpServletRequest;
import java.io.IOException;

/**
 * 全局请求Filter
 *
 * @Author: Liheng
 */
@Slf4j
public class HttpRequestLogFilter implements Filter {

    @Override
    public void init(FilterConfig filterConfig) throws ServletException {

    }

    @Override
    public void doFilter(ServletRequest servletRequest, ServletResponse servletResponse, FilterChain chain) throws IOException, ServletException {
        HttpServletRequest request = (HttpServletRequest) servletRequest;
        long startTime = System.currentTimeMillis();
        chain.doFilter(servletRequest, servletResponse);
        long endTime = System.currentTimeMillis();
        log.info("{} {} {}, use time: {} ms", request.getMethod(), request.getRequestURI(), request.getProtocol(), endTime - startTime);
    }

    @Override
    public void destroy() {

    }

}
