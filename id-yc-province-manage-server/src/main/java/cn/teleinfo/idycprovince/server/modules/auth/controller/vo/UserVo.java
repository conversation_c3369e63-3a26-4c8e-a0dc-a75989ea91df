package cn.teleinfo.idycprovince.server.modules.auth.controller.vo;

import com.fasterxml.jackson.annotation.JsonIgnore;
import com.fasterxml.jackson.annotation.JsonInclude;
import lombok.Data;

import java.util.List;

@Data
public class UserVo {

    private Long id;

    private String username;

    private String nickName;

    private String email;

    private String phone;

    private String address;

    private String handleUser;

    private String entName;

    private String remark;

    private Boolean isHosting;
    
    private Boolean binding;

    @JsonInclude(JsonInclude.Include.NON_EMPTY)
    private Integer levelType;

    private Long provinceId;
    private String nodeName;

    private String orgName;
    private String appName;

    private Long appId;

    private String appPrefix;
    private Integer appType;//应用类型
    /**
     * 0.不需要修改 1.首次登陆修改 2.30天未修改密码
     */
    private Integer updatePasswordType = 0;

    @JsonInclude(JsonInclude.Include.NON_EMPTY)
    private List<AuthVo> authTree;

    //@JsonIgnore
    private Long entId;

    @JsonInclude(JsonInclude.Include.NON_EMPTY)
    private String roles;

    @JsonInclude(JsonInclude.Include.NON_EMPTY)
    private List<RoleInfo> roleInfos;

    public static class RoleInfo {
        private Long id;
        private String roleName;
        private Integer roleType;

        public RoleInfo() {
        }

        public RoleInfo(Long id, String roleName, Integer roleType) {
            this.id = id;
            this.roleName = roleName;
            this.roleType = roleType;
        }

        public Long getId() {
            return id;
        }

        public void setId(Long id) {
            this.id = id;
        }

        public String getRoleName() {
            return roleName;
        }

        public void setRoleName(String roleName) {
            this.roleName = roleName;
        }

        public Integer getRoleType() {
            return roleType;
        }

        public void setRoleType(Integer roleType) {
            this.roleType = roleType;
        }
    }
}
