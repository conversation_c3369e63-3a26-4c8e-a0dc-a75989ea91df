package cn.teleinfo.idycprovince.server.config;

import lombok.Data;
import org.springframework.boot.context.properties.ConfigurationProperties;

@Data
@ConfigurationProperties(prefix = "app.tobacco.server")
public class MinioProperties {

    /**
     * serviceEndpoint
     */
    private String nodes;
    /**
     * serverUrl
     */
    private String serverUrl;
    /**
     * rootPath
     */
    private String rootPath;

    private String provider;

    /**
     * user
     */
    private String username;
    /**
     * password
     */
    private String password;
}
