package cn.teleinfo.idycprovince.server.constants;

import java.util.Arrays;
import java.util.List;

/**
 * MQ BUSINESS_CODE定义：
 */
public interface MQBusinessCodeConstants {


    //---------------------------------标识权限申请/审核->原企业---------------------------------
    /**
     * 标识权限申请请求
     */
    String HANDLE_AUTH_ITEM_APPLY_REQUEST = "HANDLE_AUTH_ITEM_APPLY_REQUEST";
    /**
     * 标识权限申请响应
     */
    String HANDLE_AUTH_ITEM_APPLY_RESPONSE = "HANDLE_AUTH_ITEM_APPLY_RESPONSE";
    /**
     * 标识权限审核请求
     */
    String HANDLE_AUTH_ITEM_AUDIT_REQUEST = "HANDLE_AUTH_ITEM_AUDIT_REQUEST";
    /**
     * 标识权限审核响应
     */
    String HANDLE_AUTH_ITEM_AUDIT_RESPONSE = "HANDLE_AUTH_ITEM_AUDIT_RESPONSE";

    /**
     * 权限申请集合*
     */
    List<String> HANDLE_AUTH_ITEM_APPLY_REQUEST_LIST = Arrays.asList(HANDLE_AUTH_ITEM_APPLY_REQUEST,HANDLE_AUTH_ITEM_APPLY_RESPONSE);

    /**
     * 权限审核集合*
     */
    List<String> HANDLE_AUTH_ITEM_AUDIT_REQUEST_LIST = Arrays.asList(HANDLE_AUTH_ITEM_AUDIT_REQUEST,HANDLE_AUTH_ITEM_AUDIT_RESPONSE);

    //---------------------------------维护企业->原企业---------------------------------
    /**
     * 维护请求
     */
    String HANDLE_MAINTAIN_UPDATE_NOTICE_REQUEST = "HANDLE_MAINTAIN_UPDATE_NOTICE_REQUEST";
    /**
     * 维护响应
     */
    String HANDLE_MAINTAIN_UPDATE_NOTICE_RESPONSE = "HANDLE_MAINTAIN_UPDATE_NOTICE_RESPONSE";

    //---------------------------------自动维护企业->原企业---------------------------------
    /**
     * 维护请求
     */
    String HANDLE_AUTO_MAINTAIN_UPDATE_NOTICE_REQUEST = "HANDLE_AUTO_MAINTAIN_UPDATE_NOTICE_REQUEST";
    /**
     * 维护响应
     */
    String HANDLE_AUTO_MAINTAIN_UPDATE_NOTICE_RESPONSE = "HANDLE_AUTO_MAINTAIN_UPDATE_NOTICE_RESPONSE";

    /**
     * 自动维护状态码
     */
    List<String> HANDLE_AUTO_MAINTAIN_CODE_LIST = Arrays.asList(HANDLE_AUTO_MAINTAIN_UPDATE_NOTICE_REQUEST, HANDLE_AUTO_MAINTAIN_UPDATE_NOTICE_RESPONSE);

    //---------------------------------维护企业->原企业---------------------------------
    /**
     * 编辑属性请求
     */
    String HANDLE_UPDATE_NOTICE_REQUEST = "HANDLE_UPDATE_NOTICE_REQUEST";
    /**
     * 编辑属性响应
     */
    String HANDLE_UPDATE_NOTICE_RESPONSE = "HANDLE_UPDATE_NOTICE_RESPONSE";
    /**
     * 删除属性请求
     */
    String HANDLE_DELETE_NOTICE_REQUEST = "HANDLE_DELETE_NOTICE_REQUEST";
    /**
     * 删除属性响应
     */
    String HANDLE_DELETE_NOTICE_RESPONSE = "HANDLE_DELETE_NOTICE_RESPONSE";

    /**
     * 手动维护状态码
     */
    List<String> HANDLE_MAINTAIN_CODE_LIST = Arrays.asList(HANDLE_UPDATE_NOTICE_REQUEST, HANDLE_UPDATE_NOTICE_RESPONSE,
            HANDLE_DELETE_NOTICE_REQUEST, HANDLE_DELETE_NOTICE_RESPONSE, HANDLE_MAINTAIN_UPDATE_NOTICE_REQUEST, HANDLE_MAINTAIN_UPDATE_NOTICE_RESPONSE);

}
