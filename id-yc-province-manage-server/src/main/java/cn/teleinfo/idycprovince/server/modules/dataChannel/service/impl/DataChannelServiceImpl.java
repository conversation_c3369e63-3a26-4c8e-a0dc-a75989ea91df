package cn.teleinfo.idycprovince.server.modules.dataChannel.service.impl;


import cn.hutool.core.collection.CollectionUtil;
import cn.hutool.core.util.ObjectUtil;
import cn.hutool.core.util.StrUtil;
import cn.hutool.crypto.Mode;
import cn.hutool.crypto.Padding;
import cn.hutool.crypto.symmetric.AES;
import cn.hutool.db.sql.SqlUtil;
import cn.teleinfo.idycprovince.common.constant.BusinessConstant;
import cn.teleinfo.idycprovince.common.exception.BusinessCodeMessage;
import cn.teleinfo.idycprovince.infrastructure.db.entity.*;
import cn.teleinfo.idycprovince.infrastructure.db.repository.*;
import cn.teleinfo.idycprovince.infrastructure.db.view.DataChannelPageView;
import cn.teleinfo.idycprovince.infrastructure.db.view.HandlePageView;
import cn.teleinfo.idycprovince.server.constants.DataSourcesListConstants;
import cn.teleinfo.idycprovince.server.enums.AppInfoEnum;
import cn.teleinfo.idycprovince.server.feign.dmm.IDmmClient;
import cn.teleinfo.idycprovince.server.modules.auth.util.SecurityUtil;
import cn.teleinfo.idycprovince.server.modules.dataChannel.dto.*;
import cn.teleinfo.idycprovince.server.modules.dataChannel.service.DataChannelService;
import cn.teleinfo.idycprovince.server.modules.dataChannel.vo.*;
import cn.teleinfo.idycprovince.server.modules.util.JsonUtils;
import cn.teleinfo.idycprovince.server.modules.util.SM4Util;
import cn.teleinfo.idycprovince.server.modules.util.SnowFlakeUtil;
import com.abluepoint.summer.common.exception.CodeMessageEnum;
import com.abluepoint.summer.mvc.domain.PageResult;
import com.abluepoint.summer.mvc.exception.BusinessRuntimeException;
import com.alibaba.bizworks.core.runtime.common.MultiResponse;
import com.alibaba.bizworks.core.runtime.common.SingleResponse;
import com.fasterxml.jackson.databind.JsonNode;
import com.fasterxml.jackson.databind.ObjectMapper;
import com.tobacco.mp.dmm.client.api.irs.dto.DataModelDTO;
import com.tobacco.mp.dmm.client.api.irs.dto.EntityObjectDTO;
import com.tobacco.mp.dmm.client.api.irs.dto.ModelColumnDTO;
import com.tobacco.mp.dmm.client.api.irs.req.QueryObjectRequest;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import okhttp3.*;
import org.jooq.*;
import org.jooq.conf.ParamType;
import org.springframework.beans.BeanUtils;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.data.domain.Page;
import org.springframework.data.domain.Pageable;
import org.springframework.data.jpa.domain.Specification;
import org.springframework.stereotype.Service;
import org.springframework.util.StringUtils;

import javax.persistence.criteria.Predicate;
import javax.servlet.http.HttpServletResponse;
import javax.transaction.Transactional;
import java.io.IOException;
import java.time.LocalDateTime;
import java.util.*;
import java.util.stream.Collectors;

import static org.jooq.impl.DSL.*;

@Slf4j
@Service
@RequiredArgsConstructor
public class DataChannelServiceImpl implements DataChannelService {

    private final DSLContext dslContext;
    private final IDmmClient dmmClient;
    private final DataChannelRepository dataChannelRepository;
    private final HandleRepository handleRepository;
    private final DataServiceRepository dataServiceRepository;
    private final HandleItemRepository handleItemRepository;
    private final DatabaseRepository databaseRepository;
    private final MetaDataTableRepository metaDataTableRepository;
    private final MetaDataColumnRepository metaDataColumnRepository;
    private final OkHttpClient httpClient;
    private final ObjectMapper objectMapper;
    private final  HttpServletResponse response;
    private final AppInfoRepository appInfoRepository;

    private final String KEY = "1231325435245234";
    private final String IV = "7658756852345252";

    @Value("${app.is-report}")
    private boolean isreport;

    @Override
    public void create(DataChannelDTO dataChannelDTO) {
        Long appId = SecurityUtil.getCurrentAppId();
        DataChannelEntity dataChannel = dataChannelRepository.findByDataChannelNameAndAppId(dataChannelDTO.getDataChannelName(),appId);
        if(ObjectUtil.isNotEmpty(dataChannel)){
            throw new BusinessRuntimeException(BusinessCodeMessage.DATA_CHANNEL_NAME_IS_EXSIT);
        }
        DataChannelEntity dataChannelEntity = new DataChannelEntity();
        BeanUtils.copyProperties(dataChannelDTO, dataChannelEntity);
        if(ObjectUtil.isEmpty(dataChannelDTO.getDataChannelId())){
            //生成雪花id
            dataChannelEntity.setDataChannelId(SnowFlakeUtil.getSnowFlakeId());
        }else {
            DataChannelEntity dataChannelEntity1 = dataChannelRepository.findByDataChannelIdAndAppId(dataChannelDTO.getDataChannelId(), appId);
            if(ObjectUtil.isNotEmpty(dataChannelEntity1)){
                throw new BusinessRuntimeException(BusinessCodeMessage.OPENAPI_DATA_CHANNEL_ID_IS_EXSIT);
            }
        }
        //对sql进行解密处理
        if(StrUtil.isNotEmpty(dataChannelDTO.getResolveSql())){
            dataChannelEntity.setResolveSql(SM4Util.decrypt(dataChannelDTO.getResolveSql()));
        }
        if(StrUtil.isNotEmpty(dataChannelDTO.getQuerySql())){
            dataChannelEntity.setQuerySql(SM4Util.decrypt(dataChannelDTO.getQuerySql()));
        }
        dataChannelEntity.setProvinceId(SecurityUtil.getCurrentProvinceId());
        dataChannelEntity.setEntId(SecurityUtil.getCurrentEntId());
        dataChannelEntity.setAppId(SecurityUtil.getCurrentAppId());
        dataChannelEntity.setIsShare(BusinessConstant.IS_SHARE_NO);
        dataChannelRepository.save(dataChannelEntity);

    }

    @Override
    public void update(DataChannelDTO dataChannelDTO) {
        DataChannelEntity dataChannelEntity = dataChannelRepository.findById(dataChannelDTO.getId()).orElse(null);
        BeanUtils.copyProperties(dataChannelDTO, dataChannelEntity);
        //对sql进行解密处理
        if(StrUtil.isNotEmpty(dataChannelDTO.getResolveSql())){
            dataChannelEntity.setResolveSql(SM4Util.decrypt(dataChannelDTO.getResolveSql()));
        }
        if(StrUtil.isNotEmpty(dataChannelDTO.getQuerySql())){
            dataChannelEntity.setQuerySql(SM4Util.decrypt(dataChannelDTO.getQuerySql()));
        }
        dataChannelEntity.setAppId(SecurityUtil.getCurrentAppId());
        dataChannelEntity.setEntId(SecurityUtil.getCurrentEntId());
        dataChannelEntity.setUpdatedTime(LocalDateTime.now());
        dataChannelRepository.save(dataChannelEntity);
    }

    @Override
    public void delete(Long id) {
        dataChannelRepository.deleteById(id);
    }

    @Override
    public DataChannelDetailVO detail(Long id) {
        DataChannelDetailVO dataChannelDetailVO = new DataChannelDetailVO();
        DataChannelEntity dataChannelEntity = dataChannelRepository.findById(id).orElse(null);
        BeanUtils.copyProperties(dataChannelEntity,dataChannelDetailVO);
        dataChannelDetailVO.setDataChannelId(dataChannelEntity.getDataChannelId().toString());
        //对解析SQL和查询SQL加密处理
        if(StrUtil.isNotEmpty(dataChannelEntity.getResolveSql())){
            dataChannelDetailVO.setResolveSql(SM4Util.encrypt(dataChannelEntity.getResolveSql()));
        }
        if(StrUtil.isNotEmpty(dataChannelEntity.getQuerySql())){
            dataChannelDetailVO.setQuerySql(SM4Util.encrypt(dataChannelEntity.getQuerySql()));
        }

        //查询对象标识名称
        HandleEntity handle = handleRepository.findById(dataChannelEntity.getObjectHandleId()).orElse(null);
        if(ObjectUtil.isNotEmpty(handle)){
            if(handle.getDataType() == 2){
                dataChannelDetailVO.setObjectHandle(handle.getName()+"-"+handle.getHandle());
            }else {
                if(2 == handle.getHandleType()){
                    dataChannelDetailVO.setObjectHandle(handle.getName()+"-"+handle.getHandle());
                }else {
                    dataChannelDetailVO.setObjectHandle(handle.getName());
                }
            }
        }
        //查询所属数据服务名称
        DataServiceEntity dataService = dataServiceRepository.findById(dataChannelEntity.getDataServiceId()).orElse(null);
        dataChannelDetailVO.setDataServiceName(dataService.getDataServiceName());
        //查询数据库名称
        DatabaseEntity database = databaseRepository.findById(dataChannelEntity.getDatabaseId()).orElse(null);
        if(ObjectUtil.isNotEmpty(database)){
            dataChannelDetailVO.setDatabaseName(database.getDatabaseName());
        }else {
            dataChannelDetailVO.setDatabaseName("");
        }


        return dataChannelDetailVO;
    }

    @Override
    public DataChannelDetailVO query(Long dataChannelId,Long DataServiceId) {
//        DataChannelEntiy dataChannelEntiy = dataChannelRepository.findByDataChannelId(dataChannelId);
//        if(dataChannelEntiy == null) {
//            throw new BusinessRuntimeException(BusinessCodeMessage.DATA_CHANNEL_NOT_EXIST);
//        }
        DataServiceEntity dataServiceEntity = dataServiceRepository.findById(DataServiceId).get();
        if(dataServiceEntity == null) {
            throw new BusinessRuntimeException(BusinessCodeMessage.DATA_SERVICE_NOT_EXIST);
        }
        Request request;
        try {
            String url = DataSourcesListConstants.DATACHANNELDETAIL + "?id=" +dataChannelId;
            request = new Request.Builder().addHeader("appcode",dataServiceEntity.getServiceToken()).addHeader("Authorization", "Bearer ".concat(dataServiceEntity.getServiceToken()))
                    .url(dataServiceEntity.getServiceAddress() + url).get().build();
            log.info("===url=== {} : {}", request.method(), request.url());
            log.info("===Header=== appcode : {}", request.header("appcode"));
            log.info("===Header=== Authorization : {}", request.header("Authorization"));
            log.info("===param=== {}", "");
            Response response = httpClient.newCall(request).execute();
            log.info("===response=== isSuccessful : {}, code : {}", response.isSuccessful(), response.code());
            boolean successful = response.isSuccessful();
            DataChannelDetailVO dataChannelDetailVO = new DataChannelDetailVO();
            if (!successful) {
                log.error("数据服务获取数据通道接口, code:{}, body:{}", response == null ? "null" : response.code(),
                        response == null ? "null" : response.body());
                return dataChannelDetailVO;
            }
            String result = response.body().string();
            log.info("===result=== {}", result);
            JsonNode jsonNode = objectMapper.readTree(result);
            int code = jsonNode.get("code").asInt();
            if (code == CodeMessageEnum.OK.getCode()) {
                dataChannelDetailVO = objectMapper.convertValue(jsonNode.get("data"), DataChannelDetailVO.class);
                return dataChannelDetailVO;
            } else {
                String msg = jsonNode.get("msg").asText();
                log.error("数据服务获取数据通道接口错误, code:{}, msg:{}", code, msg);
                return dataChannelDetailVO;
            }
        } catch (IOException e) {
            log.error("数据服务获取数据通道接口错误", e);
            return new DataChannelDetailVO();
        } finally {
            log.info("================  数据服务获取数据通道接口错误 结束  ================");
        }
    }

    @Override
    public void download(Long id) {
        // 假设你有一个服务方法来获取JSON数据，这里使用伪代码表示
        DataChannelEntity dataChannelEntity = dataChannelRepository.findById(id).get();
        DatabaseEntity databaseEntity = databaseRepository.findById(dataChannelEntity.getDatabaseId()).get();
        DataServiceEntity dataServiceEntity = dataServiceRepository.findById(dataChannelEntity.getDataServiceId()).get();
        DataChannelJsonVO dataChannelJsonVO = objectMapper.convertValue(dataChannelEntity, DataChannelJsonVO.class);
        dataChannelJsonVO.setDataServiceName(dataServiceEntity.getDataServiceName());
        dataChannelJsonVO.setServiceAddress(dataServiceEntity.getServiceAddress());
        dataChannelJsonVO.setDatasourceId(databaseEntity.getDataSourceId());
        try {
            String jsonData = objectMapper.writeValueAsString(dataChannelJsonVO);
            // 设置响应头，告诉浏览器下载一个.json文件
            response.setContentType("application/json");
            response.setHeader("Content-Disposition", "attachment; filename=dataChannel.json");

            // 将JSON数据写入响应输出流
            objectMapper.writeValue(response.getOutputStream(), jsonData);
        } catch (IOException e) {
            // 处理异常
        }
    }

    @Override
    public void send(Long id) {
        // 构建
        AES aes = new AES(Mode.CBC, Padding.PKCS5Padding,
                KEY.getBytes(), IV.getBytes());
        DataChannelEntity dataChannelEntity = dataChannelRepository.findById(id).get();
        DatabaseEntity databaseEntity = databaseRepository.findById(dataChannelEntity.getDatabaseId()).get();
        DataServiceEntity dataServiceEntity = dataServiceRepository.findById(dataChannelEntity.getDataServiceId()).get();
        AppDataChannelDTO appDataChannelDTO = objectMapper.convertValue(dataChannelEntity, AppDataChannelDTO.class);
        appDataChannelDTO.setId(dataChannelEntity.getDataChannelId());
        appDataChannelDTO.setDataChannelType(dataChannelEntity.getDataType());
        appDataChannelDTO.setDatasourceId(databaseEntity.getDataSourceId());
        appDataChannelDTO.setName(dataChannelEntity.getDataChannelName());
        appDataChannelDTO.setResolveSql(aes.encryptHex(appDataChannelDTO.getResolveSql()));
        appDataChannelDTO.setQuerySql(aes.encryptHex(appDataChannelDTO.getQuerySql()));
        Request request;
        try {
            String url = DataSourcesListConstants.DATACHANNELSAVE;
            String value = JsonUtils.toJson(appDataChannelDTO);
            RequestBody requestBody = RequestBody.create(MediaType.parse("application/json"), value);
            request = new Request.Builder().addHeader("appcode",dataServiceEntity.getServiceToken()).addHeader("Authorization", "Bearer ".concat(dataServiceEntity.getServiceToken()))
                    .url(dataServiceEntity.getServiceAddress() + url).put(requestBody).build();
            log.info("===url=== {} : {}", request.method(), request.url());
            log.info("===Header=== appcode : {}", request.header("appcode"));
            log.info("===Header=== Authorization : {}", request.header("Authorization"));
            log.info("===param=== {}", "");
            Response response = httpClient.newCall(request).execute();
            log.info("===response=== isSuccessful : {}, code : {}", response.isSuccessful(), response.code());
            boolean successful = response.isSuccessful();
            if (!successful) {
                log.error("下发接口错误, code:{}, body:{}", response == null ? "null" : response.code(),
                        response == null ? "null" : response.body());
                throw new BusinessRuntimeException(BusinessCodeMessage.OPENAPI_DATA_SERVICE_CONNECT_ERROR);
            }
            String result = response.body().string();
            log.info("===result=== {}", result);
            JsonNode jsonNode = objectMapper.readTree(result);
            int code = jsonNode.get("code").asInt();
            if (code == CodeMessageEnum.OK.getCode()) {
                dataChannelEntity.setSendStatus(1);
            } else {
                dataChannelEntity.setSendStatus(2);
                String msg = jsonNode.get("msg").asText();
                log.error("下发接口错误, code:{}, msg:{}", code, msg);
                throw new BusinessRuntimeException(BusinessCodeMessage.OPENAPI_DATA_SERVICE_CONNECT_ERROR);
            }
        } catch (IOException e) {
            log.error("下发接口错误", e);
            throw new BusinessRuntimeException(BusinessCodeMessage.OPENAPI_DATA_SERVICE_CONNECT_ERROR);
        } finally {
            dataChannelRepository.save(dataChannelEntity);
            log.info("================  下发接口错误 结束  ================");
        }
    }

    @Override
    public PageResult<DataChannelPageVO> page(String dataCHannelName, String objectHandle, Integer sendStatus, Date startTime, Date endTime, Pageable pageable) {
        Long provinceId = SecurityUtil.getCurrentProvinceId();
        Long entId = SecurityUtil.getCurrentEntId();
        Long appId = SecurityUtil.getCurrentAppId();
        Page<DataChannelPageView> pageViews = dataChannelRepository.page(dataCHannelName,objectHandle,sendStatus,startTime,endTime,provinceId,
               entId,appId,pageable);
        Page<DataChannelPageVO> pageVOS = pageViews.map(dataChannelPageView -> {
            DataChannelPageVO dataChannelPageVO = new DataChannelPageVO();
            BeanUtils.copyProperties(dataChannelPageView,dataChannelPageVO);
            return dataChannelPageVO;
        });

        return PageResult.of(pageVOS);
    }

    /**
     * 新增数据通道-构建解析SQL-获取数据库列表
     *
     * @param id
     * @return
     */
    @Override
    public ResolveDatabaseListVO resolveDatabaseList(Long id, Long dataServiceId, Long databaseId) {
        Long currentAppId = SecurityUtil.getCurrentAppId();
        AppInfoEntity appInfoEntity = appInfoRepository.findById(currentAppId).orElse(null);
        int appType = appInfoEntity.getAppType();
        ResolveDatabaseListVO resolveDatabaseListVO = new ResolveDatabaseListVO();
        resolveDatabaseListVO.setDatabaseVOS(Collections.emptyList());
        // appType 1中台
        if (appType == 1) {
            // 1. 根据主键获取实体Id
            HandleEntity handleEntity = handleRepository.findById(id).orElseThrow(() -> new BusinessRuntimeException(BusinessCodeMessage.OBJECT_HANDLE_NOT_EXIST));
            String entityObjectId = handleEntity.getEntityObjectId();
            // 2. 查询数据模型list
            QueryObjectRequest queryObjectRequest = new QueryObjectRequest();
            queryObjectRequest.setId(entityObjectId);
            SingleResponse<EntityObjectDTO> result = dmmClient.getEntityObjectInfoByCode(queryObjectRequest);
            // 失败，返回空
            if (!result.isSuccess()) {
                log.info("获取实体对象详细信息失败, code:{}, msg:{}", result.getCode(), result.getMessage());
                return resolveDatabaseListVO;
            }
            List<DataModelDTO> dataModelDTOS = result.getData().getRelationDataModel();
            // 数据库为空，返回空
            if (CollectionUtil.isEmpty(dataModelDTOS)) {
                return resolveDatabaseListVO;
            }
            // 3. 构建数据库模型列表
            List<ResolveDatabaseVO> resolveDatabaseVOS = dataModelDTOS.stream().map(dataModelDTO -> {
                ResolveDatabaseVO resolveDatabaseVO = new ResolveDatabaseVO();
                resolveDatabaseVO.setModelId(dataModelDTO.getModelId());
                resolveDatabaseVO.setTableName(dataModelDTO.getModelCode());
                return resolveDatabaseVO;
            }).collect(Collectors.toList());
            resolveDatabaseListVO.setDatabaseVOS(resolveDatabaseVOS);
            return resolveDatabaseListVO;
        } else if (appType == 2) {
            // appType 2非中台
            DataServiceEntity dataServiceEntity = dataServiceRepository.findById(dataServiceId).orElse(null);
            Integer dataServiceType = dataServiceEntity.getDataServiceType();
            List<MetaDataTableEntity> metaDataTableEntityList;
            if (dataServiceType == 1) {
                // 标准版
                DatabaseEntity databaseEntity = databaseRepository.findById(databaseId).orElse(null);
                if (databaseEntity == null) {
                    return resolveDatabaseListVO;
                }
                metaDataTableEntityList = metaDataTableRepository.findByDbIdAndAppId(databaseId, currentAppId);
            } else if (dataServiceType == 2) {
                // 自研版
                metaDataTableEntityList = metaDataTableRepository.findByDbIdAndAppId(databaseId, currentAppId);
            } else {
                return resolveDatabaseListVO;
            }
            // 构建数据库模型列表
            List<ResolveDatabaseVO> resolveDatabaseVOS = metaDataTableEntityList.stream().map(dataModelDTO -> {
                ResolveDatabaseVO resolveDatabaseVO = new ResolveDatabaseVO();
                resolveDatabaseVO.setModelId(dataModelDTO.getId().toString());
                resolveDatabaseVO.setTableName(StrUtil.emptyIfNull(dataModelDTO.getTableName()));
                return resolveDatabaseVO;
            }).collect(Collectors.toList());
            resolveDatabaseListVO.setDatabaseVOS(resolveDatabaseVOS);
            return resolveDatabaseListVO;
        }
        return resolveDatabaseListVO;
    }

    /**
     * 新增数据通道-构建解析SQL-获取数据库列表-查询表详情
     *
     * @param modelId
     * @param handleId
     * @return
     */
    @Override
    public ResolveDatabaseModelDetailVO resolveDatabaseModelDetail(Long handleId, String modelId) {
        Long currentAppId = SecurityUtil.getCurrentAppId();
        AppInfoEntity appInfoEntity = appInfoRepository.findById(currentAppId).orElse(null);
        int appType = appInfoEntity.getAppType();
        ResolveDatabaseModelDetailVO databaseModelDetailVO = new ResolveDatabaseModelDetailVO();
        //中台应用
        if(appType == AppInfoEnum.MP_DMM_APP.getCode()){
            List<String> modelIds = Collections.singletonList(modelId);
            MultiResponse<DataModelDTO> result = dmmClient.getDataModelAndColumnByModelIds(modelIds);
            // 失败，返回空
            if (!result.isSuccess()) {
                log.info("获取实体对象关联数据模型信息失败, code:{}, msg:{}", result.getCode(), result.getMessage());
                return databaseModelDetailVO;
            }
            int totalCount = result.getData().getTotalCount();
            log.info("获取实体对象关联数据模型信息成功, 公查询到totalCount:{}", totalCount);
            // 数据库为空，返回空
            if (totalCount == 0) {
                return databaseModelDetailVO;
            }
            Collection<DataModelDTO> modelDTOS = result.getData().getItems();
            // 获取第一个数据模型
            DataModelDTO dataModelDTO = modelDTOS.iterator().next();
            String modelCode = dataModelDTO.getModelCode();
            databaseModelDetailVO.setTableName(modelCode);
            // 获取字段列表和字段别名
            List<ModelColumnDTO> columnList = dataModelDTO.getColumnList();
            List<TableColumnVO> tableColumnVOS = new ArrayList<>();
            columnList.stream().forEach(modelColumnDTO -> {
                TableColumnVO tableColumnVO = new TableColumnVO();
                String columnCode = modelColumnDTO.getColumnCode();
                // 查询别名
                List<HandleItemEntity> handleItemEntityList = handleItemRepository.findByHandleIdAndTableNameAndColumnName(handleId, modelCode, columnCode);
                if(CollectionUtil.isNotEmpty(handleItemEntityList)){
                    HandleItemEntity handleItemEntity = handleItemEntityList.get(0);
                    tableColumnVO.setColumn(columnCode);
                    tableColumnVO.setColumnAlias(handleItemEntity.getField());
                    tableColumnVO.setIsSelected(true);
                    tableColumnVOS.add(tableColumnVO);

                }else{
                    tableColumnVO.setColumn(columnCode);
                    tableColumnVOS.add(tableColumnVO);
                }
            });
            databaseModelDetailVO.setTableColumnVOS(tableColumnVOS);
        }
        //非中台应用
        if(appType == AppInfoEnum.NOT_MP_DMM_APP.getCode()){
            MetaDataTableEntity metaDataTableEntity = metaDataTableRepository.findById(Long.parseLong(modelId)).orElseThrow(() -> new BusinessRuntimeException(BusinessCodeMessage.DATA_NOT_EXIST));
            String tableName = metaDataTableEntity.getTableName();
            databaseModelDetailVO.setTableName(tableName);
            List<TableColumnVO> tableColumnVOList = new ArrayList<>();
            metaDataColumnRepository.findByTableIdAndAppId(Long.parseLong(modelId), currentAppId)
                    .stream().forEach(metaDataColumnEntity -> {
                        TableColumnVO tableColumnVO = new TableColumnVO();
                        // 查询别名
                        List<HandleItemEntity> handleItemEntityList = handleItemRepository.findByHandleIdAndTableNameAndColumnName(handleId, tableName, metaDataColumnEntity.getColumnName());
                        if(CollectionUtil.isNotEmpty(handleItemEntityList)){
                            HandleItemEntity handleItemEntity = handleItemEntityList.get(0);
                            tableColumnVO.setColumn(metaDataColumnEntity.getColumnName());
                            tableColumnVO.setColumnAlias(handleItemEntity.getField());
                            tableColumnVO.setIsSelected(true);
                            tableColumnVOList.add(tableColumnVO);
                        }else{
                            tableColumnVO.setColumn(metaDataColumnEntity.getColumnName());
                            tableColumnVOList.add(tableColumnVO);
                        }
           });
           databaseModelDetailVO.setTableColumnVOS(tableColumnVOList);
        }
        return databaseModelDetailVO;
    }
    
    /**
     * 构建解析SQL
     *
     * @param resolveSqlBuildDTO
     * @return
     */
    @Override
    @SuppressWarnings("all")
    public String buildResolveSql(ResolveSqlBuildDTO resolveSqlBuildDTO) {
        // 创建查询字段列表
        List<Field<?>> fields = resolveSqlBuildDTO.getQueryFieldsDTOList().stream()
                .map(f -> StringUtils.hasText(f.getColumnAlias()) ? field(name(f.getTable(),
                        f.getColumn())).as(f.getColumnAlias()) : field(name(f.getTable(), f.getColumn()))).collect(Collectors.toList());
        // 创建主表
        String mainTable = resolveSqlBuildDTO.getMainTable();
        Table<?> mainTablea = table(name(mainTable));
        Table<?> joinTableRigth = null;
        Table<?> joinTableLeft = null;
        Set<String> tableName = new HashSet<>();
        tableName.add(mainTablea.getName());
        Map<String,ConditionDTO> map = new LinkedHashMap<>();
        // 构建关联表和条件
        for (int i = 0; i < resolveSqlBuildDTO.getTableAssociationDTOList().size(); i++) {
            TableAssociationDTO association = resolveSqlBuildDTO.getTableAssociationDTOList().get(i);
            joinTableRigth = table(name(association.getTableRight().getTable()));
            joinTableLeft = table(name(association.getTableLeft().getTable()));
            // 关联条件
            Condition onCondition = field(name(association.getTableLeft().getTable(), association.getTableLeft().getColumn()))
                    .eq(field(name(association.getTableRight().getTable(), association.getTableRight().getColumn())));
            // join还未关联的表，都关联过任意选一张表（主表除外）
            String joinName = tableName.contains(joinTableRigth.getName()) ? joinTableLeft.getName() : joinTableRigth.getName();
            joinName = mainTablea.getName().equals(joinName) ? tableName.contains(joinTableRigth.getName()) ? joinTableRigth.getName() : joinTableLeft.getName() : joinName;
            String nameLeft = mainTablea.getName() + joinName;
            // join表的条件集，已有条件用and连接条件,还没有条件new Condition
            ConditionDTO conditionDTO = map.get(nameLeft);
            if(conditionDTO != null) {
                Condition onCondition1 = conditionDTO.getCondition().and(onCondition);
                conditionDTO.setCondition(onCondition1);
                map.put(nameLeft,conditionDTO);
            }else {
                conditionDTO = new ConditionDTO();
                conditionDTO.setCondition(onCondition);
                conditionDTO.setTableName(joinName);
                map.put(nameLeft,conditionDTO);
            }

            tableName.add(joinName);
        }
        // 遍历join每张表和on关联条件
        for (String e : map.keySet()) {
            ConditionDTO conditionDTO = map.get(e);
            mainTablea = mainTablea.join(table(name(conditionDTO.getTableName()))).on(conditionDTO.getCondition());
        }

        // 创建查询条件
        String column = resolveSqlBuildDTO.getQueryConditionDTO().getColumn();
        Param<Object> param = param("tid", Object.class);
        Condition whereCondition = field(name(mainTable,column))
                .eq(param);
//        String columnAlias = resolveSqlBuildDTO.getQueryConditionDTO().getColumnAlias();
//        if (StrUtil.isNotBlank(columnAlias)) {
//            whereCondition = field(name(mainTable,columnAlias)).eq(param);
//        }

        // 创建查询
        Query query = dslContext.select(fields).from(mainTablea).where(whereCondition);
        return SqlUtil.formatSql(query.getSQL(ParamType.NAMED));

    }

    /**
     * 查询所属对象标识
     * @param handleType
     * @return
     */
    @Override
    public PageResult<HandleVO> selectObjectHandleAndDataType(Integer handleType,Integer dataType,String handle,String name, Pageable pageable) {
        Long appId = SecurityUtil.getCurrentAppId();
        Page<HandleEntity> handlePageEntity = null;
        if(1 == dataType){//中台标识

            if(handleType == 1){
                Specification<HandleEntity> spec = (root, query, cb) -> {
                    List<Predicate> predicates = new ArrayList<>();
                    predicates.add(cb.equal(root.get("handleType").as(Integer.class), handleType));
                    predicates.add(cb.equal(root.get("dataType").as(Integer.class), dataType));
                    predicates.add(cb.equal(root.get("appId").as(Long.class), appId));
                    predicates.add(cb.isNull(root.get("parentId")));
                    // 应用名称
                    if (StrUtil.isNotBlank(handle)){
                        predicates.add(cb.like(root.get("handle").as(String.class), "%" + handle + "%"));
                    }
                    if (StrUtil.isNotBlank(name)){
                        predicates.add(cb.like(root.get("name").as(String.class), "%" + name + "%"));
                    }
                    query.where(cb.and(predicates.toArray(new Predicate[predicates.size()])));
                    return query.getRestriction();
                };
                handlePageEntity = handleRepository.findAll(spec,pageable);

            }
            if(handleType == 2){
                Specification<HandleEntity> spec = (root, query, cb) -> {
                    List<Predicate> predicates = new ArrayList<>();
                    predicates.add(cb.equal(root.get("handleType").as(Integer.class), handleType));
                    predicates.add(cb.equal(root.get("dataType").as(Integer.class), dataType));
                    predicates.add(cb.equal(root.get("appId").as(Long.class), appId));
                    // 应用名称
                    if (StrUtil.isNotBlank(handle)){
                        predicates.add(cb.like(root.get("handle").as(String.class), "%" + handle + "%"));
                    }
                    if (StrUtil.isNotBlank(name)){
                        predicates.add(cb.like(root.get("name").as(String.class), "%" + name + "%"));
                    }
                    query.where(cb.and(predicates.toArray(new Predicate[predicates.size()])));
                    return query.getRestriction();
                };
                handlePageEntity = handleRepository.findAll(spec,pageable);

            }
        }
        if(2 == dataType){//非中台标识
            Specification<HandleEntity> spec = (root, query, cb) -> {
                List<Predicate> predicates = new ArrayList<>();
                predicates.add(cb.equal(root.get("dataType").as(Integer.class), dataType));
                predicates.add(cb.equal(root.get("appId").as(Long.class), appId));
                // 名称
                if (StrUtil.isNotBlank(handle)){
                    predicates.add(cb.like(root.get("handle").as(String.class), "%" + handle + "%"));
                }
                if (StrUtil.isNotBlank(name)){
                    predicates.add(cb.like(root.get("name").as(String.class), "%" + name + "%"));
                }
                query.where(cb.and(predicates.toArray(new Predicate[predicates.size()])));
                return query.getRestriction();
            };
            handlePageEntity = handleRepository.findAll(spec,pageable);
        }

        Page<HandleVO> pageVO = handlePageEntity.map(appInfoEntity -> {
            HandleVO appInfoVO = new HandleVO();
            BeanUtils.copyProperties(appInfoEntity,appInfoVO);

            return appInfoVO;
        });

        return PageResult.of(pageVO);

    }

    @Override
    @Transactional
    public void share(Long id) {
        DataChannelEntity dataChannelEntity = dataChannelRepository.findById(id).orElse(null);

        if (ObjectUtil.isNotEmpty(dataChannelEntity)) {
            dataChannelEntity.setIsShare(BusinessConstant.IS_SHARE_YES);
            dataChannelRepository.save(dataChannelEntity);
        }

    }

    @Override
    @Transactional
    public void cancelShare(Long id) {
        DataChannelEntity dataChannelEntity = dataChannelRepository.findById(id).orElse(null);
        if (ObjectUtil.isNotEmpty(dataChannelEntity)) {

            //校验通道下有无 关联对象标识
            //根据data_channel_id查询关联对象标识
            List<HandleItemEntity> collect = handleItemRepository.findAllByDataSourceId(dataChannelEntity.getDataChannelId()).stream().distinct().collect(Collectors.toList());

//            Long objectHandleId = dataChannelEntiy.getObjectHandleId();
//
//            List<HandleItemEntity> itemEntityList = collect.stream().filter(handleItemEntity -> !handleItemEntity.getHandleId().equals(objectHandleId)).collect(Collectors.toList());
            if(collect.size()>0){
                throw new BusinessRuntimeException(BusinessCodeMessage.NOT_CANCEL_SHARE);
            }

            dataChannelEntity.setIsShare(BusinessConstant.IS_SHARE_NO);
            dataChannelRepository.save(dataChannelEntity);
        }
    }

    @Override
    public PageResult<HandleVO> objectHandlePage( Long id, String name, String handle, Pageable pageable) {

        DataChannelEntity dataChannelEntity = dataChannelRepository.findById(id).orElse(null);

        Long dataChannelId = dataChannelEntity.getDataChannelId();

        Page<HandlePageView> pageViews = dataChannelRepository.objectHandlePage(dataChannelId,name,handle,
                            pageable);

        Page<HandleVO> pageVOS = pageViews.map(dataChannelPageView -> {
            HandleVO handleVO = new HandleVO();
            BeanUtils.copyProperties(dataChannelPageView,handleVO);
            return handleVO;
        });

        return PageResult.of(pageVOS);



    }

    @Override
    public List<DatabaseVO> databaseList(Long dataServiceId) {
        List<DatabaseVO> databaseVOS = new ArrayList<>();
        //查询数据服务类型
        DataServiceEntity dataService = dataServiceRepository.findById(dataServiceId).orElse(null);
        if(isreport){
            if(1 == dataService.getDataServiceType()){
                //判断数据服务的类型1代表标准，2代表自研
                //如果是标准版，则从数据服务系统查询数据源信息
                Request request;
                try {
                    String url = DataSourcesListConstants.DATASOURCEBASICINFOLIST;
                    request = new Request.Builder().addHeader("appcode",dataService.getServiceToken()).addHeader("Authorization", "Bearer ".concat(dataService.getServiceToken()))
                            .url(dataService.getServiceAddress() + url).get().build();
                    log.info("===url=== {} : {}", request.method(), request.url());
                    log.info("===Header=== appcode : {}", request.header("appcode"));
                    log.info("===Header=== Authorization : {}", request.header("Authorization"));
                    log.info("===param=== {}", "");
                    Response response = httpClient.newCall(request).execute();
                    log.info("===response=== isSuccessful : {}, code : {}", response.isSuccessful(), response.code());
                    boolean successful = response.isSuccessful();
                    if (!successful) {
                        log.error("获取数据源信息接口错误, code:{}, body:{}", response == null ? "null" : response.code(),
                                response == null ? "null" : response.body());
                        throw new BusinessRuntimeException(BusinessCodeMessage.OPENAPI_DATA_SERVICE_CONNECT_ERROR);
                    }
                    String result = response.body().string();
                    log.info("===result=== {}", result);
                    JsonNode jsonNode = objectMapper.readTree(result);
                    int code = jsonNode.get("code").asInt();
                    JsonNode dataNode = jsonNode.get("data");
                    if (code == CodeMessageEnum.OK.getCode()) {
                        dataNode.forEach(e ->{
                            DatabaseVO databaseVO = new DatabaseVO();
                            //查询数据库中是否存在，如果存在则更新，不存在则新增
                            DatabaseEntity databaseEntity = databaseRepository.findByDataServiceIdAndDataSourceId(dataServiceId,e.get("id").asLong());
                            if(ObjectUtil.isEmpty(databaseEntity)){
                                DatabaseEntity database = new DatabaseEntity();
                                database.setDataServiceId(dataServiceId);
                                database.setDataSourceId(e.get("id").asLong());
                                database.setDatabaseName(e.get("databaseName").asText());
                                database.setDatabaseIp(e.get("databaseIp").asText());
                                database.setAppId(SecurityUtil.getCurrentAppId());
                                database.setEntId(SecurityUtil.getCurrentEntId());
                                database.setProvinceId(SecurityUtil.getCurrentProvinceId());
                                databaseEntity = databaseRepository.save(database);
                            }else {
                                databaseEntity.setDatabaseName(e.get("databaseName").asText());
                                databaseEntity.setDatabaseIp(e.get("databaseIp").asText());
                                databaseEntity.setProvinceId(SecurityUtil.getCurrentProvinceId());
                                databaseEntity.setEntId(SecurityUtil.getCurrentEntId());
                                databaseEntity.setAppId(SecurityUtil.getCurrentAppId());
                                databaseRepository.save(databaseEntity);
                            }
                            databaseVO.setDatabaseId(databaseEntity.getId());
                            databaseVO.setDatabaseName(databaseEntity.getDatabaseName());
                            databaseVO.setDatabaseIp(databaseEntity.getDatabaseIp());
                            databaseVOS.add(databaseVO);
                        });
                    } else {
                        String msg = jsonNode.get("msg").asText();
                        log.error("获取数据源信息接口错误, code:{}, msg:{}", code, msg);
                        throw new BusinessRuntimeException(BusinessCodeMessage.OPENAPI_DATA_SERVICE_CONNECT_ERROR);
                    }
                } catch (IOException e) {
                    log.error("获取数据源信息接口错误", e);
                    throw new BusinessRuntimeException(BusinessCodeMessage.OPENAPI_DATA_SERVICE_CONNECT_ERROR);
                }
            }
            if(2 == dataService.getDataServiceType()){
                //如果是自研版，则从数据库中查询数据库列表
                List<DatabaseEntity> databaseEntities = databaseRepository.findByDataServiceId(dataServiceId);
                databaseEntities.forEach(databaseEntity -> {
                    DatabaseVO databaseVO = new DatabaseVO();
                    databaseVO.setDatabaseIp(databaseEntity.getDatabaseIp());
                    databaseVO.setDatabaseId(databaseEntity.getId());
                    databaseVO.setDatabaseName(databaseEntity.getDatabaseName());
                    databaseVOS.add(databaseVO);
                });
            }
        }else {
            List<DatabaseEntity> databaseEntities = databaseRepository.findByDataServiceId(dataServiceId);
            databaseEntities.forEach(databaseEntity -> {
                DatabaseVO databaseVO = new DatabaseVO();
                databaseVO.setDatabaseIp(databaseEntity.getDatabaseIp());
                databaseVO.setDatabaseId(databaseEntity.getId());
                databaseVO.setDatabaseName(databaseEntity.getDatabaseName());
                databaseVOS.add(databaseVO);
            });
        }
        return databaseVOS;
    }


}
