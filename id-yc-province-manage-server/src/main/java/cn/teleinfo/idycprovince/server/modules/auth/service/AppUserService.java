package cn.teleinfo.idycprovince.server.modules.auth.service;

/*
 * File: PasswordService.java
 * <AUTHOR>
 * @since 2022-07-01
 *
 * Copyright (c) 2022 abluepoint teleinfo All Rights Reserved.
 */

import cn.hutool.core.collection.CollectionUtil;
import cn.hutool.core.util.StrUtil;
import cn.teleinfo.idycprovince.common.exception.BusinessCodeMessage;
import cn.teleinfo.idycprovince.infrastructure.db.entity.*;
import cn.teleinfo.idycprovince.infrastructure.db.repository.HandleRepository;
import cn.teleinfo.idycprovince.infrastructure.db.repository.UserAppRepository;
import cn.teleinfo.idycprovince.infrastructure.db.repository.UserRepository;
import cn.teleinfo.idycprovince.infrastructure.db.repository.UserRoleRepository;
import cn.teleinfo.idycprovince.infrastructure.db.repository.UserSocialUserRepository;
import cn.teleinfo.idycprovince.infrastructure.db.to.EntAppUserTO;
import cn.teleinfo.idycprovince.server.enums.RoleTypeEnum;
import cn.teleinfo.idycprovince.server.modules.auth.controller.dto.AppUserDto;
import cn.teleinfo.idycprovince.server.modules.auth.service.auth.SocialUserServiceInterface;
import cn.teleinfo.idycprovince.server.modules.auth.service.auth.impl.RoleSerivce;
import cn.teleinfo.idycprovince.server.modules.auth.util.SecurityUtil;
import cn.teleinfo.idycprovince.server.modules.auth.vo.AppUserPageVo;
import cn.teleinfo.idycprovince.server.modules.auth.vo.AppUserVo;
import cn.teleinfo.idycprovince.server.modules.util.Assert;
import com.abluepoint.summer.mvc.domain.PageResult;
import com.abluepoint.summer.mvc.exception.BusinessRuntimeException;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.ObjectUtils;
import org.springframework.beans.BeanUtils;
import org.springframework.data.domain.Page;
import org.springframework.data.domain.PageRequest;
import org.springframework.data.domain.Sort;
import org.springframework.security.crypto.password.PasswordEncoder;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import java.util.List;

@Slf4j
@Service
@RequiredArgsConstructor
public class AppUserService {

    private final UserRepository userRepository;
    private final RoleSerivce roleSerivce;

    private final UserRoleRepository userRoleRepository;

    private final UserAppRepository userAppRepository;

    private final PasswordEncoder passwordEncoder;
    
    private final HandleRepository handleRepository;

    private String DEFAULT_PWD = "Admin@123";
    
    private final UserSocialUserRepository userSocialUserRepository;
    
    private final SocialUserServiceInterface socialUserService;

    public PageResult<AppUserPageVo> getUserList(String username, String appName, Integer currentPage, Integer pageSize) {
        PageRequest pageable = PageRequest.of(currentPage, pageSize, Sort.by(Sort.Direction.DESC, "updatedTime"));
        username = StrUtil.emptyToNull(username);
        appName = StrUtil.emptyToNull(appName);
        Page<EntAppUserTO> appUserTOPage = userRepository.listUserByApp(username, appName, SecurityUtil.getCurrentProvinceId(), SecurityUtil.getCurrentEntId(), pageable);
        Page<AppUserPageVo> page = appUserTOPage.map(entAppUserTO -> {
            AppUserPageVo appUserPageVo = new AppUserPageVo();
            BeanUtils.copyProperties(entAppUserTO, appUserPageVo);
    
            UserSocialUserEntity userSocialUserEntity = userSocialUserRepository.findByUserId(entAppUserTO.getId());
            if(ObjectUtils.isNotEmpty(userSocialUserEntity)){
                appUserPageVo.setBinding(Boolean.TRUE);
            }else {
                appUserPageVo.setBinding(Boolean.FALSE);
            }
            
            return appUserPageVo;
        });
        return PageResult.of(page);
    }

    @Transactional
    public void addUser(AppUserDto appUserDto) {
        Long provinceId = SecurityUtil.getCurrentProvinceId();

        // 用户名校验
        boolean existsByUsername = userRepository.existsByUsernameAndProvinceId(appUserDto.getUsername(), provinceId);
        if (existsByUsername) {
            Assert.throwEx(BusinessCodeMessage.USERNAME_NOT_EXIST);
        }

        // 添加默认密码
        UserEntity userEntity = new UserEntity();
        userEntity.setUsername(appUserDto.getUsername());
        userEntity.setPassword(passwordEncoder.encode(DEFAULT_PWD));
        userEntity.setRemark(appUserDto.getRemark());
        userEntity.setProvinceId(SecurityUtil.getCurrentProvinceId());
        userEntity.setEntId(SecurityUtil.getCurrentEntId());

        // 入用户表
        userRepository.save(userEntity);

        // 用户应用绑定
        UserAppEntity userAppEntity = new UserAppEntity();
        userAppEntity.setAppId(appUserDto.getAppId());
        userAppEntity.setUserId(userEntity.getId());
        userAppRepository.save(userAppEntity);

        // 入角色表
        RoleEntity appRole = roleSerivce.getRoleByProvinceIdAndRoleTypeEnum(provinceId, RoleTypeEnum.ROLE_TYPE_ENUM_APP);
        userRoleRepository.save(new UserRoleEntity(userEntity.getId(), appRole.getId()));
    }

    @Transactional
    public void modifyUser(AppUserDto appUserDto) {
        UserEntity userEntity = userRepository.findById(appUserDto.getId())
                .orElseThrow(() -> new BusinessRuntimeException(BusinessCodeMessage.DATA_NOT_EXIST));

        // 判断用户是否绑定应用
        UserAppEntity userAppEntity = userAppRepository.findByUserId(userEntity.getId());
        if (userAppEntity == null) {
            throw new BusinessRuntimeException(BusinessCodeMessage.USER_UNBOUND_APP);
        }

        userEntity.setRemark(appUserDto.getRemark());
        userEntity.setRemark(appUserDto.getRemark());
        userRepository.save(userEntity);
    }


    @Transactional
    public void delete(Long id) {
        UserEntity userEntity = userRepository.findById(id)
                .orElseThrow(() -> new BusinessRuntimeException(BusinessCodeMessage.DATA_NOT_EXIST));

        // 判断用户是否绑定应用
        UserAppEntity userAppEntity = userAppRepository.findByUserId(userEntity.getId());
        if (userAppEntity == null) {
            throw new BusinessRuntimeException(BusinessCodeMessage.USER_UNBOUND_APP);
        }
        
        //判断用户下是否有标识
        List<HandleEntity> handleEntityList = handleRepository.findByAppId(id);
        if(CollectionUtil.isNotEmpty(handleEntityList)){
            throw new BusinessRuntimeException(BusinessCodeMessage.APP_DELETE_ERROR);
        }

        userRoleRepository.deleteByUserId(id);
        userRepository.delete(userEntity);
    
        //若系统用户有关联业务中台用户，则解除绑定关系
        UserSocialUserEntity userSocialUserEntity = userSocialUserRepository.findByUserId(id);
        if (ObjectUtils.isNotEmpty(userSocialUserEntity)) {
            userSocialUserRepository.deleteById(userSocialUserEntity.getId());
//            socialUserService.unbinding(userSocialUserEntity.getSocialUserId());
        }
    }

    public AppUserVo detail(Long id) {
        UserEntity userEntity = userRepository.findById(id)
                .orElseThrow(() -> new BusinessRuntimeException(BusinessCodeMessage.DATA_NOT_EXIST));
        // 判断用户是否绑定应用
        UserAppEntity userAppEntity = userAppRepository.findByUserId(userEntity.getId());
        if (userAppEntity == null) {
            throw new BusinessRuntimeException(BusinessCodeMessage.USER_UNBOUND_APP);
        }
        AppUserVo appUserVo = new AppUserVo();
        appUserVo.setUsername(userEntity.getUsername());
        appUserVo.setAppId(userAppEntity.getAppId());
        appUserVo.setRemark(userEntity.getRemark());
        appUserVo.setId(userEntity.getId());
        return appUserVo;
    }
}
