package cn.teleinfo.idycprovince.server.feign.dmm;

import com.alibaba.bizworks.core.runtime.common.MultiResponse;
import com.alibaba.bizworks.core.runtime.common.SingleResponse;
import com.tobacco.mp.dmm.client.api.irs.dto.CategorySimpleDTO;
import com.tobacco.mp.dmm.client.api.irs.dto.DataModelDTO;
import com.tobacco.mp.dmm.client.api.irs.dto.EntityObjectDTO;
import com.tobacco.mp.dmm.client.api.irs.dto.InfoCategoryDTO;
import com.tobacco.mp.dmm.client.api.irs.req.PageQueryCategorySimpleRequest;
import com.tobacco.mp.dmm.client.api.irs.req.QueryObjectRequest;
import com.tobacco.mp.dmm.client.api.irs.req.QuerySystemObjectRequest;
import org.springframework.web.bind.annotation.RequestBody;

import java.util.List;

/**
 * 数据中台客户端
 */
public interface IDmmClient {

    /**
     * 获取系统清单（分页）
     */
    MultiResponse<CategorySimpleDTO> pageSystemCategory(@RequestBody PageQueryCategorySimpleRequest pageQueryCategorySimpleRequest);

    /**
     * 获取实体对象分类树
     */
    SingleResponse<InfoCategoryDTO> getEntityObjectClassifyTree();

    /**
     * 获取系统下的实体对象清单（可根据信息分类ID、实体对象名称、数据状态进行查询
     */
    MultiResponse<EntityObjectDTO> getSystemSingleEntityObject(@RequestBody QuerySystemObjectRequest querySystemObjectRequest);

    /**
     * 获取实体对象详细信息（根据ID、编码查询）
     */
    SingleResponse<EntityObjectDTO> getEntityObjectInfoByCode(@RequestBody QueryObjectRequest queryObjectRequest);

    /**
     * 获取数据模型与字段信息（根据模型ID查询）
     */
    MultiResponse<DataModelDTO> getDataModelAndColumnByModelIds(@RequestBody List<String> ids);

}
