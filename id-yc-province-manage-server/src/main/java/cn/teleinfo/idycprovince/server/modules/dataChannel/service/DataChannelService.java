package cn.teleinfo.idycprovince.server.modules.dataChannel.service;

import cn.teleinfo.idycprovince.server.modules.dataChannel.dto.DataChannelDTO;
import cn.teleinfo.idycprovince.server.modules.dataChannel.dto.ResolveSqlBuildDTO;
import cn.teleinfo.idycprovince.server.modules.dataChannel.vo.*;
import com.abluepoint.summer.mvc.domain.PageResult;
import org.springframework.data.domain.Pageable;
import org.springframework.data.repository.query.Param;

import java.util.Date;
import java.util.List;

public interface DataChannelService {

    void create(DataChannelDTO dataChannelDTO);

    void update(DataChannelDTO dataChannelDTO);

    void delete(Long id);

    DataChannelDetailVO detail(Long id);

    DataChannelDetailVO query(Long dataChannelId,Long dataServiceId);

    void download(Long id);

    void send(Long id);

    PageResult<DataChannelPageVO> page(String dataCHannelName, String objectHandle, Integer sendStatus, Date startTime,Date endTime, Pageable pageable);

    ResolveDatabaseListVO resolveDatabaseList(Long id, Long dataServiceId, Long databaseId);
    
    ResolveDatabaseModelDetailVO resolveDatabaseModelDetail(Long handleId, String modelId);
    

    List<DatabaseVO> databaseList(Long dataServiceId);

    String buildResolveSql(ResolveSqlBuildDTO resolveSqlBuildDTO);

    PageResult<HandleVO> selectObjectHandleAndDataType(Integer handleType,Integer dataType,String handle,String name, Pageable pageable);

    void share(Long id);

    void cancelShare(Long id);

    PageResult<HandleVO> objectHandlePage( Long id, String name, String handle, Pageable pageable);
}
