package cn.teleinfo.idycprovince.server.modules.automaintain.service;

import cn.hutool.core.bean.BeanUtil;
import cn.hutool.core.codec.Base64;
import cn.hutool.extra.pinyin.PinyinUtil;
import cn.hutool.json.JSONUtil;
import cn.teleinfo.idpointer.sdk.core.HandleValue;
import cn.teleinfo.idycprovince.common.constant.HandleAutoMaintainStateEnum;
import cn.teleinfo.idycprovince.common.constant.HandleConstant;
import cn.teleinfo.idycprovince.infrastructure.db.entity.*;
import cn.teleinfo.idycprovince.infrastructure.db.repository.*;
import cn.teleinfo.idycprovince.server.constants.MQBusinessCodeConstants;
import cn.teleinfo.idycprovince.server.modules.handle.dto.AuthDTO;
import cn.teleinfo.idycprovince.server.modules.handle.dto.HandleFieldDTO;
import cn.teleinfo.idycprovince.server.modules.handle.to.HandleItemTO;
import cn.teleinfo.idycprovince.server.modules.handle.to.HandleReferenceTO;
import cn.teleinfo.idycprovince.server.modules.handle.to.HandleTO;
import cn.teleinfo.idycprovince.server.modules.handlemaintain.dto.HandleMaintainItemDTO;
import cn.teleinfo.idycprovince.server.modules.handlemaintain.dto.HandleMaintainReferenceDTO;
import cn.teleinfo.idycprovince.server.modules.monitorchannel.to.MonitorChannelTO;
import cn.teleinfo.idycprovince.server.modules.resolve.service.impl.IdClientService;
import cn.teleinfo.idycprovince.server.mq.transfer.dto.RequestMessageBody;
import cn.teleinfo.idycprovince.server.mq.transfer.sender.MQSender;
import com.alibaba.fastjson.JSONArray;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Service;
import org.springframework.util.StopWatch;

import java.util.*;
import java.util.stream.Collectors;

@Slf4j
@Service
@RequiredArgsConstructor
public class HandleAutoMaintainStateService {
    private final HandleAutoMaintainStateRepository handleAutoMaintainStateRepository;
    private final MQSender mqSender;
    private final HandleRepository handleRepository;
    private final HandleItemRepository handleItemRepository;
    private final IdClientService idClientService;
    private final EntPrefixRepository entPrefixRepository;
    private final HandleUserRepository handleUserRepository;

    // 自动维护
    public String autoMaintain() {
        StopWatch stopWatch = new StopWatch();
        stopWatch.start();

        log.info("******************根据解析日志自动维护标识开始******************");

        try {
            this.autoMaintain0();
        } catch (Exception e) {
            log.error("根据解析日志自动维护标识异常", e);
            return "error";
        }

        log.info("******************根据解析日志自动维护标识结束******************");
        log.info("******************根据解析日志自动维护标识共花费时间：{} ms******************", stopWatch.getTotalTimeMillis());


        stopWatch.stop();
        return "success";
    }


    private void autoMaintain0() {
        List<HandleAutoMaintainStateEntity> notMaintainedHandle = handleAutoMaintainStateRepository.findByState(HandleAutoMaintainStateEnum.NOT_MAINTAINED.getCode());
        if (notMaintainedHandle == null || notMaintainedHandle.isEmpty()) {
            log.warn("没有需要自动维护的标识");
            return;
        }

        for (HandleAutoMaintainStateEntity each : notMaintainedHandle) {
            MonitorChannelTO monitorChannelTO = new MonitorChannelTO();
            monitorChannelTO.setObjectHandle(each.getHandle());
            monitorChannelTO.setDescription(each.getDescription());
            monitorChannelTO.setField(each.getField());
            monitorChannelTO.setReferenceHandle(each.getReferenceHandle());
            monitorChannelTO.setReferenceHandleName(each.getReferenceHandleName());

            processHandleAutoMaintain(monitorChannelTO);
        }
    }

    private void processHandleAutoMaintain(MonitorChannelTO monitorChannelTO) {
        String objectHandle = monitorChannelTO.getObjectHandle();
        String referenceHandle = monitorChannelTO.getReferenceHandle();

        // 标识不存在放弃维护
        HandleEntity handleEntity = handleRepository.findByHandle(objectHandle);
        if (handleEntity == null) {
            log.info("自动维护: 标识{}不属于本系统的标识，放弃自动维护", objectHandle);
            return;
        }
        String field = monitorChannelTO.getField();
        HandleItemEntity handleItemEntity = handleItemRepository.findByHandleIdAndField(handleEntity.getId(), field);
        if (handleItemEntity == null) {
            log.info("自动维护: 标识{}属性{}不存在，放弃自动维护", objectHandle,field);
            return;
        }

        // 解析需要维护的标识
        HandleValue[] handleValues = idClientService.resolveHandle(referenceHandle);
        Map<String, String> handleValueMap = Arrays.stream(handleValues)
                .collect(Collectors.toMap(HandleValue::getTypeAsString, HandleValue::getDataAsString, (k1, k2) -> k1));
        // 查看对象标识信息
        String data = handleValueMap.get(HandleConstant.OBJECT_DEFINITION);
        if (data == null) {
            log.info("自动维护: 标识{}不是对象标识, 放弃自动维护", referenceHandle);
            return;
        }
        HandleTO handleTO = JSONUtil.toBean(Base64.decodeStr(data), HandleTO.class);
        // 遍历对象标识属性
        List<HandleItemTO> handleItemList = handleTO.getItems();
        for (HandleItemTO handleItemTO : handleItemList) {
            Integer fieldType = handleItemTO.getFieldType();
            // 只判断关联标识类型
            if (HandleConstant.FIELD_TYPE_REFERENCE.equals(fieldType)) {
                List<HandleReferenceTO> references = handleItemTO.getReferences();
                for (HandleReferenceTO handleReferenceTO : references) {
                    // 已维护的标识不需要再次维护
                    if (Objects.equals(objectHandle, handleReferenceTO.getReferenceHandle()) && Objects.equals(field, handleReferenceTO.getQueryProp())) {
                        log.info("自动维护: 对象标识{}已完成自动维护, 关联对象标识{}", monitorChannelTO.getObjectHandle(), monitorChannelTO.getReferenceHandle());
                        return;
                    }
                }
            }
        }
        RequestMessageBody requestMessageBody = buildRequestMessageBody(monitorChannelTO, handleEntity, handleItemEntity, handleTO);
        //获取标识身份信息
        String authUser = getHandleUser(objectHandle.split("/")[0]);
        List<HandleMaintainItemDTO> add = requestMessageBody.getAdd();
//        for (HandleMaintainItemDTO itemDTO : add) {
//            itemDTO.setAuthUser(authUser);
//        }
        mqSender.sendHandleAutoMaintainMsg(requestMessageBody);
    }

    public String getHandleUser(String prefix) {
        //查询前缀id
        EntPrefixEntity entPrefix = entPrefixRepository.findByEntPrefix(prefix);
        //查询标识身份信息
        List<HandleUserEntity> handleUserEntities = handleUserRepository.findByEntPrefixId(entPrefix.getId());
        JSONArray authUser = new JSONArray();
        List<AuthDTO> authDTOS = BeanUtil.copyToList(handleUserEntities, AuthDTO.class);
        authUser.addAll(authDTOS);
        return authUser.toJSONString();
    }

    private static RequestMessageBody buildRequestMessageBody(MonitorChannelTO monitorChannelTO, HandleEntity handleEntity, HandleItemEntity handleItemEntity, HandleTO handleTO) {
        String objectHandle = monitorChannelTO.getObjectHandle();
        String field = monitorChannelTO.getField();
        String referenceHandle = monitorChannelTO.getReferenceHandle();
        // 发送自动维护
        RequestMessageBody requestMessageBody = new RequestMessageBody();
        requestMessageBody.setAppCode(handleTO.getAppHandleCode());
        requestMessageBody.setHandle(referenceHandle);
        requestMessageBody.setBusinessCode(MQBusinessCodeConstants.HANDLE_AUTO_MAINTAIN_UPDATE_NOTICE_REQUEST);
        requestMessageBody.setTargetPrefix(handleTO.getEntPrefix());
        // 构建对象标识属性
        HandleMaintainItemDTO handleMaintainItemDTO = new HandleMaintainItemDTO();
        handleMaintainItemDTO.setFieldIndex(-1);
        String pinyin = PinyinUtil.getPinyin(handleEntity.getName(), "");
        handleMaintainItemDTO.setField(pinyin + "_" + objectHandle);
        handleMaintainItemDTO.setDescription(handleEntity.getName() + "_标识");
        handleMaintainItemDTO.setFieldType(HandleConstant.FIELD_TYPE_REFERENCE);
        HandleMaintainReferenceDTO handleMaintainReferenceDTO = new HandleMaintainReferenceDTO();
        // 构建对象标识关联信息
        handleMaintainReferenceDTO.setReferenceHandle(objectHandle);
        handleMaintainReferenceDTO.setQueryProp(new HandleFieldDTO(handleItemEntity.getFieldIndex(), field, null));
        handleMaintainReferenceDTO.setParamProp(new HandleFieldDTO(null, null, null));
        handleMaintainItemDTO.setReferences(Collections.singletonList(handleMaintainReferenceDTO));
        // 设置要维护的属性
        requestMessageBody.setAdd(Collections.singletonList(handleMaintainItemDTO));
        // 设置自动维护信息
        requestMessageBody.setAutoMaintain(monitorChannelTO);
        return requestMessageBody;
    }
}
