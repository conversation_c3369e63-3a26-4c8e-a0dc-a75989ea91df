package cn.teleinfo.idycprovince.server.config.filter;

import cn.teleinfo.idycprovince.server.modules.auth.util.TenantContextHolder;
import lombok.extern.slf4j.Slf4j;

import javax.servlet.*;
import java.io.IOException;

/**
 * 租户上下文清理过滤器
 * 用于清理 ThreadLocal 中的租户信息，防止内存泄漏和数据污染
 */
@Slf4j
public class TenantContextCleanFilter implements Filter {

    @Override
    public void init(FilterConfig filterConfig) throws ServletException {
        // 初始化时不需要做任何事情
    }

    @Override
    public void doFilter(ServletRequest servletRequest, ServletResponse servletResponse, FilterChain chain) throws IOException, ServletException {
        try {
            // 执行过滤器链
            chain.doFilter(servletRequest, servletResponse);
        } finally {
            // 无论请求处理是否成功，都清理 ThreadLocal 中的租户信息
            TenantContextHolder.clear();
            log.debug("TenantContext cleared after request processing");
        }
    }

    @Override
    public void destroy() {
        // 销毁时不需要做任何事情
    }
}