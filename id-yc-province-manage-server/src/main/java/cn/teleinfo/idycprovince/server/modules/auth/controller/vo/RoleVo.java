package cn.teleinfo.idycprovince.server.modules.auth.controller.vo;

import com.fasterxml.jackson.annotation.JsonIgnore;
import com.fasterxml.jackson.annotation.JsonInclude;
import lombok.Data;

import java.time.LocalDateTime;

@Data
public class RoleVo {

    private Integer sort;

    /**
     * 角色名称
     */
    private String roleName;

    /**
     * 角色码;后台生成
     */
    private String roleCode;

    @JsonInclude(JsonInclude.Include.NON_EMPTY)
    private String roleDesc;

    private LocalDateTime createdTime;

    private LocalDateTime updatedTime;

    @JsonInclude(JsonInclude.Include.NON_EMPTY)
    private String creatorName;
    @JsonInclude(JsonInclude.Include.NON_EMPTY)
    private Long createdBy;

    @JsonInclude(JsonInclude.Include.NON_EMPTY)
    private String reviserName;
    @JsonInclude(JsonInclude.Include.NON_EMPTY)
    private Long updatedBy;

    private Long id;

    private Integer undelete;

    @JsonIgnore
    private Long provinceId;

    @JsonInclude(JsonInclude.Include.NON_EMPTY)
    private Long entId;

    @JsonInclude(JsonInclude.Include.NON_EMPTY)
    private String entName;


    private Integer roleType;
}
