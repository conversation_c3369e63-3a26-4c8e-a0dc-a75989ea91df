package cn.teleinfo.idycprovince.server.modules.auth.controller.dto;

import lombok.Data;

@Data
public class InternalAccountDto {

    private Long id;


//    @Size(max = 30, message = "用户名长度30以内")
//    @Pattern(regexp = "^[_a-zA-Z0-9]+$", message = "登陆名由字母数字下划线组成")
    private String username;

    private Long roleId;

    private String address;

    private String remark;

    private Integer type;

    private Long entId;

    private String entName;

    private Long appId;



}
