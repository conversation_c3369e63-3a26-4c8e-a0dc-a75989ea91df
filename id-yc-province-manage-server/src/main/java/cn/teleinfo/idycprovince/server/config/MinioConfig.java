package cn.teleinfo.idycprovince.server.config;

import cn.teleinfo.idycprovince.common.manager.S3FileManager;
import cn.teleinfo.idycprovince.common.manager.TobaccoS3FileManager;
import cn.tobacco.tcaf.api.file.AFileService;
import cn.tobacco.tcaf.api.file.FileServerParam;
import cn.tobacco.tcaf.file.FileServiceCreator;
import lombok.AllArgsConstructor;
import org.springframework.boot.autoconfigure.condition.ConditionalOnProperty;
import org.springframework.boot.context.properties.EnableConfigurationProperties;
import org.springframework.context.annotation.Bean;
import org.springframework.context.annotation.Configuration;

import java.util.Properties;

@Configuration
@ConditionalOnProperty(prefix = "app", name = "file-manager", havingValue = "tbc")
@EnableConfigurationProperties({MinioProperties.class})
@AllArgsConstructor
public class MinioConfig {

    @Bean
    public AFileService fileService(MinioProperties minioProperties) {
        Properties properties = new Properties();
        properties.setProperty(FileServerParam.FILE_SERVER_USER, minioProperties.getUsername());
        properties.setProperty(FileServerParam.FILE_SERVER_PASSWD, minioProperties.getPassword());
        properties.setProperty(FileServerParam.FILE_NODES, minioProperties.getNodes());
        properties.setProperty(FileServerParam.FILE_SERVER_URL, minioProperties.getServerUrl());
        properties.setProperty(FileServerParam.FILE_SERVER_ROOT_PATH, minioProperties.getRootPath());
        properties.setProperty(FileServerParam.FILE_SERVER_PROVIDER, minioProperties.getProvider());
        return new FileServiceCreator(properties).getFileService();
    }

    @Bean
    public S3FileManager configS3Manager(AFileService aFileService, MinioProperties minioProperties) {
        return new TobaccoS3FileManager(aFileService, minioProperties.getRootPath());
    }

}
