package cn.teleinfo.idycprovince.server.config;

import cn.teleinfo.idycprovince.server.config.filter.HttpRequestLogFilter;
import cn.teleinfo.idycprovince.server.config.filter.TenantContextCleanFilter;
import org.springframework.boot.autoconfigure.condition.ConditionalOnExpression;
import org.springframework.boot.web.servlet.FilterRegistrationBean;
import org.springframework.context.annotation.Bean;
import org.springframework.context.annotation.Configuration;

import javax.servlet.DispatcherType;

/**
 * @description: 日志拦截器
 * @author: liuyan
 * @create: 2022−10-21 5:52 PM
 */
@Configuration
public class FilterConfig {

    /**
     * 日志记录
     */
    @Bean
    public FilterRegistrationBean<HttpRequestLogFilter> logFilterRegistration() {
        FilterRegistrationBean<HttpRequestLogFilter> registration = new FilterRegistrationBean<>();
        registration.addUrlPatterns("/*");
        registration.setDispatcherTypes(DispatcherType.REQUEST);
        registration.setFilter(new HttpRequestLogFilter());
        registration.setName("httpRequestLogFilter");
        registration.setOrder(Integer.MIN_VALUE);
        return registration;
    }

    /**
     * 租户上下文清理
     */
    @Bean
    public FilterRegistrationBean<TenantContextCleanFilter> tenantContextCleanFilterRegistration() {
        FilterRegistrationBean<TenantContextCleanFilter> registration = new FilterRegistrationBean<>();
        registration.addUrlPatterns("/*");
        registration.setDispatcherTypes(DispatcherType.REQUEST);
        registration.setFilter(new TenantContextCleanFilter());
        registration.setName("tenantContextCleanFilter");
        // 设置为最高优先级，确保在所有过滤器之后执行
        registration.setOrder(Integer.MAX_VALUE);
        return registration;
    }

}
