package cn.teleinfo.idycprovince.server.modules.auth.controller;

import cn.teleinfo.idycprovince.common.base.PageBaseDTO;
import cn.teleinfo.idycprovince.server.modules.auth.controller.dto.AppUserDto;
import cn.teleinfo.idycprovince.server.modules.auth.service.AppUserService;
import cn.teleinfo.idycprovince.server.modules.auth.vo.AppUserVo;
import cn.teleinfo.idycprovince.server.modules.logcallback.RestfulLogCallback;
import cn.teleinfo.summer.log.annotation.OpLog;
import com.abluepoint.summer.mvc.domain.R;
import com.abluepoint.summer.mvc.domain.Result;
import lombok.RequiredArgsConstructor;
import org.springframework.security.access.prepost.PreAuthorize;
import org.springframework.web.bind.annotation.DeleteMapping;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.PutMapping;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;

import java.util.Objects;

/*
 * File: UserController.java
 * <AUTHOR>
 * @since 2022-07-02
 *
 * Copyright (c) 2022 abluepoint teleinfo All Rights Reserved.
 */
@RestController
@RequestMapping("/api/v1/appuser")
@RequiredArgsConstructor
    public class AppUserController {

    private final AppUserService appUserService;

    /**
     * 分页查询用户信息
     *
     * @param pageBaseDTO
     * @return
     */
    @GetMapping
    @OpLog(value = "分页查询应用用户", callback = RestfulLogCallback.class)
    @PreAuthorize("hasAuthority('SYS:USER:APPPAGE')")
    public Result userList(String username, String appName, PageBaseDTO pageBaseDTO) {
        Integer currentPage = pageBaseDTO.getCurrentPage();
        if (Objects.isNull(currentPage)) {
            currentPage = 0;
        }
        Integer pageSize = pageBaseDTO.getPageSize();
        if (Objects.isNull(pageSize)) {
            pageSize = 10;
        }
        currentPage = currentPage <= 1 ? 0 : --currentPage;

        return R.ok(appUserService.getUserList(username, appName, currentPage, pageSize));
    }

    /**
     * 创建用户
     *
     * @return
     */
    @PostMapping
    @OpLog(value = "创建应用用户", callback = RestfulLogCallback.class)
    @PreAuthorize("hasAuthority('SYS:USER:APPADD')")
    public Result addUser(@RequestBody AppUserDto appUserDto) {
        appUserService.addUser(appUserDto);
        return R.ok();
    }

    /**
     * 更新用户
     *
     * @return
     */
    @GetMapping("detail")
    @OpLog(value = "应用用户详情", callback = RestfulLogCallback.class)
    @PreAuthorize("hasAuthority('SYS:USER:APPDETAIL')")
    public Result detail(Long id) {
        AppUserVo appUserVo = appUserService.detail(id);
        return R.ok(appUserVo);
    }

    /**
     * 更新用户
     *
     * @return
     */
    @PutMapping
    @OpLog(value = "更新用户信息", callback = RestfulLogCallback.class)
    @PreAuthorize("hasAuthority('SYS:USER:APPEDIT')")
    public Result modifyUser(@RequestBody AppUserDto appUserDto) {
        appUserService.modifyUser(appUserDto);
        return R.ok();
    }

    /**
     * 删除用户
     *
     * @return
     */
    @DeleteMapping
    @OpLog(value = "删除用户", callback = RestfulLogCallback.class)
    @PreAuthorize("hasAuthority('SYS:USER:APPDELETE')")
    public Result userDelete(Long id) {
        appUserService.delete(id);
        return R.ok();
    }

}
