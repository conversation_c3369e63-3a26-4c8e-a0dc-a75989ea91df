package cn.teleinfo.idycprovince.server.enums;

import lombok.Getter;

// 审核状态 0 审核中 1 审核通过 2 驳回
@Getter
public enum HandleAuditStateEnum {

    PENDING(0, "审核中"),
    APPROVED(1, "通过"),
    REJECTED(2, "驳回");

    private final int code;
    private final String desc;

    HandleAuditStateEnum(int code, String desc) {
        this.code = code;
        this.desc = desc;
    }

    public static HandleAuditStateEnum findByCode(int code) {
        for (HandleAuditStateEnum handleAuditStateEnum : HandleAuditStateEnum.values()) {
            if (handleAuditStateEnum.getCode() == code) {
                return handleAuditStateEnum;
            }
        }
        return null;
    }

}
