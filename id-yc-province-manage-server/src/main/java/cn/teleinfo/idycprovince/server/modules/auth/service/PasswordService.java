package cn.teleinfo.idycprovince.server.modules.auth.service;

/*
 * File: PasswordService.java
 * <AUTHOR>
 * @since 2022-07-01
 *
 * Copyright (c) 2022 abluepoint teleinfo All Rights Reserved.
 */

import com.abluepoint.summer.common.util.EncryptionUtils;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.cloud.context.config.annotation.RefreshScope;
import org.springframework.stereotype.Service;

import java.nio.charset.StandardCharsets;
import java.security.PrivateKey;
import java.util.Base64;
import java.util.regex.Matcher;
import java.util.regex.Pattern;

@Slf4j
@Service
@RefreshScope
@RequiredArgsConstructor
public class PasswordService {
    private final PublicKeyServiceInterface publicKeyService;
    @Value("${app.login.password-style}")
    private String passwordStyle;
    private Pattern pwdStyle;

    public String getOriPassword(String encryptedPwd) throws Exception {
        PrivateKey privateKey = publicKeyService.getPrivateKey();
        byte[] bytes = EncryptionUtils.decryptByKey(Base64.getDecoder().decode(encryptedPwd), privateKey,"RSA/ECB/OAEPWITHSHA-256ANDMGF1PADDING");
        String oriPwd = new String(bytes, StandardCharsets.UTF_8);
        return oriPwd;
    }

    /**
     * @param oriPassword
     * @return
     *  true: 合法
     *  false: 不合法
     *  ^(?=.*\d)(?=.*[a-z])(?=.*[A-Z])(?=.*[~!@#$%^&*()_+`\-={}:";'<>?,./\\|\]\[)[\da-zA-Z~!@#$%^&*()_+`\-={}:";'<>?,./\\|\]\[]{8,}
     */
    public boolean validate(String oriPassword) {
        if (pwdStyle == null) {
            pwdStyle = Pattern.compile(passwordStyle);
        }
        Matcher matcher = pwdStyle.matcher(oriPassword);
        return matcher.matches();
    }

//    public String createToken(String username, String password){
//        UsernamePasswordAuthenticationToken token = new UsernamePasswordAuthenticationToken(username, password);
//        authenticationManager.authenticate(token);
//        UserDetails userDetails = userDetailService.loadUserByUsername(username);
//    }
}
