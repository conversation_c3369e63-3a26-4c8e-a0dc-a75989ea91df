package cn.teleinfo.idycprovince.server.modules.auth.controller;

import cn.teleinfo.idycprovince.common.base.PageBaseDTO;
import cn.teleinfo.idycprovince.common.exception.BusinessCodeMessage;
import cn.teleinfo.idycprovince.server.modules.auth.controller.dto.RoleDto;
import cn.teleinfo.idycprovince.server.modules.auth.controller.vo.RoleVo;
import cn.teleinfo.idycprovince.server.modules.auth.service.auth.RoleServiceInterface;
import cn.teleinfo.idycprovince.server.modules.logcallback.RestfulLogCallback;
import cn.teleinfo.idycprovince.server.modules.util.Assert;
import cn.teleinfo.summer.log.annotation.OpLog;
import com.abluepoint.summer.mvc.domain.R;
import com.abluepoint.summer.mvc.domain.Result;
import lombok.RequiredArgsConstructor;
import org.springframework.security.access.prepost.PreAuthorize;
import org.springframework.web.bind.annotation.*;

import java.util.List;
import java.util.Objects;
import java.util.Set;


@RestController
@RequestMapping("/api/v1/role")
@RequiredArgsConstructor
public class RoleController {

    private final RoleServiceInterface roleService;

    /**
     * 添加用户下拉框查询角色，根据当前角色的roleType查询即可
     * 省级管理员  所有省级角色
     * 企业管理员  查询所有企业角色
     *
     * @return
     */
    @GetMapping("/user/roleList")
    @OpLog(value = "添加用户下拉框查询角色", callback = RestfulLogCallback.class)
    @PreAuthorize("hasAuthority('SYS:ROLE:ROLES')")
    public Result<List<RoleVo>> roleListWhenAddUser() {
        return R.ok(roleService.getRoleListWhenAddUser());
    }

    /**
     * 下拉框获取角色信息
     * 省级管理员  查看所有预置角色及省级自建角色（provinceId =xxx and (entId is null or entId = -1)）
     * 企业管理员  查看企业级别以下预置角色（企业+应用）及企业自建角色（provinceId =xxx and (entId = xxx or entId = -1)）
     *
     * @return
     */
    @GetMapping("/all")
    @OpLog(value = "下拉框查询角色", callback = RestfulLogCallback.class)
    @PreAuthorize("hasAuthority('SYS:ROLE:ROLES')")
    public Result<List<RoleVo>> rolesAll(Integer type) {
        return R.ok(roleService.getAllRole(type));
    }

    /**
     * 下拉框获取角色信息
     * 省级管理员  查看所有预置角色及省级自建角色（provinceId =xxx and (entId is null or entId = -1)）
     * 企业管理员  查看企业级别以下预置角色（企业+应用）及企业自建角色（provinceId =xxx and (entId = xxx or entId = -1)）
     *
     * @return
     */
    @GetMapping("/ent/all")
    @OpLog(value = "企业账号下拉框查询角色", callback = RestfulLogCallback.class)
    @PreAuthorize("hasAuthority('SYS:ROLE:ENTROLES')")
    public Result<List<RoleVo>> getEntAllRole() {
        return R.ok(roleService.getAllRole(2));
    }

    /**
     * 分页查询角色列表
     * 省级管理员  查看所有预置角色及省级自建角色（provinceId =xxx and (entId is null or entId = -1)）
     * 企业管理员  查看企业级别以下预置角色（企业+应用）及企业自建角色（provinceId =xxx and (entId = xxx or entId = -1)）
     */
    @GetMapping("/list")
    @OpLog(value = "分页查询角色列表", callback = RestfulLogCallback.class)
    @PreAuthorize("hasAuthority('SYS:ROLE:PAGE')")
    public Result rolesList(RoleDto roleDto, PageBaseDTO pageBaseDTO) {
        Integer currentPage = pageBaseDTO.getCurrentPage();
        if (Objects.isNull(currentPage)) {
            currentPage = 0;
        }
        Integer pageSize = pageBaseDTO.getPageSize();
        if (Objects.isNull(pageSize)) {
            pageSize = 10;
        }
        currentPage = currentPage <= 1 ? 0 : --currentPage;
        return R.ok(roleService.getRoleList(roleDto, currentPage, pageSize));
    }

    /**
     * 添加角色信息
     * 添加角色的时候，当前账号没有entId就是省级类型账号（roleType=1），否则是企业账号（roleType=2）
     *
     * @param roleDto
     * @return
     */
    @PostMapping("/add")
    @OpLog(value = "添加角色", callback = RestfulLogCallback.class)
    @PreAuthorize("hasAuthority('SYS:ROLE:ADD')")
    public Result roleAdd(@RequestBody RoleDto roleDto) {
        Assert.notNull(roleDto, BusinessCodeMessage.DATA_NOT_EXIST);
        Assert.notBlankStr(roleDto.getRoleName(), BusinessCodeMessage.ROLE_NAME_NOT_NULL);
        roleService.addRole(roleDto);
        return R.ok();
    }

    /**
     * 角色排序
     *
     * @return
     */
    @PostMapping("/sort")
    @OpLog(value = "查询角色排序数字", callback = RestfulLogCallback.class)
    public Result<Set<Integer>> roleSort() {
        return R.ok(roleService.getRoleSort());
    }

    /**
     * 更新角色信息
     *
     * @param roleDto
     * @return
     */
    @PostMapping("/update")
    @OpLog(value = "更新角色", callback = RestfulLogCallback.class)
    @PreAuthorize("hasAuthority('SYS:ROLE:EDIT')")
    public Result roleModify(@RequestBody RoleDto roleDto) {
        Assert.notNull(roleDto.getId(), BusinessCodeMessage.ID_NOT_EXIST);
        roleService.modifyRole(roleDto);
        return R.ok();
    }

    /**
     * 获取角色详情信息
     *
     * @param id
     * @return
     */
    @GetMapping("/detail")
    @OpLog(value = "查询角色详情", callback = RestfulLogCallback.class)
    @PreAuthorize("hasAuthority('SYS:ROLE:DETAIL')")
    public Result roleDetail(Long id) {
        Assert.notNull(id, BusinessCodeMessage.ID_NOT_EXIST);
        return R.ok(roleService.getRoleDetail(id));
    }

    /**
     * 删除角色——逻辑删除
     *
     * @param roleDto
     * @return
     */
    @PostMapping("/delete")
    @OpLog(value = "删除角色", callback = RestfulLogCallback.class)
    @PreAuthorize("hasAuthority('SYS:ROLE:DELETE')")
    public Result roleDelete(@RequestBody RoleDto roleDto) {
        Assert.notNull(roleDto.getId(), BusinessCodeMessage.ID_NOT_EXIST);
        roleService.removeRole(roleDto.getId());
        return R.ok();
    }
}
