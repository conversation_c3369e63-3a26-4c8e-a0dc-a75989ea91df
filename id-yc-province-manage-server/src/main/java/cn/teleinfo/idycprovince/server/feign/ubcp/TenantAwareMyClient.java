package cn.teleinfo.idycprovince.server.feign.ubcp;

import cn.teleinfo.idycprovince.server.modules.auth.util.SecurityUtil;
import com.bizworks.ubcp.apiclient.feign.MyClient;
import feign.Request;
import feign.Response;
import java.io.IOException;
import java.util.Map;
import java.util.Objects;

import org.springframework.util.StringUtils;

/**
 * 支持多租户的MyClient
 */
public class TenantAwareMyClient extends MyClient {
    
    private final Map<String, Map<String, MyClient>> tenantServiceClients;
    private final String serviceName;
    
    public TenantAwareMyClient(Map<String, Map<String, MyClient>> tenantServiceClients, String serviceName) {
        super(null, null);
        this.tenantServiceClients = tenantServiceClients;
        this.serviceName = serviceName;
    }
    
    @Override
    public Response execute(Request request, Request.Options options) throws IOException {
        // 获取当前租户ID
        String tenantId = Objects.requireNonNull(SecurityUtil.getCurrentProvinceId()).toString();
        
        if (StringUtils.isEmpty(tenantId)) {
            throw new IllegalStateException("当前租户ID未设置，请确保在发起请求前设置租户ID");
        }
        
        // 获取对应的客户端
        Map<String, MyClient> serviceClients = tenantServiceClients.get(tenantId);
        if (serviceClients == null) {
            throw new IllegalStateException("未找到租户ID为 " + tenantId + " 的客户端配置");
        }
        
        MyClient client = serviceClients.get(serviceName);
        if (client == null) {
            throw new IllegalStateException("未找到租户ID为 " + tenantId + " 且服务名为 " + serviceName + " 的客户端配置");
        }
        
        // 使用对应租户和服务的客户端执行请求
        return client.execute(request, options);
    }
}