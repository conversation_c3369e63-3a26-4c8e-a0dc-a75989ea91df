package cn.teleinfo.idycprovince.server.config;

import cn.teleinfo.idycprovince.infrastructure.db.repository.LoginConfigRepository;
import cn.teleinfo.idycprovince.infrastructure.db.repository.SysOperatorLogRepository;
import cn.teleinfo.idycprovince.infrastructure.db.repository.UserRepository;
import cn.teleinfo.idycprovince.infrastructure.redis.RedisCache;
import cn.teleinfo.idycprovince.server.modules.security.*;
import cn.teleinfo.idycprovince.server.modules.security.industryportal.IndustryPortalAuthenticationConfigurer;
import cn.teleinfo.idycprovince.server.modules.security.industryportal.IndustryPortalAuthenticationFilter;
import cn.teleinfo.idycprovince.server.modules.security.industryportal.IndustryPortalAuthenticationProvider;
import cn.teleinfo.idycprovince.server.modules.security.openToken.OpenTokenAuthenticationConfigurer;
import cn.teleinfo.idycprovince.server.modules.security.apiSign.SignAuthenticationTokenFilter;
import cn.teleinfo.idycprovince.server.modules.security.userCenter.UserCenterAuthenticationConfigurer;
import cn.teleinfo.idycprovince.server.modules.security.userCenter.UserCenterAuthenticationProvider;
import com.abluepoint.summer.security.AppHttpSessionIdResolver;
import com.abluepoint.summer.security.AuthenticationHandler;
import com.abluepoint.summer.security.LogoutAndSuccessHandler;
import com.abluepoint.summer.security.SecurityProperties;
import com.abluepoint.summer.security.app.AppAccessDeniedHandler;
import com.abluepoint.summer.security.app.AppAuthenticationEntryPoint;
import com.abluepoint.summer.security.app.AppTokenPermitAllFilter;
import com.abluepoint.summer.security.json.JsonAuthenticationConfigurer;
import com.abluepoint.summer.security.jwt.JwtManager;
import com.fasterxml.jackson.databind.ObjectMapper;
import lombok.Getter;
import lombok.RequiredArgsConstructor;
import lombok.Setter;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.boot.context.properties.ConfigurationProperties;
import org.springframework.boot.context.properties.EnableConfigurationProperties;
import org.springframework.context.MessageSource;
import org.springframework.context.annotation.Bean;
import org.springframework.context.annotation.Configuration;
import org.springframework.security.config.annotation.authentication.builders.AuthenticationManagerBuilder;
import org.springframework.security.config.annotation.method.configuration.EnableGlobalMethodSecurity;
import org.springframework.security.config.annotation.web.builders.HttpSecurity;
import org.springframework.security.config.annotation.web.builders.WebSecurity;
import org.springframework.security.config.annotation.web.configuration.EnableWebSecurity;
import org.springframework.security.config.annotation.web.configuration.WebSecurityConfigurerAdapter;
import org.springframework.security.core.userdetails.UserDetailsService;
import org.springframework.security.crypto.password.PasswordEncoder;
import org.springframework.security.web.authentication.UsernamePasswordAuthenticationFilter;
import org.springframework.security.web.firewall.DefaultHttpFirewall;
import org.springframework.security.web.servletapi.SecurityContextHolderAwareRequestFilter;
import org.springframework.security.web.session.HttpSessionEventPublisher;
import org.springframework.session.config.annotation.web.http.EnableSpringHttpSession;
import org.springframework.session.web.http.HttpSessionIdResolver;

import javax.servlet.http.HttpServletResponse;
import java.util.Arrays;
import java.util.HashMap;
import java.util.List;
import java.util.Map;

@Configuration
@EnableWebSecurity
@EnableSpringHttpSession
@EnableConfigurationProperties({AppSecurityConfig.AppSecurityProperties.class})
@RequiredArgsConstructor
@EnableGlobalMethodSecurity(prePostEnabled=true)
public class AppSecurityConfig extends WebSecurityConfigurerAdapter {

    private final ObjectMapper objectMapper;
    private final AppSecurityProperties appSecurityProperties;
    private final UserDetailsService userDetailsService;
    private final PasswordEncoder passwordEncoder;
    private final MessageSource messageSource;
    private final AppAuthenticationFilter authenticationFilter;
    private final TokenAuthenticationFilter tokenAuthenticationFilter;
    private final AppUserCenterAuthenticationFilter userCenterAuthenticationFilter;
    private final UserCenterAuthenticationProvider userCenterAuthenticationProvider;
    private final RedisCache redisCache;
    private final LoginConfigRepository loginConfigRepository;
    private final UserRepository userRepository;
    private final SysOperatorLogRepository sysOperatorLogRepository;
    private final SignAuthenticationTokenFilter signAuthenticationTokenFilter;
    private final IndustryPortalAuthenticationProvider industryPortalAuthenticationProvider;
    private final IndustryPortalAuthenticationFilter industryPortalAuthenticationFilter;
    @Value("${app.account.continue-minute}")
    private Integer continueMinute;

    @Bean
    public JwtManager jwtManager() {
        return new JwtManager(appSecurityProperties.getJwtSecret());
    }

    @Override
    protected void configure(AuthenticationManagerBuilder builder) throws Exception {
        // @format off
        builder
                .userDetailsService(userDetailsService).passwordEncoder(passwordEncoder)
        .and()
                .authenticationProvider(userCenterAuthenticationProvider)
                .authenticationProvider(industryPortalAuthenticationProvider);
        // @format on
    }


    @Override
    public void configure(WebSecurity webSecurity) {
        if (appSecurityProperties.isIgnoreAll()) {
            webSecurity.ignoring().anyRequest();
        } else {
            if (appSecurityProperties.getIgnored() != null) {
                webSecurity.ignoring().antMatchers(appSecurityProperties.getIgnored());
            }
        }

        DefaultHttpFirewall firewall = new DefaultHttpFirewall();
        //此处开启不让检测‘/’符号
        firewall.setAllowUrlEncodedSlash(true);
        webSecurity.httpFirewall(firewall);

        webSecurity.debug(appSecurityProperties.isDebug());
    }

    @Override
    protected void configure(HttpSecurity httpSecurity) throws Exception {
        AppAuthenticationEntryPoint authenticationEntryPoint = new AppAuthenticationEntryPoint(objectMapper, messageSource);

        httpSecurity.requestCache().disable();

        httpSecurity
                .sessionManagement()
                // todo 临时开放账户session
                .maximumSessions(100)
                .expiredSessionStrategy(event -> {
                    HttpServletResponse response = event.getResponse();
                    Map<String,Object> map = new HashMap<>();
                    map.put("code", 20002);
                    map.put("msg","当前账号异地登录");
                    String result = new ObjectMapper().writeValueAsString(map);
                    response.setContentType("application/json;charset=UTF-8");
                    response.getWriter().println(result);
                    response.flushBuffer();
            });

        // @format off
        httpSecurity.headers().frameOptions().sameOrigin();
        httpSecurity.csrf().disable();
        List<String> publicPatternList = appSecurityProperties.getPublicPattern();
        String[] publicPatterns = new String[publicPatternList.size()];
        publicPatternList.toArray(publicPatterns);

        httpSecurity.authorizeRequests()
                .antMatchers(publicPatterns)
                .permitAll()
                .anyRequest()
                .authenticated();

        AuthenticationHandler authenticationHandler = new MyAppAuthenticationHandler(jwtManager(), objectMapper, messageSource, redisCache, loginConfigRepository, continueMinute, userRepository, sysOperatorLogRepository);
        httpSecurity.apply(new JsonAuthenticationConfigurer<>(authenticationFilter))
                .successHandler(authenticationHandler)
                .failureHandler(authenticationHandler);

        httpSecurity.apply(new OpenTokenAuthenticationConfigurer<>(tokenAuthenticationFilter))
                .successHandler(authenticationHandler)
                .failureHandler(authenticationHandler);
        
        httpSecurity.apply(new UserCenterAuthenticationConfigurer<>(userCenterAuthenticationFilter))
                .successHandler(authenticationHandler)
                .failureHandler(authenticationHandler);

        httpSecurity.apply(new IndustryPortalAuthenticationConfigurer<>(industryPortalAuthenticationFilter))
                .successHandler(authenticationHandler)
                .failureHandler(authenticationHandler);
    
        httpSecurity.addFilterAfter(new AppTokenPermitAllFilter(jwtManager(), authenticationEntryPoint, publicPatternList), SecurityContextHolderAwareRequestFilter.class);
        httpSecurity.addFilterBefore(signAuthenticationTokenFilter, UsernamePasswordAuthenticationFilter.class);

        LogoutAndSuccessHandler logoutAndSuccessHandler = new AppLogoutAndSuccessHandler(objectMapper, messageSource, userRepository, sysOperatorLogRepository);
        httpSecurity.logout().logoutUrl(appSecurityProperties.getLogoutUrl())
                .addLogoutHandler(logoutAndSuccessHandler)
                .logoutSuccessHandler(logoutAndSuccessHandler)
                .invalidateHttpSession(true)
                .deleteCookies();
        // 设置认证失败后认证入口
        httpSecurity.exceptionHandling()
                .accessDeniedHandler(new AppAccessDeniedHandler(objectMapper, messageSource))
                .authenticationEntryPoint(authenticationEntryPoint);

        // @format on
        httpSecurity.rememberMe().alwaysRemember(true);
    }

    @Bean
    public HttpSessionIdResolver appHttpSessionIdResolver() {
        return new AppHttpSessionIdResolver();
    }

    @Bean
    public HttpSessionEventPublisher httpSessionEventPublisher(){
        return new HttpSessionEventPublisher();
    }

    @Getter
    @Setter
    @ConfigurationProperties(prefix = "app.security")
    public static class AppSecurityProperties extends SecurityProperties {

        private String authenticationEntryUrl = "/api/v1/login/name";
        private String authenticationEntryUrlForUserCenter = "/api/v1/user/center/login/name";
        private String authenticationAuthUrl = "/openapi/v1/public/auth";
        private String authenticationEntryUrlForIndustryPortal = "/api/v1/industry-portal/login";

        private String logoutUrl = "/api/v1/logout";

        private List<String> publicPattern = Arrays.asList("/api/v1/public/**"
                , "/api/v1/login/**"
                , "/api/v1/redis/**"
                , "/api/mq/**"
                , "/api/v1/users/forget/**"
                , "/api/v1/http/prefix/**"
                , "/api/v1/social/user/login/**"
                , "/api/v1/province_tenant/public/**"
                , "/openapi/v1/public/**");


        private String oauthAuthenticationUrl;

        private String industryPortalUrl;

        private String industryPortalAuthenticationUrl;

        private String industryPortalSystemCode;

        private boolean captchaEnable = true;

        private boolean passwordEncrypted = false;

        private String jwtSecret = "teleinfo";

    }



}