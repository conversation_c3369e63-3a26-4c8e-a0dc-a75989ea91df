package cn.teleinfo.idycprovince.server.feign.dmm.impl;

import cn.teleinfo.idycprovince.server.feign.dmm.IDmmClient;
import com.alibaba.bizworks.core.runtime.common.MultiResponse;
import com.alibaba.bizworks.core.runtime.common.SingleResponse;
import com.tobacco.mp.dmm.client.api.DataModelServiceAPI;
import com.tobacco.mp.dmm.client.api.DigitalObjectServiceAPI;
import com.tobacco.mp.dmm.client.api.InfoCategoryServiceAPI;
import com.tobacco.mp.dmm.client.api.irs.dto.CategorySimpleDTO;
import com.tobacco.mp.dmm.client.api.irs.dto.DataModelDTO;
import com.tobacco.mp.dmm.client.api.irs.dto.EntityObjectDTO;
import com.tobacco.mp.dmm.client.api.irs.dto.InfoCategoryDTO;
import com.tobacco.mp.dmm.client.api.irs.req.PageQueryCategorySimpleRequest;
import com.tobacco.mp.dmm.client.api.irs.req.QueryObjectRequest;
import com.tobacco.mp.dmm.client.api.irs.req.QuerySystemObjectRequest;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.boot.autoconfigure.condition.ConditionalOnProperty;
import org.springframework.stereotype.Component;

import java.util.List;

@Component
@ConditionalOnProperty(prefix = "app.mock", name = "enable-dmm", havingValue = "false")
public class DmmClientImpl implements IDmmClient {

    @Autowired
    private InfoCategoryServiceAPI infoCategoryServiceAPI;

    @Autowired
    private DigitalObjectServiceAPI digitalObjectServiceAPI;

    @Autowired
    private DataModelServiceAPI dataModelServiceAPI;

    @Override
    public MultiResponse<CategorySimpleDTO> pageSystemCategory(PageQueryCategorySimpleRequest pageQueryCategorySimpleRequest) {
        return infoCategoryServiceAPI.pageSystemCategory(pageQueryCategorySimpleRequest);
    }

    @Override
    public SingleResponse<InfoCategoryDTO> getEntityObjectClassifyTree() {
        return infoCategoryServiceAPI.getEntityObjectClassifyTree();
    }

    @Override
    public MultiResponse<EntityObjectDTO> getSystemSingleEntityObject(QuerySystemObjectRequest querySystemObjectRequest) {
        return digitalObjectServiceAPI.getSystemSingleEntityObject(querySystemObjectRequest);
    }

    @Override
    public SingleResponse<EntityObjectDTO> getEntityObjectInfoByCode(QueryObjectRequest queryObjectRequest) {
        return digitalObjectServiceAPI.getEntityObjectInfoByCode(queryObjectRequest);
    }

    @Override
    public MultiResponse<DataModelDTO> getDataModelAndColumnByModelIds(List<String> ids) {
        return dataModelServiceAPI.getDataModelAndColumnByModelIds(ids);
    }

}
