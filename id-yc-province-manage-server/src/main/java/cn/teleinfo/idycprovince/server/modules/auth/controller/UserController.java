package cn.teleinfo.idycprovince.server.modules.auth.controller;

import cn.hutool.core.util.ObjectUtil;
import cn.hutool.core.util.RandomUtil;
import cn.teleinfo.idycprovince.common.base.PageBaseDTO;
import cn.teleinfo.idycprovince.common.exception.BusinessCodeMessage;
import cn.teleinfo.idycprovince.infrastructure.db.entity.ProvinceTenantEntity;
import cn.teleinfo.idycprovince.infrastructure.db.entity.RoleEntity;
import cn.teleinfo.idycprovince.infrastructure.db.entity.UserEntity;
import cn.teleinfo.idycprovince.infrastructure.db.repository.ProvinceTenantRepository;
import cn.teleinfo.idycprovince.infrastructure.redis.RedisCache;
import cn.teleinfo.idycprovince.server.config.AppSecurityConfig;
import cn.teleinfo.idycprovince.server.constants.RedisKeyConstants;
import cn.teleinfo.idycprovince.server.modules.auth.controller.dto.*;
import cn.teleinfo.idycprovince.server.modules.auth.controller.vo.UserVo;
import cn.teleinfo.idycprovince.server.modules.auth.model.UserDetail;
import cn.teleinfo.idycprovince.server.modules.auth.service.auth.RoleServiceInterface;
import cn.teleinfo.idycprovince.server.modules.auth.service.auth.UserServiceInterface;
import cn.teleinfo.idycprovince.server.modules.auth.util.SecurityUtil;
import cn.teleinfo.idycprovince.server.modules.logcallback.RestfulLogCallback;
import cn.teleinfo.idycprovince.server.modules.prefix.vo.EntDropDownVO;
import cn.teleinfo.idycprovince.server.modules.util.Assert;
import cn.teleinfo.idycprovince.server.util.EmailUtil;
import cn.teleinfo.summer.log.annotation.OpLog;
import com.abluepoint.summer.mvc.annotation.Interceptors;
import com.abluepoint.summer.mvc.domain.R;
import com.abluepoint.summer.mvc.domain.Result;
import com.abluepoint.summer.mvc.exception.BusinessRuntimeException;
import com.anji.captcha.model.common.ResponseModel;
import com.anji.captcha.model.vo.CaptchaVO;
import com.anji.captcha.service.CaptchaService;
import lombok.RequiredArgsConstructor;
import org.apache.commons.lang3.StringUtils;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.cloud.context.config.annotation.RefreshScope;
import org.springframework.security.access.prepost.PreAuthorize;
import org.springframework.security.authentication.BadCredentialsException;
import org.springframework.security.core.Authentication;
import org.springframework.util.CollectionUtils;
import org.springframework.web.bind.annotation.*;

import javax.servlet.http.HttpServletRequest;
import java.util.Arrays;
import java.util.List;
import java.util.Objects;

/*
 * File: UserController.java
 * <AUTHOR>
 * @since 2022-07-02
 *
 * Copyright (c) 2022 abluepoint teleinfo All Rights Reserved.
 */
@RestController
@RequestMapping("/api/v1/users")
@RequiredArgsConstructor
@RefreshScope
    public class UserController {

    private final UserServiceInterface userService;

    private final RoleServiceInterface roleService;
    private final CaptchaService captchaService;
    private final RedisCache redisCache;
    private final EmailUtil emailUtil;
    private final AppSecurityConfig.AppSecurityProperties appSecurityProperties;
    private final ProvinceTenantRepository provinceTenantRepository;

    private final int codeCachedSecond = 60*60;
    private final int emailSentSecond = 1*60;

    /**
     * 1省级节点 2企业节点
     */
    @Value("${app.system-type}")
    private Integer systemType;

    @PostMapping("/forget/submit")
    public Result senEmail(@RequestBody ForgetDto forgetDto) {

        Assert.notNull(forgetDto.getCode(), BusinessCodeMessage.CODE_NOT_EXIST);
        Assert.notNull(forgetDto.getEmail(), BusinessCodeMessage.EMAIL_NOT_EXIST);
        Assert.notNull(forgetDto.getNewPassword(), BusinessCodeMessage.PWD_NOT_NULL);
        Assert.notNull(forgetDto.getRePassword(), BusinessCodeMessage.PWD_NOT_NULL);
       // Assert.notNull(forgetDto.getProvinceId(), BusinessCodeMessage.PROVINCE_ID_NOT_EXIST);
        Assert.checkEmail(forgetDto.getEmail());
        if(ObjectUtil.isEmpty(forgetDto.getProvinceId())) {
            UserEntity userEntity = userService.getUserDetailByEmail(forgetDto.getEmail());
            if(!ObjectUtil.isEmpty(userEntity))
                forgetDto.setProvinceId(userEntity.getProvinceId());
        }

        userService.forgetUserPasssword(forgetDto);

        return R.ok();
    }
    @PostMapping("/forget/sendEmail")
    public Result senEmail(@RequestBody EmailDto params) {
        String email = params.getEmail();
        Assert.notNull(email, BusinessCodeMessage.REPORT_MISSING_PARAMETERS);
        if(ObjectUtil.isEmpty(params.getProvinceId())) {
            UserEntity userEntity = userService.getUserDetailByEmail(params.getEmail());
            if(!ObjectUtil.isEmpty(userEntity))
                params.setProvinceId(userEntity.getProvinceId());
        }
//        Long provinceId = params.getProvinceId();
//        Assert.notNull(provinceId, BusinessCodeMessage.PROVINCE_ID_NOT_EXIST);

        if (appSecurityProperties.isCaptchaEnable()) {
            CaptchaVO captcha = new CaptchaVO();
            captcha.setCaptchaVerification(params.getCaptcha());
            ResponseModel check = this.captchaService.verification(captcha);
            if (check.isSuccess() == false) {
                throw new BadCredentialsException(check.getRepMsg());
            }
        }

        String provinceIdAndEmail =  params.getProvinceId() + email;

        userService.checkEmailLock(provinceIdAndEmail);

        //判断系统中是否存在
        UserEntity userEntity = userService.getUserDetailByEmailAndProvinceId(email, params.getProvinceId());
        if (null==userEntity) {
            throw new BusinessRuntimeException(BusinessCodeMessage.SYSTEM_EMAIL_NOT_FOUNT);
        }

        //判断1分钟内是否发送过
        String emailSentKey = RedisKeyConstants.REDIS_KEY_FORGET_PASSWORD_SENT + email;
        Boolean hasKey = redisCache.hasKey(emailSentKey);
        if (hasKey) {
            throw new BusinessRuntimeException(BusinessCodeMessage.EMAIL_HAD_SENT);
        }

        String code = Integer.toString(RandomUtil.randomInt(100000, 999999));

        String systemTypeStr = systemType==1?"省级":"企业";

        String topic = systemTypeStr+"标识数据关联系统--重置密码邮箱验证码";
        String content = "您好！感谢您使用" +systemTypeStr+
                "标识数据关联系统，您正在进行邮箱验证，本次请求的验证码为："
                +code+"(为了保障您帐号的安全性，请在3分钟内完成验证）";

        emailUtil.sendSimpleMail(email, topic, content);

        String emailExpiredTimeKey = RedisKeyConstants.REDIS_KEY_FORGET_PASSWORD + email;
        String value = code+"-"+ System.currentTimeMillis();
        redisCache.set(emailExpiredTimeKey, value, codeCachedSecond);
        redisCache.set(emailSentKey, "1", emailSentSecond);

        return R.ok();
    }

    @GetMapping("/info")
    @Interceptors
    public Result<UserDetail> info(Authentication authentication) {
        UserDetail principal = (UserDetail) authentication.getPrincipal();
        return R.ok(principal);
    }

    /**
     * 创建有管理员角色的用户接口
     *
     * @param userDto
     * @return
     */
    @PostMapping("/add/admin")
    @OpLog(value = "仅创建用户", callback = RestfulLogCallback.class)
    public Result addUserAdmin(@RequestBody UserDto userDto) {
        ProvinceTenantEntity provinceTenantEntity = provinceTenantRepository.findById(1L).orElse(null);
        Integer enableUserAdd = provinceTenantEntity.getEnableUserAdd();
        if (provinceTenantEntity == null || enableUserAdd == 0){
            throw new BusinessRuntimeException(BusinessCodeMessage.CLOSE_USER_ADD);
        }
        Assert.notNull(userDto.getPhone(), BusinessCodeMessage.PHONE_NOT_NULL);
        Assert.notNull(userDto.getEmail(), BusinessCodeMessage.EMAIL_NOT_NULL);
        Assert.checkMobile(userDto.getPhone());
        Assert.checkEmail(userDto.getEmail());
        if (StringUtils.isNotBlank(userDto.getHandleUser())) {
            Assert.checkHandle(userDto.getHandleUser());
        }

        Long currentProvinceId = SecurityUtil.getCurrentProvinceId();
        if (currentProvinceId==0) {
            Assert.notNull(userDto.getProvinceId(), BusinessCodeMessage.PROVINCE_ID_NOT_EXIST);
            userDto.setProvinceId(userDto.getProvinceId());
            //获取到省级管理员角色，并赋值
            RoleEntity superAdminRole = roleService.getSuperAdminRole(userDto.getProvinceId());
            userDto.setRoles(Arrays.asList(superAdminRole.getId()));
        }else {
            Assert.assertTrue(!CollectionUtils.isEmpty(userDto.getRoles()), BusinessCodeMessage.ROLE_NOT_EXIST);
            userDto.setProvinceId(SecurityUtil.getCurrentProvinceId());
            userDto.setEntId(Objects.nonNull(SecurityUtil.getCurrentEntId())
                    ? SecurityUtil.getCurrentEntId() : null);
        }


        userService.addUserAdmin(userDto);
        return R.ok();
    }

    /**
     * 创建企业用户
     *
     * @param userDto
     * @return
     */
    @PostMapping("/add/ent")
    @OpLog(value = "创建用户及企业", callback = RestfulLogCallback.class)
    @PreAuthorize("hasAuthority('SYS:USER:ADD')")
    public Result addUserEnt(@RequestBody UserDto userDto) {
        ProvinceTenantEntity provinceTenantEntity = provinceTenantRepository.findById(1L).orElse(null);
        Integer enableUserAdd = provinceTenantEntity.getEnableUserAdd();
        if (provinceTenantEntity == null || enableUserAdd == 0){
            throw new BusinessRuntimeException(BusinessCodeMessage.CLOSE_USER_ADD);
        }
        Assert.notNull(userDto.getPhone(), BusinessCodeMessage.PHONE_NOT_NULL);
        Assert.notNull(userDto.getEmail(), BusinessCodeMessage.EMAIL_NOT_NULL);
        Assert.checkMobile(userDto.getPhone());
        if (StringUtils.isNotBlank(userDto.getHandleUser())) {
            Assert.checkHandle(userDto.getHandleUser());
        }
        Assert.checkEmail(userDto.getEmail());

        // 添加租户信息
        userDto.setProvinceId(SecurityUtil.getCurrentProvinceId());
        userService.addUserEntAdmin(userDto);
        return R.ok();
    }

    /**
     * 更新用户信息，包括角色信息
     *
     * @param userDto
     * @return
     */
    @PostMapping("/updateBySelf/info")
    @OpLog(value = "更新用户信息", callback = RestfulLogCallback.class)
    public Result updateBySelf(@RequestBody UserDto userDto) {
        Assert.notNull(userDto.getEmail(), BusinessCodeMessage.EMAIL_NOT_EXIST);
        Assert.notNull(userDto.getUsername(), BusinessCodeMessage.USER_NOT_NULL);
        Assert.notNull(userDto.getPhone(), BusinessCodeMessage.PHONE_NOT_NULL);
        Assert.checkMobile(userDto.getPhone());
        Assert.checkEmail(userDto.getEmail());
        userDto.setProvinceId(SecurityUtil.getCurrentProvinceId());
        UserEntity userEntity = userService.modifyUserInfoBySelf(userDto);
        return R.ok(userEntity);
    }
    /**
     * 更新用户信息，包括角色信息
     *
     * @param userDto
     * @return
     */
    @PostMapping("/update/info")
    @OpLog(value = "更新用户信息", callback = RestfulLogCallback.class)
    @PreAuthorize("hasAuthority('SYS:USER:EDIT')")
    public Result modifyUser(@RequestBody UserDto userDto) {
        Assert.notBlankLong(userDto.getId(), BusinessCodeMessage.ID_NOT_EXIST);
        Assert.notNull(userDto.getPhone(), BusinessCodeMessage.PHONE_NOT_NULL);
        Assert.notNull(userDto.getEmail(), BusinessCodeMessage.EMAIL_IS_EXIST);
        Assert.checkMobile(userDto.getPhone());
        Assert.assertTrue(!CollectionUtils.isEmpty(userDto.getRoles()), BusinessCodeMessage.ROLE_NOT_EXIST);
        if (StringUtils.isNotBlank(userDto.getHandleUser())) {
            Assert.checkHandle(userDto.getHandleUser());
        }
        if (StringUtils.isNotBlank(userDto.getEmail())) {
            Assert.checkEmail(userDto.getEmail());
        }
        roleService.removeUserRole(userDto.getId());
        userService.modifyUserInfo(userDto);
        return R.ok();
    }

    /**
     * 临时查询登录人信息接口，无权限信息
     *
     * @return
     */
    @GetMapping("/user/info")
    @OpLog(value = "查询登录人信息", callback = RestfulLogCallback.class)
    public Result<UserVo> info() {
        // TODO: 2022/11/7 临时添加登录人查询接口
        return R.ok(userService.getUserInfo(SecurityUtil.getCurrentUserId()));
    }

    @GetMapping("/user/ent/info")
    @OpLog(value = "查询企业信息", callback = RestfulLogCallback.class)
    @PreAuthorize("hasAuthority('SYS:USER:ENTLIST')")
    public Result<List<EntDropDownVO>> infoEnt() {
        return R.ok(userService.entDropDowntList());
    }

    /**
     * 更新用户密码，支持加密或不加密
     * 密码格式最低要求：英文+数字，至少6位
     *
     * @param userDto
     * @return
     */
    @PostMapping("/update/pwd")
    @OpLog(value = "更新用户密码", callback = RestfulLogCallback.class)
    @PreAuthorize("hasAuthority('SYS:USER:PWDEDIT')")
    public Result modifyPwdUser(@RequestBody UserDto userDto) {
        Assert.notBlankLong(userDto.getId(), BusinessCodeMessage.ID_NOT_EXIST);
        Assert.notBlankStr(userDto.getNewPwd(), BusinessCodeMessage.PWD_NOT_NULL);
        Assert.notBlankStr(userDto.getDuplicatePwd(), BusinessCodeMessage.PWD_NOT_NULL);
        userService.modifyUserPwd(userDto);
        return R.ok();
    }

    @PostMapping("/reset/pwd")
    @OpLog(value = "重置用户密码", callback = RestfulLogCallback.class)
    @PreAuthorize("hasAuthority('SYS:USER:PWDEDIT')")
    public Result resetPwdUser(@RequestBody UserDto userDto) {
        Assert.notBlankLong(userDto.getId(), BusinessCodeMessage.ID_NOT_EXIST);
        Assert.notBlankStr(userDto.getNewPwd(), BusinessCodeMessage.PWD_NOT_NULL);
        Assert.notBlankStr(userDto.getDuplicatePwd(), BusinessCodeMessage.PWD_NOT_NULL);
        userService.resetPwdUser(userDto);
        return R.ok();
    }

    /**
     * 分页查询用户信息
     *
     * @param
     * @param pageBaseDTO
     * @return
     */
    @GetMapping("/list")
    @OpLog(value = "分页查询用户", callback = RestfulLogCallback.class)
    @PreAuthorize("hasAuthority('SYS:USER:PAGE')")
    public Result userList(String username,Integer type, PageBaseDTO pageBaseDTO) {

        Integer currentPage = pageBaseDTO.getCurrentPage();
        if (Objects.isNull(currentPage)) {
            currentPage = 0;
        }
        Integer pageSize = pageBaseDTO.getPageSize();
        if (Objects.isNull(pageSize)) {
            pageSize = 10;
        }
        currentPage = currentPage <= 1 ? 0 : --currentPage;

        Long currentProvinceId = SecurityUtil.getCurrentProvinceId();
        Boolean isSupperAdmin = currentProvinceId==0;
        //Assert.notBlankInt(userDto.getType(), BusinessCodeMessage.TYPE_NOT_NULL);
        //Assert.assertTrue(LevelTypeEnum.exist(userDto.getType()), BusinessCodeMessage.TYPE_NOT_EXIST);
        return R.ok(userService.getUserList(username,type, currentPage, pageSize, isSupperAdmin));

    }

    /**
     * 绑定用户标识字段信息，暂无实际意义
     *
     * @param userDto
     * @return
     */
    @PostMapping("/binding")
    @OpLog(value = "绑定用户标识", callback = RestfulLogCallback.class)
    @PreAuthorize("hasAuthority('SYS:USER:BINDING')")
    public Result userBinding(@RequestBody UserDto userDto) {
        Assert.notBlankLong(userDto.getId(), BusinessCodeMessage.ID_NOT_EXIST);
        Assert.notBlankStr(userDto.getHandleUser(), BusinessCodeMessage.HANDLE_NOT_MATCH);
        Assert.checkHandle(userDto.getHandleUser());
        userService.bindHandleUser(userDto);
        return R.ok();
    }

    /**
     * 解绑标识字段信息，置为空串
     *
     * @param userDto
     * @return
     */
    @PostMapping("/unbinding")
    @OpLog(value = "解绑用户标识", callback = RestfulLogCallback.class)
    @PreAuthorize("hasAuthority('SYS:USER:UNBINDING')")
    public Result userUnbinding(@RequestBody UserDto userDto) {
        Assert.notBlankLong(userDto.getId(), BusinessCodeMessage.ID_NOT_EXIST);
        userService.unbindHandleUser(userDto);
        return R.ok();
    }

    /**
     * 查询用户详情
     *
     * @param id
     * @return
     */
    @GetMapping("/detail")
    @OpLog(value = "查询用户详情", callback = RestfulLogCallback.class)
    @PreAuthorize("hasAuthority('SYS:USER:DETAIL')")
    public Result userDetail(Long id) {
        Assert.notBlankLong(id, BusinessCodeMessage.ID_NOT_EXIST);
        return R.ok(userService.getUserDetail(id));
    }

    /**
     * 逻辑删除用户信息
     *
     * @param userDto
     * @return
     */
    @PostMapping("/delete")
    @OpLog(value = "删除用户", callback = RestfulLogCallback.class)
    @PreAuthorize("hasAuthority('SYS:USER:DELETE')")
    public Result userDelete(@RequestBody UserDto userDto) {
        Long currentUserId = SecurityUtil.getCurrentUserId();
        if(currentUserId.equals(userDto.getId()))
            throw new BusinessRuntimeException(BusinessCodeMessage.NO_ALLOW_DELETE_SELF);
        Assert.notNull(userDto.getId(), BusinessCodeMessage.ID_NOT_EXIST);
        roleService.removeUserRole(userDto.getId());
        userService.removeUser(userDto.getId());
        return R.ok();
    }

    @PostMapping("/add/internalAccount")
    @OpLog(value = "新增内部账号",callback = RestfulLogCallback.class)
    @PreAuthorize("hasAnyAuthority('SYS:USER:ADD')")
    public Result addInternalAccount(@RequestBody InternalAccountDto internalAccountDto){

        Assert.notNull(internalAccountDto.getUsername(), BusinessCodeMessage.USER_NOT_NULL);
        Assert.notNull(internalAccountDto.getRoleId(), BusinessCodeMessage.ROLE_NOT_EXIST);
        Long userId = userService.addInternalAccount(internalAccountDto);
        return R.ok(userId);

    }




    public static final String getRemoteId(HttpServletRequest request) {
        String xfwd = request.getHeader("X-Forwarded-For");
        String ip = getRemoteIpFromXfwd(xfwd);
        String ua = request.getHeader("user-agent");
        return com.anji.captcha.util.StringUtils.isNotBlank(ip) ? ip + ua : request.getRemoteAddr() + ua;
    }

    private static String getRemoteIpFromXfwd(String xfwd) {
        if (com.anji.captcha.util.StringUtils.isNotBlank(xfwd)) {
            String[] ipList = xfwd.split(",");
            return com.anji.captcha.util.StringUtils.trim(ipList[0]);
        } else {
            return null;
        }
    }


    @PostMapping("/add/social/user")
    @OpLog(value = "新增用户", callback = RestfulLogCallback.class)
    public Result addSocialUser(@RequestBody SocialUserAddDTO socialUserAddDTO){
        userService.addSocialUser(socialUserAddDTO);
        return R.ok();
    }

    @PostMapping("/update/social/user")
    @OpLog(value = "编辑用户", callback = RestfulLogCallback.class)
    public Result updateSocialUser(@RequestBody SocialUserUpdateDTO socialUserUpdateDTO){
        userService.updateSocialUser(socialUserUpdateDTO);
        return R.ok();
    }
}
