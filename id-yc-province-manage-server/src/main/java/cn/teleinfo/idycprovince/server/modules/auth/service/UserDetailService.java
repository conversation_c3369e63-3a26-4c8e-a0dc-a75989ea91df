package cn.teleinfo.idycprovince.server.modules.auth.service;/*
 * File: AppUserDetailService.java
 * <AUTHOR>
 * @since 2022-07-01
 *
 * Copyright (c) 2022 abluepoint teleinfo All Rights Reserved.
 */

import cn.hutool.core.util.ObjectUtil;
import cn.hutool.core.util.StrUtil;
import cn.teleinfo.idycprovince.common.constant.BusinessConstant;
import cn.teleinfo.idycprovince.infrastructure.db.entity.*;
import cn.teleinfo.idycprovince.infrastructure.db.repository.*;
import cn.teleinfo.idycprovince.infrastructure.redis.RedisCache;
import cn.teleinfo.idycprovince.server.modules.auth.model.HandleUserTO;
import cn.teleinfo.idycprovince.server.modules.auth.model.UserDetail;
import cn.teleinfo.idycprovince.server.modules.auth.util.TenantContextHolder;
import lombok.RequiredArgsConstructor;
import org.springframework.beans.BeanUtils;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.security.core.GrantedAuthority;
import org.springframework.security.core.authority.SimpleGrantedAuthority;
import org.springframework.security.core.userdetails.UserDetails;
import org.springframework.security.core.userdetails.UserDetailsService;
import org.springframework.security.core.userdetails.UsernameNotFoundException;
import org.springframework.stereotype.Service;

import java.util.ArrayList;
import java.util.HashSet;
import java.util.List;
import java.util.Set;
import java.util.stream.Collectors;

@RequiredArgsConstructor
@Service
public class UserDetailService implements UserDetailsService {

    private final UserRepository userRepository;
    private final UserRoleRepository userRoleRepository;
    private final RoleRepository roleRepository;
    private final UserAppRepository userAppRepository;
    private final RoleAuthRepository roleAuthRepository;
    private final AuthRepository authRepository;
    private final RedisCache redisCache;
    private final HandleUserRepository handleUserRepository;

    @Value("${app.on-off.tenant}")
    private Integer isTenant;

    @Override
    public UserDetails loadUserByUsername(String username) throws UsernameNotFoundException {
        UserEntity userEntity;
        if (isTenant == 0) {
            userEntity = userRepository.findByUsernameAndProvinceId(username,1L);
        } else {
            userEntity = userRepository.findByUsernameAndProvinceId(username, TenantContextHolder.getTenantId());
        }

        //判断登录用户是否属于应用账号，为应用账号则赋值应用id
        Set<Long> roles = userRoleRepository.findUserRoleEntitiesByUserId(userEntity.getId())
                .stream().map(UserRoleEntity::getRoleId).collect(Collectors.toSet());
        List<RoleEntity> roleEntities = roleRepository.findAllById(roles);
        UserAppEntity userAppEntity = null;
        if (roleEntities.stream().anyMatch(index -> ObjectUtil.equals(index.getRoleType(), BusinessConstant.APP_ROLE))) {
            //角色为企业应用角色,查询关联应用信息
            userAppEntity = userAppRepository.findByUserId(userEntity.getId());
        }
        //由角色查询出接口权限
        Set<Long> auths = roleAuthRepository.findRoleAuthEntitiesByRoleIdIn(roles).stream()
                .map(RoleAuthEntity::getAuthId).collect(Collectors.toSet());
        List<AuthEntity> authList = authRepository.findAllById(auths);
        Set<GrantedAuthority> authorities = new HashSet<>();
        authList.forEach(auth -> {
            String authPermission = auth.getAuthPermission();
            if (StrUtil.isNotBlank(authPermission)) {
                String[] permissions = authPermission.split(",");
                for (String permission : permissions) {
                    authorities.add(new SimpleGrantedAuthority(permission));
                }
            }
        });

        //处理账户是否锁定
        String accountKey = new StringBuilder().append(BusinessConstant.USER_LOGIN_ERROR).append(":").append(username).toString();
        boolean accountNonLocked = !redisCache.exists(accountKey);
        //获取所有用户角色编码
        List<String> roleCodes = roleEntities.stream().map(RoleEntity::getRoleCode).collect(Collectors.toList());
        //标识用户
        List<HandleUserTO> handleUsers = new ArrayList<>();
        if(roleCodes.contains("sys_ent_admin")){
            List<HandleUserEntity> handleUserEntityList  = handleUserRepository.findByEntIdAndUserType(userEntity.getEntId(), 2);
            handleUserEntityList.stream().forEach(handleUserEntity -> {
                HandleUserTO handleUserTO = new HandleUserTO();
                BeanUtils.copyProperties(handleUserEntity, handleUserTO);
                handleUsers.add(handleUserTO);
            });

        }
        if(roleCodes.contains("sys_app_user")){
            HandleUserTO handleUserTO = new HandleUserTO();
            HandleUserEntity handleUserEntity = handleUserRepository.findByAppId(userAppEntity.getAppId());
            if(ObjectUtil.isNotEmpty(handleUserEntity)){
                BeanUtils.copyProperties(handleUserEntity,handleUserTO);
                handleUsers.add(handleUserTO);
            }
        }
        // @format off
        UserDetail userDetail = UserDetail.builder()
                .userId(userEntity.getId())
                .nickName(userEntity.getNickName())
                .username(userEntity.getUsername())
                .password(userEntity.getPassword())
                .accountNonExpired(true)
                .accountNonLocked(accountNonLocked)
                .credentialsNonExpired(true)
                .enabled(true)
                .authorities(authorities)
                .provinceId(userEntity.getProvinceId())
                .entId(userEntity.getEntId())
                .appId(ObjectUtil.isNotEmpty(userAppEntity) ? userAppEntity.getAppId() : null)
                .handleUserTOList(handleUsers)
                .roleCodes(roleCodes)
                .build();
        
        // @format on
        return userDetail;
    }

}
