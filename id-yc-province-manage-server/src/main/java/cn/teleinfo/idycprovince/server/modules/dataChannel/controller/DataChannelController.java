package cn.teleinfo.idycprovince.server.modules.dataChannel.controller;

import cn.teleinfo.idycprovince.common.constant.BusinessConstant;
import cn.teleinfo.idycprovince.infrastructure.db.entity.HandleEntity;
import cn.teleinfo.idycprovince.server.modules.dataChannel.dto.DataChannelDTO;
import cn.teleinfo.idycprovince.server.modules.dataChannel.dto.DataChannelPageDTO;
import cn.teleinfo.idycprovince.server.modules.dataChannel.dto.ResolveSqlBuildDTO;
import cn.teleinfo.idycprovince.server.modules.dataChannel.service.DataChannelService;
import cn.teleinfo.idycprovince.server.modules.dataChannel.vo.*;
import cn.teleinfo.idycprovince.server.modules.handle.dto.HandleDTO;
import cn.teleinfo.idycprovince.server.modules.handle.vo.HandlePageVO;
import cn.teleinfo.idycprovince.server.modules.logcallback.RestfulLogCallback;
import cn.teleinfo.summer.log.annotation.OpLog;
import com.abluepoint.summer.mvc.domain.PageResult;
import com.abluepoint.summer.mvc.domain.R;
import com.abluepoint.summer.mvc.domain.Result;
import lombok.RequiredArgsConstructor;
import org.springframework.data.domain.Pageable;
import org.springframework.data.domain.Sort;
import org.springframework.data.repository.query.Param;
import org.springframework.data.web.PageableDefault;
import org.springframework.security.access.prepost.PreAuthorize;
import org.springframework.web.bind.annotation.*;

import javax.servlet.http.HttpServletResponse;
import java.time.LocalDateTime;
import java.util.Date;
import java.util.List;

@OpLog(value = "数据通道", callback = RestfulLogCallback.class)
@RestController
@RequestMapping("/api/v1/data-channel")
@RequiredArgsConstructor
public class DataChannelController {

    private final DataChannelService dataChannelService;

    /**
     * 创建数据通道*
     * @return
     */
    @PostMapping("/create")
    public Result create(@RequestBody DataChannelDTO dataChannelDTO){
        dataChannelService.create(dataChannelDTO);
        return R.ok();
    }

    /**
     * 数据通道修改*
     * @param dataChannelDTO
     * @return
     */
    @PostMapping("/update")
    public Result update(@RequestBody DataChannelDTO dataChannelDTO){
        dataChannelService.update(dataChannelDTO);
        return R.ok();
    }

    /**
     * 数据通道删除*
     * @param id
     * @return
     */
    @DeleteMapping("/delete")
    public Result delete(@RequestParam Long id){
        dataChannelService.delete(id);
        return R.ok();
    }

    /**
     * 数据通道详情*
     * @param id
     * @return
     */
    @GetMapping("/detail")
    public Result detail(@RequestParam Long id){
        DataChannelDetailVO dataChannelDetailVO = dataChannelService.detail(id);
        return R.ok(dataChannelDetailVO);
    }

    @GetMapping("/query")
    public Result query(@RequestParam Long dataChannelId,@RequestParam Long dataServiceId){
        DataChannelDetailVO dataChannelDetailVO = dataChannelService.query(dataChannelId,dataServiceId);
        return R.ok(dataChannelDetailVO);
    }

    @GetMapping("/download")
    public Result download(@RequestParam Long id){
        dataChannelService.download(id);
        return R.ok(0);
    }

    @GetMapping("/send")
    public Result send(@RequestParam Long id){
        dataChannelService.send(id);
        return R.ok(0);
    }

    /**
     * 数据通道分页列表*
     * @param dataChannelName
     * @param objectHandle
     * @param sendStatus
     * @param pageable
     * @return
     */
    @GetMapping("/page")
    public Result page(@Param("dataChannelName") String dataChannelName,
                       @Param("objectHandleName") String objectHandle,
                       @Param("sendStatus") Integer sendStatus,
                       @Param("startTime") Date startTime,
                       @Param("endTime") Date endTime,
                       @PageableDefault(sort = "updatedTime", direction = Sort.Direction.DESC) Pageable pageable) {
        PageResult<DataChannelPageVO> page = dataChannelService.page(dataChannelName,objectHandle,sendStatus,startTime,endTime, pageable);
        return R.ok(page);
    }

    @GetMapping("/database/list")
    public Result databaseList(@RequestParam Long dataServiceId){
        List<DatabaseVO> databaseVOS = dataChannelService.databaseList(dataServiceId);
        return R.ok(databaseVOS);
    }

    /**
     * 新增数据通道-构建解析SQL-获取数据库列表
     * 
     * @param id
     * @return
     */
    @GetMapping("/resolve/database-list")
    public Result resolveDatabaseList(@RequestParam(name = "id")Long id, Long dataServiceId, Long databaseId){
        ResolveDatabaseListVO resolveDatabaseListVO = dataChannelService.resolveDatabaseList(id, dataServiceId, databaseId);
        return R.ok(resolveDatabaseListVO);
    }

    /**
     * 新增数据通道-构建解析SQL-获取数据库列表-查询表详情
     * 
     * @param handleId
     * @param modelId
     * @return
     */
    @GetMapping("/resolve/table-detail")
    public Result resolveDatabaseTableDetail(@RequestParam(name = "modelId")String modelId,
                                             @RequestParam(name = "handleId")Long handleId){
        ResolveDatabaseModelDetailVO databaseModelDetailVO = dataChannelService.resolveDatabaseModelDetail(handleId, modelId);
        return R.ok(databaseModelDetailVO);
    }

    /**
     * 构建解析SQL
     * 
     * @param resolveSqlBuildDTO
     * @return
     */
    @PostMapping("/resolve/build-sql")
    public Result buildResolveSql(@RequestBody ResolveSqlBuildDTO resolveSqlBuildDTO){
        String resolveSql = dataChannelService.buildResolveSql(resolveSqlBuildDTO);
        return R.ok(resolveSql);
    }

    /**
     * 查询所属对象标识
     * @param handleType
     * @return
     */
    @GetMapping("/object-handle")
    public Result selectObjectHandle(@RequestParam(name = "handleType",required = false)Integer handleType,@Param("handle")String handle,@Param("name")String name
            ,@RequestParam(value = "dataType") Integer dataType,
                                     @PageableDefault(sort = "updatedTime", direction = Sort.Direction.DESC)Pageable pageable){
        PageResult<HandleVO> handleVOS = dataChannelService.selectObjectHandleAndDataType(handleType,dataType,handle,name,pageable);
        return R.ok(handleVOS);
    }

    /**
     * 共享通道
     * @param id
     * @return
     */
    @GetMapping("/share")
    public Result share(@RequestParam Long id){
        dataChannelService.share(id);
        return R.ok(0);
    }


    /**
     * 取消共享通道
     * @param id
     * @return
     */
    @GetMapping("/cancelShare")
    public Result cancelShare(@RequestParam Long id){
        dataChannelService.cancelShare(id);
        return R.ok(0);
    }


    @GetMapping("/objectHandlePage")
    public Result objectHandlePage(@Param("id") Long id,@Param("name") String name, @Param("handle") String handle,
                        Pageable pageable) {
        PageResult<HandleVO> page = dataChannelService.objectHandlePage(id,name,handle, pageable);
        return R.ok(page);
    }



}
