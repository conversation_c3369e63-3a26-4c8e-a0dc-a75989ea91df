package cn.teleinfo.idycprovince.server.modules.auth.service;/*
 * File: StaticPublicKeyService.java
 * <AUTHOR>
 * @since 2022-07-03
 *
 * Copyright (c) 2022 abluepoint teleinfo All Rights Reserved.
 */

import com.abluepoint.summer.common.util.KeyConverter;
import org.springframework.stereotype.Service;

import java.security.NoSuchAlgorithmException;
import java.security.PrivateKey;


/**
-----BEGIN PUBLIC KEY-----
MIIBIjANBgkqhkiG9w0BAQEFAAOCAQ8AMIIBCgKCAQEA2HoU0x7lKHQzeUKdHZ1Q7cwNsbC1SvPz
ncb44cv0EK0nv/5NMbm9/8opi+lXyuBgKznZ2g92jWrcxPxVT+4TX9uUjB/qw34IKqjq1FFM7M5y
G80oBrHCjc2veVREv/lPBDHKTcWdUUmLJlkWm8pLH/p1GHFLiSiJNXraQ08Be8oBB9XvG/bcUCfe
rQdMHuD4ShtL5KgZhVdJkk3v/vB+C2HzxrcEXB35D7H+pe4H8B4d4YJwkSJmvOLdcVYkfLr/CzLi
o4cckqVxRw1NPGLGFX2ofjkL8pXGxYnxX510eBZHC7jgvv7ihcqn807w5VF42MwS4SxlvRjZhkUM
ka2WpQIDAQAB
-----END PUBLIC KEY-----

********************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************
 */

@Service
public class StaticPublicKeyService implements PublicKeyServiceInterface {

    private KeyInfo keyInfo;
    public StaticPublicKeyService(){
        String publicKeyPem = "-----BEGIN PUBLIC KEY-----\r\n" +
                "MIIBIjANBgkqhkiG9w0BAQEFAAOCAQ8AMIIBCgKCAQEA2HoU0x7lKHQzeUKdHZ1Q7cwNsbC1SvPz\r\n" +
                "ncb44cv0EK0nv/5NMbm9/8opi+lXyuBgKznZ2g92jWrcxPxVT+4TX9uUjB/qw34IKqjq1FFM7M5y\r\n" +
                "G80oBrHCjc2veVREv/lPBDHKTcWdUUmLJlkWm8pLH/p1GHFLiSiJNXraQ08Be8oBB9XvG/bcUCfe\r\n" +
                "rQdMHuD4ShtL5KgZhVdJkk3v/vB+C2HzxrcEXB35D7H+pe4H8B4d4YJwkSJmvOLdcVYkfLr/CzLi\r\n" +
                "o4cckqVxRw1NPGLGFX2ofjkL8pXGxYnxX510eBZHC7jgvv7ihcqn807w5VF42MwS4SxlvRjZhkUM\r\n" +
                "ka2WpQIDAQAB\r\n" +
                "-----END PUBLIC KEY-----";
        String privateKeyPem = "-----BEGIN PRIVATE KEY-----\r\n" +
                "MIIEvgIBADANBgkqhkiG9w0BAQEFAASCBKgwggSkAgEAAoIBAQDYehTTHuUodDN5Qp0dnVDtzA2x\r\n" +
                "sLVK8/Odxvjhy/QQrSe//k0xub3/yimL6VfK4GArOdnaD3aNatzE/FVP7hNf25SMH+rDfggqqOrU\r\n" +
                "UUzsznIbzSgGscKNza95VES/+U8EMcpNxZ1RSYsmWRabyksf+nUYcUuJKIk1etpDTwF7ygEH1e8b\r\n" +
                "9txQJ96tB0we4PhKG0vkqBmFV0mSTe/+8H4LYfPGtwRcHfkPsf6l7gfwHh3hgnCRIma84t1xViR8\r\n" +
                "uv8LMuKjhxySpXFHDU08YsYVfah+OQvylcbFifFfnXR4FkcLuOC+/uKFyqfzTvDlUXjYzBLhLGW9\r\n" +
                "GNmGRQyRrZalAgMBAAECggEAB8ysO+gQzxqSsTOEeCjvHcY8lxKcEZGFDUH6PR5tUn1+oWT+8IP1\r\n" +
                "88WbphoX6uwXIu+QaRImDhXT0vgZdcoI4isF6N+MhJMhpbHU1iYAvMZ6Z4SGhOk6LoRC5n7yBC2u\r\n" +
                "GmNV2+P7FU7uR8ktbpsFBmeueN06ILy4ZtggjYzG66gMVu06P5nlId9kBPNnKYcFZvQIRQ2hye3+\r\n" +
                "D8vB1V7XpSLrvZwJwJqNg3WT7jZWua2rUTwvMmf/FVcJoCX+8E/rBoZBX8K+tjH4mAt14nqg/B4P\r\n" +
                "IXt3OgtFVjJkI9t4E+2Pr/Qbl5zXPhVebl7JZFEo/MlzW31kjCgyMdewzgG8eQKBgQDubTS2rYS6\r\n" +
                "IYwkvM2m2ce8LIiL8EymR2G/Xmd02F3zDEYz2zDrZXVJCPglNOqz+BeWSdum0KeqLN6TrWmP7ycz\r\n" +
                "8CYOD1oPC5EcMuQ4IHE8LMBkwuR3Rlax0UFcJ81Acdysq7dJlq98EMDtc0/HmgOFyYJdv7uTvuH8\r\n" +
                "LpsQg51OKQKBgQDobrarBRNu1MFN+NpOu8S0RYg1jP38DstW4+GIt1E/PgmTdzdY8Pz4H1Omq7gD\r\n" +
                "D4lO7X2Y0Z2RE/KC72nR200Du5Xhc5xfhkz53wDJOUTVh89DE6ceJQzsvCcnZECjiuKqxYHC/QE4\r\n" +
                "VXn+LjoA3tPWtuFlBE23WBfD1UcXmT1cHQKBgQCwmRa4UCHFM9/3vhidAyXcDSg+boh3iNuvwR7j\r\n" +
                "/vbndSnycuHzGT0cEZarCFznXFUsLhyRnG7zrzRVMdbEk6D36WJme5x7FFgxCMjdgbodlhATEf/j\r\n" +
                "EKhaIOJGffybRTrscmvaB9CHFmmv9v8amssNJ0p2YfNv5azA+0xDmRjEsQKBgQC634CoAV29Zvq/\r\n" +
                "yMskMB0XXywC+geeJvrfMjo7JEADW8pxMJFkReTtTKqBv2AjvdMEjTbErgtEAEijcv+eRPYngSMg\r\n" +
                "70WTG7Q51UyZfBzWnOlLobpkJx+8Gg5Uwmqh0mEpMcCBV88qTrbVYK8r0ft5jvGquQoO1ZnNzmTp\r\n" +
                "GUBXFQKBgHEQd1qOfVph/sudKMrd6M93jmYmrwp+ddKhiposjvoQHILG1aPt8yTMDuUkyDCF7JXs\r\n" +
                "m+3w51R3EZQJpWl7k2a5XVcyRFCuy1Zb3r/QgwolJMEHYqWkwhs2gasxgqRtXJ/1RzpouiiyzRuv\r\n" +
                "hjVYfivajgyqE2S6HQB8qIl0PCDk\r\n" +
                "-----END PRIVATE KEY-----";
        keyInfo = new KeyInfo(publicKeyPem, privateKeyPem, 86400000, System.currentTimeMillis());
    }

    @Override
    public KeyInfo getKeyInfo() throws NoSuchAlgorithmException {
        return keyInfo;
    }

    @Override
    public PrivateKey getPrivateKey() throws Exception {
        String privateKeyPem = keyInfo.getPrivateKeyPem();
        return KeyConverter.fromPkcs8Pem(privateKeyPem, null);
    }
}
