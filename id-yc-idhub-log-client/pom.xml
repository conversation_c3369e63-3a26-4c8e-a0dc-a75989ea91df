<?xml version="1.0" encoding="UTF-8"?>

<project xmlns="http://maven.apache.org/POM/4.0.0" xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance"
         xsi:schemaLocation="http://maven.apache.org/POM/4.0.0 http://maven.apache.org/xsd/maven-4.0.0.xsd">
    <parent>
        <artifactId>id-yc-province-parent</artifactId>
        <groupId>cn.teleinfo</groupId>
        <version>0.4.0.RELEASE</version>
        <relativePath>../id-yc-province-parent/pom.xml</relativePath>
    </parent>
    <modelVersion>4.0.0</modelVersion>

    <artifactId>id-yc-idhub-log-client</artifactId>
    <name>id-yc-idhub-log-client</name>
    <version>0.4.0.RELEASE</version>
    
    <properties>
        <maven.compiler.source>8</maven.compiler.source>
        <maven.compiler.target>8</maven.compiler.target>
        <project.build.sourceEncoding>UTF-8</project.build.sourceEncoding>
    </properties>

    <dependencies>
        <dependency>
            <groupId>com.alibaba.cloud</groupId>
            <artifactId>spring-cloud-starter-alibaba-nacos-config</artifactId>
        </dependency>
        <dependency>
            <groupId>cn.teleinfo</groupId>
            <artifactId>id-yc-province-common</artifactId>
        </dependency>
        <dependency>
            <groupId>cn.teleinfo</groupId>
            <artifactId>id-yc-province-infrastructure</artifactId>
        </dependency>
        <dependency>
            <groupId>org.springframework.session</groupId>
            <artifactId>spring-session-core</artifactId>
        </dependency>
<!--        <dependency>-->
<!--            <groupId>org.springframework.boot</groupId>-->
<!--            <artifactId>spring-boot-starter-web</artifactId>-->
<!--        </dependency>-->
        <dependency>
            <groupId>org.springframework.boot</groupId>
            <artifactId>spring-boot-starter-data-jpa</artifactId>
        </dependency>
        <dependency>
            <groupId>com.mysql</groupId>
            <artifactId>mysql-connector-j</artifactId>
        </dependency>
        <dependency>
            <groupId>cn.hutool</groupId>
            <artifactId>hutool-all</artifactId>
        </dependency>
        <!-- 该依赖必加，里面有sping对schedule的支持 -->
        <dependency>
            <groupId>org.springframework</groupId>
            <artifactId>spring-context-support</artifactId>
        </dependency>
        <dependency>
            <groupId>io.minio</groupId>
            <artifactId>minio</artifactId>
        </dependency>
        <!--       Java的免费基于JNA的操作系统和硬件信息库 -->
        <!-- https://mvnrepository.com/artifact/com.github.oshi/oshi-core -->
        <dependency>
            <groupId>com.github.oshi</groupId>
            <artifactId>oshi-core</artifactId>
            <version>6.1.5</version>
        </dependency>
        <dependency>
            <groupId>com.squareup.okhttp3</groupId>
            <artifactId>okhttp</artifactId>
            <version>4.9.0</version>
        </dependency>
        <!--Lombok-->
        <dependency>
            <groupId>org.projectlombok</groupId>
            <artifactId>lombok</artifactId>
            <version>1.18.26</version>
            <scope>provided</scope>
        </dependency>
        <dependency>
            <groupId>com.amazonaws</groupId>
            <artifactId>aws-java-sdk-s3</artifactId>
            <version>1.11.125</version>
        </dependency>
        <dependency>
            <groupId>com.github.ben-manes.caffeine</groupId>
            <artifactId>caffeine</artifactId>
        </dependency>

        <dependency>
            <groupId>org.springframework.boot</groupId>
            <artifactId>spring-boot-starter-test</artifactId>
            <scope>test</scope>
        </dependency>
    </dependencies>
    
    <build>
        <plugins>
            <plugin>
                <groupId>com.google.cloud.tools</groupId>
                <artifactId>jib-maven-plugin</artifactId>
                <configuration>
                    <from>
                        <image>dockerhub.qingcloud.com/idpointer/java:8</image>
                    </from>
                    <to>
                        <image>dockerhub.qingcloud.com/idpointer/id-yc-idhub-log-client</image>
                    </to>
                    <!--容器相关的属性-->
                    <container>
                        <!--jvm内存参数-->
                        <environment>
                            <JAVA_OPTS>-Xms1g -Xmx2g -Xss256k</JAVA_OPTS>
                        </environment>
                        <!-- springboot项目的入口类 -->
                        <entrypoint>
                            <arg>/bin/bash</arg>
                            <arg>-c</arg>
                            <!--suppress UnresolvedMavenProperty -->
                            <arg>java ${JAVA_OPTS} -Duser.timezone=PRC -cp /app/resources/:/app/classes/:/app/libs/* cn.teleinfo.idhub.log.client.IdhubLogClientServer
                            </arg>
                        </entrypoint>
                        <ports>
                            <port>4900</port>
                        </ports>
                        <creationTime>USE_CURRENT_TIMESTAMP</creationTime>
                    </container>
                    <allowInsecureRegistries>true</allowInsecureRegistries>
                </configuration>
            </plugin>
        </plugins>
    </build>


</project>
