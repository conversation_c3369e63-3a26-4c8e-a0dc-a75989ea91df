server:
  port: 6200
spring:
  cloud:
    nacos:
      username: nacos
      password: Teleinfo@1122
      config:
        server-addr: 10.14.153.105:30848
        namespace: id-yc-ent
  config:
    import:
      - nacos:id-yc-idhub-log.yml
  datasource:
    driver-class-name: com.mysql.cj.jdbc.Driver
    url: ******************************************************************************************************************************************************************
    username: id-yc
    password: Teleinfo-88
  jpa:
    database: mysql
    database-platform: org.hibernate.dialect.MySQL57Dialect
    generate-ddl: false
    open-in-view: true
    show-sql: true
    hibernate:
      use-new-id-generator-mappings: true
      ddl-auto: none
    properties:
      hibernate:
        enable_lazy_load_no_trans: true
logging:
  file:
    name: id-yc-idhub-log-client
    path: ./id-yc-idhub-log-client/target/logs
  logback:
    rollingpolicy:
      # 单文件的大小，默认10M, 超过之后打包成一个日志文件
      max-file-size: 30MB
      # 日志保存的天数
      max-history: 30
  level:
    #日志级别
    root: info
app:
  log-file-path: classpath:oplog
  task:
    fixed-rate: 60000
  # 对象存储配置
  oss:
    enabled: true
    endpoint: http://10.14.153.105:32065
    access-key: teleinfo
    secret-key: teleinfo
    bucket-name: province
    path-style-access: true
  # 行业大数据平台对象存储配置
  industry-oss:
    enabled: true
    endpoint: http://10.14.153.105:32065
    access-key: teleinfo
    secret-key: teleinfo
    bucket-name: industry
    province-name: 'lhs-beijing'
    path-style-access: true