package cn.teleinfo.idhub.log.client.service;

import cn.hutool.core.io.FileUtil;
import cn.hutool.core.util.ObjectUtil;
import cn.hutool.json.JSONArray;
import cn.hutool.json.JSONObject;
import cn.hutool.json.JSONUtil;
import cn.hutool.system.oshi.OshiUtil;
import cn.teleinfo.idhub.log.client.props.IndustryOssProperties;
import cn.teleinfo.idhub.log.client.props.OssProperties;
import cn.teleinfo.idhub.log.client.template.OssTemplate;
import cn.teleinfo.idhub.log.client.vo.DataLogResponseValueVO;
import cn.teleinfo.idhub.log.client.vo.DataLogVO;
import cn.teleinfo.idpointer.sdk.core.HandleValue;
import cn.teleinfo.idycprovince.common.constant.HandleAutoMaintainStateEnum;
import cn.teleinfo.idycprovince.infrastructure.db.entity.EntPrefixCount;
import cn.teleinfo.idycprovince.infrastructure.db.entity.HandleAutoMaintainStateEntity;
import cn.teleinfo.idycprovince.infrastructure.db.entity.HandleEntity;
import cn.teleinfo.idycprovince.infrastructure.db.repository.EntPrefixCountRepository;
import cn.teleinfo.idycprovince.infrastructure.db.repository.HandleAutoMaintainStateRepository;
import cn.teleinfo.idycprovince.infrastructure.db.repository.HandleRepository;
import com.fasterxml.jackson.databind.ObjectMapper;
import com.github.benmanes.caffeine.cache.Cache;
import com.github.benmanes.caffeine.cache.Caffeine;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.StringUtils;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.core.io.ResourceLoader;
import org.springframework.stereotype.Service;
import org.springframework.util.Base64Utils;

import javax.annotation.Resource;
import java.io.*;
import java.text.SimpleDateFormat;
import java.time.LocalDate;
import java.time.LocalDateTime;
import java.time.format.DateTimeFormatter;
import java.util.*;
import java.util.concurrent.TimeUnit;
import java.util.regex.Matcher;
import java.util.regex.Pattern;
import java.util.stream.Collectors;

@Service
@Slf4j
public class ReadIdhubLogService {

    @Resource
    private OssTemplate ossTemplate;

    @Resource
    private OssTemplate industryOssTemplate;

    @Resource
    private ObjectMapper objectMapper;

    // 日志文件目录
    @Value("${app.log-file-path}")
    private String logFilePath;

    @Resource
    private OssProperties ossProperties;

    @Resource
    private IndustryOssProperties industryOssProperties;

    @Resource
    private EntPrefixCountRepository entPrefixCountRepository;

    @Resource
    private ResourceLoader resourceLoader;

    @Resource
    private HandleRepository handleRepository;

    @Resource
    private HandleAutoMaintainStateRepository handleAutoMaintainStateRepository;

    @Resource
    private IdClientService idClientService;

    Cache<String, Integer> fileCache = Caffeine.newBuilder().expireAfterWrite(30, TimeUnit.MINUTES).build();
    Cache<String, List<String>> handleCache = Caffeine.newBuilder().expireAfterWrite(30, TimeUnit.MINUTES).build();

    /**
     * 读取.log 和 .part 结尾的文件，过滤出60秒未更新的文件
     */
    public void readNeedUploadFile() {
        Map<String, Long> map = new HashMap<>();
        List<String> typeList = Arrays.asList("log", "part", "gap");
        List<File> files = searchDir(logFilePath);
        long currentTimeMillis = System.currentTimeMillis();
        SimpleDateFormat sdf = new SimpleDateFormat("yyyyMMdd");
        SimpleDateFormat sdf1 = new SimpleDateFormat("yyyyMM");

        // 过滤出需要处理的日志文件
        List<File> needProcessFiles = new ArrayList<>();
        for (File file : files) {
            long second = (currentTimeMillis - file.lastModified()) / 1000;
            String type = FileUtil.extName(file);
            if (!typeList.contains(type)) {
                continue;
            }
            if (second > 60) {
                // 日志文件如果60秒未更新，直接处理
                needProcessFiles.add(file);
            } else {
                // 日志文件如果任务调度5次都未被处理, 则直接处理
                Integer count = fileCache.get(file.getName(), key -> 1);
                if (count > 5) {
                    needProcessFiles.add(file);
                    fileCache.invalidate(file.getName());
                } else {
                    fileCache.put(file.getName(), count + 1);
                }
            }
        }

        // log文件命名为gap
        Iterator<File> it = needProcessFiles.iterator();
        List<File> tempFiles = new ArrayList<>();
        while (it.hasNext()) {
            File oldFile = it.next();
            if (FileUtil.pathEndsWith(oldFile, "log")) {
                File newFile = changeLogFileNameToGap(oldFile);
                it.remove();
                tempFiles.add(newFile);
            }
        }
        needProcessFiles.addAll(tempFiles);

        if (needProcessFiles.size() > 0) {
            log.info("监测到{}个日志文件需要处理", needProcessFiles.size());
        }

        // 开始统计解析量
        for (File file : needProcessFiles) {
            log.info("处理文件: {}", file.getName());
            try (
                    FileInputStream fileInputStream = new FileInputStream(file);
                    BufferedReader bufferedReader = new BufferedReader(new InputStreamReader(fileInputStream))) {
                String str;
                Long parseNum = 1L;
                while ((str = bufferedReader.readLine()) != null) {
                    DataLogVO dataLogVO = objectMapper.readValue(str, DataLogVO.class);
                    if (StringUtils.isBlank(dataLogVO.getOperatorTime())) {
                        continue;
                    }
                    if (dataLogVO.getOpCode() != 1) {
                        continue;
                    }
                    if (map.containsKey(dataLogVO.getPrefix())) {
                        map.put(dataLogVO.getPrefix(), parseNum++);
                    }
                    map.put(dataLogVO.getPrefix(), parseNum);


                    writeHandleAutoMaintain(dataLogVO);
                }
            } catch (Exception e) {
                throw new RuntimeException(e);
            }
            //将统计数量存入数据库
            for (String prefix : map.keySet()) {
                EntPrefixCount entPrefixCount = entPrefixCountRepository.findByEntPrefixAndStatisticsDate(prefix, sdf.format(new Date()));
                if (ObjectUtil.isNotEmpty(entPrefixCount)) {
                    //增量更新解析量
                    entPrefixCountRepository.updateHandleQueryNum(map.get(prefix), prefix, sdf.format(new Date()));
                } else {
                    EntPrefixCount entPrefixCountEntity = new EntPrefixCount();
                    entPrefixCountEntity.setProvincePrefix(prefix.substring(0, 7));
                    entPrefixCountEntity.setEntPrefix(prefix);
                    entPrefixCountEntity.setHandleQueryNum(map.get(prefix));
                    entPrefixCountEntity.setHandleCreateNum(0L);
                    entPrefixCountEntity.setHandleDeleteNum(0L);
                    entPrefixCountEntity.setHandleValueAddNum(0L);
                    entPrefixCountEntity.setHandleValueModifyNum(0L);
                    entPrefixCountEntity.setHandleValueRemoveNum(0L);
                    entPrefixCountEntity.setStatisticsMouth(sdf1.format(new Date()));
                    entPrefixCountEntity.setStatisticsDate(sdf.format(new Date()));
                    entPrefixCountEntity.setSourceType(1);
                    entPrefixCountEntity.setCreatedTime(LocalDateTime.now());
                    entPrefixCountEntity.setUpdatedTime(LocalDateTime.now());
                    entPrefixCountRepository.save(entPrefixCountEntity);
                }
                log.info("前缀{}增加解析量{}", prefix, map.get(prefix));
            }
            //压缩文件并上传到S3
            gzFileAndUpload(file);

            //删除日志文件
            FileUtil.del(file);
            log.info("删除日志文件{}成功", file.getName());
        }

    }

    /**
     * 处理以 .log 结尾的⽂件  复制文件，重命名,
     * 定义新⽂件名：<新⽂件名> = <原⽂件名> + . + <时-分-秒_毫秒> + . + gap
     */
    public File changeLogFileNameToGap(File file) {
        DateTimeFormatter formatter = DateTimeFormatter.ofPattern("HH-mm-ss_SSS");
        File newFile = FileUtil.copy(file, new File(file.getParent() + File.separator + file.getName() + "." + LocalDateTime.now().format(formatter) + ".gap"), false);
        FileUtil.del(file);
        return newFile;
    }

    /**
     * 压缩文件并上传到S3
     */
    public void gzFileAndUpload(File file) {
        //压缩文件
        File gzFile = cn.teleinfo.idycprovince.common.file.FileUtil.compressGzip(file, file.getParent());
        //压缩文件名 <uuid>_<文件>.gz
        String gzName = new StringBuilder(UUID.randomUUID().toString()).append("_").append(gzFile.getName()).toString();
        //上传文件到当前节点OSS
        try (InputStream inputStream = new FileInputStream(gzFile)) {
            //上传文件到当前节点OSS
            if (ossProperties.getEnabled()) {
                //S3文件路径 oplog/<硬件指纹>/<uuid>_<⽂件名>.gz
                String s3FileName = new StringBuffer("oplog").append("/").append(getHardware()).append("/").append(gzName).toString();
                ossTemplate.putObject(s3FileName, inputStream);
                log.info("上传文件{}到当前节点OSS成功", s3FileName);
            }
        } catch (Exception e) {
            log.error("上传文件到当前节点OSS异常", e);
        }
        //上传文件到行业节点OSS 目录规则：/LHS-省全称/机器码/年-月-日/文件名.gap.gz
        try (InputStream inputStream = new FileInputStream(gzFile)) {
            //上传文件到行业节点OSS
            if (industryOssProperties.getEnabled()) {
                DateTimeFormatter dateFormat = DateTimeFormatter.ofPattern("yyyy-MM-dd");
                String today = dateFormat.format(LocalDate.now());
                String industryS3FileName = new StringBuffer(industryOssProperties.getProvinceName()).append("/").append(getHardware()).append("/").append(today).append("/").append(gzName).toString();
                industryOssTemplate.putObject(industryS3FileName, inputStream);
                log.info("上传文件{}到行业节点OSS成功", industryS3FileName);
            }
        } catch (Exception e) {
            log.error("上传文件到行业OSS异常", e);
        }
        //删除压缩文件
        FileUtil.del(gzFile);
    }

    /**
     * 获取机器指纹
     */
    public String getHardware() {
        String hardwareUUID = OshiUtil.getHardware().getComputerSystem().getHardwareUUID();
        return hardwareUUID.toLowerCase(Locale.ROOT);
    }

    /**
     * 根据文件路径获得当前路径下所有文件
     *
     * @param path
     * @return
     */
    public List<File> searchDir(String path) {
        File dir = null;
        try {
            dir = resourceLoader.getResource(path).getFile();
        } catch (IOException e) {
            log.info("日志文件获取异常");
            throw new RuntimeException(e);
        }
        //定义集合
        List<File> returnFileList = new ArrayList<>();
        List<File> fileList = new ArrayList<>();
        //将参数dir对象添加到集合
        fileList.add(dir);
        //循环，条件：集合非空
        while (!fileList.isEmpty()) {
            //取出，并删除集合的第一个File对象
            File file = fileList.remove(0);
            //如果此File对象是一个目录
            if (file.isDirectory()) {
                //获取此File对象下所有子文件和子目录的数组，并添加到集合的前面
                fileList.addAll(0, Arrays.asList(file.listFiles()));
            } else {
                returnFileList.add(file);
            }
        }
        return returnFileList;
    }


    // 写入自动维护标识表
    public void writeHandleAutoMaintain(DataLogVO dataLogVO) {
        log.info("读取到日志内容 {}", JSONUtil.toJsonStr(dataLogVO));

        if (dataLogVO.getResponse() == null) {
            log.warn("response is null");
            return;
        }

        if (dataLogVO.getResponse().getValue() == null || dataLogVO.getResponse().getValue().isEmpty()) {
            log.warn("response is null");
            return;
        }

        String handleName = null;
        for (DataLogResponseValueVO each : dataLogVO.getResponse().getValue()) {
            if ("OBJECT_DEFINITION".equals(each.getType())) {
                break;
            }

            if ("OBJECT_HANDLE".equals(each.getType())) {
                handleName = each.getData();
            }
        }

        if (handleName == null) {
            return;
        }

        // 判断是否为本节点实例标识。
        HandleEntity h = handleRepository.findByHandle(handleName);
        if (h == null) {
            log.warn("{} 该标识非本节点标识放弃维护", handleName);
            return;
        }

        // 查询需要维护的标识
        for (DataLogResponseValueVO each : dataLogVO.getResponse().getValue()) {
            if (!"OBJECT_HANDLE".equals(each.getType())) {
                String fieldValue = each.getData();

                if ("OBJECT_INSTANCE".equals(each.getType())) {
                    try {

                        if (dataLogVO.getHandleName().contains("$")) {
                            Pattern pattern = Pattern.compile("_field=([^$&]+)");
                            Matcher matcher = pattern.matcher(dataLogVO.getHandleName());
                            if (matcher.find()) {
                                String field = matcher.group(1);

                                byte[] bytes = Base64Utils.decodeFromString(fieldValue);
                                if (JSONUtil.isTypeJSON(new String(bytes))) {
                                    JSONArray jsonArray = new JSONObject(bytes).getJSONArray("data");
                                    for (Object o : jsonArray) {
                                        fieldValue = replaceHttp(o.toString());
                                        if (Pattern.compile("99\\.\\d+\\.\\d+").matcher(fieldValue).find()) {
                                            String s = resolveHandle(handleName, fieldValue);
                                            if (StringUtils.isEmpty(s)) {
                                                log.warn("解析标识失败，标识不存在 {}", fieldValue);
                                                continue;
                                            }
                                            saveState(handleName, field, s, h);
                                        }
                                    }

                                }
                            }
                        }


                    } catch (Exception e) {
                        log.warn("解析 base 64 err", e);
                    }
                } else {
                    fieldValue = replaceHttp(fieldValue);

                    if (fieldValue.startsWith(handleName)) {
                        continue;
                    }

                    if (Pattern.compile("99\\.\\d+\\.\\d+").matcher(fieldValue).find()) {
                        saveState(handleName, each.getType(), fieldValue, h);
                    }
                }
            }
        }
    }

    private String replaceHttp(String fieldValue) {
        if (Pattern.compile("^(HTTPS|https)://.*").matcher(fieldValue).matches()) {
            // 移除匹配的部分
            fieldValue = fieldValue.replaceFirst("^(HTTPS|https)?://[^/]+(/)", "").replaceFirst("^(/)?", "");
        }
        return fieldValue;
    }

    // handleName 监测标识
    // field 监测字段
    // fieldValue 维护标识
    private void saveState(String handleName, String field, String fieldValue, HandleEntity h) {
        // 判断标识是否已经维护过
        HandleAutoMaintainStateEntity state = handleAutoMaintainStateRepository.findByHandleAndFieldAndReferenceHandle(handleName, field, fieldValue);
        if (state != null) {
            if (HandleAutoMaintainStateEnum.NOT_MAINTAINED.getCode() != state.getState()) {
                return;
            }
        } else {
            state = new HandleAutoMaintainStateEntity();
        }

        // 记录需要维护的日志到数据库
        state.setHandle(handleName);
        state.setField(field);
        state.setDescription(h.getName());
        state.setReferenceHandle(fieldValue);
        state.setState(HandleAutoMaintainStateEnum.NOT_MAINTAINED.getCode());

        handleAutoMaintainStateRepository.save(state);
    }

    private String resolveHandle(String handleName, String fieldValue) {
        List<String> handles = handleCache.getIfPresent(handleName);

        if (handles == null || handles.isEmpty()) {
            handles = new ArrayList<>();
        }

        if (!handles.isEmpty()) {
            for (String handle : handles) {
                if (fieldValue.startsWith(handle)) {
                    return handle;
                }
            }
        }

        // 解析实例标识
        HandleValue[] handleValues = idClientService.resolveHandle(fieldValue);
        Map<String, String> handleValueMap = Arrays.stream(handleValues)
                .collect(Collectors.toMap(HandleValue::getTypeAsString, HandleValue::getDataAsString, (k1, k2) -> k1));
        String s = handleValueMap.get("OBJECT_HANDLE");

        if (StringUtils.isEmpty(s)) {
            return null;
        }

        handles = new ArrayList<>();
        handles.add(s);
        handleCache.put(handleName, handles);

        return s;
    }


}
