package cn.teleinfo.idhub.log.client.service;

import cn.teleinfo.idhub.log.client.idclient.HandleResponseCode;
import cn.teleinfo.idhub.log.client.idclient.RecursionProperties;
import cn.teleinfo.idpointer.sdk.client.GlobalIdClientFactory;
import cn.teleinfo.idpointer.sdk.client.IDClient;
import cn.teleinfo.idpointer.sdk.client.IDClientFactory;
import cn.teleinfo.idpointer.sdk.core.AbstractResponse;
import cn.teleinfo.idpointer.sdk.core.ErrorResponse;
import cn.teleinfo.idpointer.sdk.core.HandleValue;
import cn.teleinfo.idpointer.sdk.core.Util;
import cn.teleinfo.idpointer.sdk.exception.IDException;
import cn.teleinfo.idycprovince.common.exception.BusinessCodeMessage;
import com.abluepoint.summer.common.exception.CodeMessage;
import com.abluepoint.summer.mvc.exception.BusinessRuntimeException;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Service;

import java.io.IOException;
import java.net.InetSocketAddress;
import java.util.Arrays;

/**
 * <AUTHOR>
 * @version 1.0
 * @date Created in 2022/11/10
 * @description
 */
@Slf4j
@Service
@RequiredArgsConstructor
public class IdClientService {

    private final IDClientFactory idClientFactory;

    private final RecursionProperties recursionProperties;

    public IDClient idClient() {
        InetSocketAddress inetSocketAddress = new InetSocketAddress(recursionProperties.getRecursionIp(), recursionProperties.getRecursionPort());
        return idClientFactory.newInstance(inetSocketAddress);
    }

    /**
     * 解析标识
     *
     * @param handle
     */
    public HandleValue[] resolveHandle(String handle) {
        // 解析前缀
        if (handle.indexOf("/") == -1 || handle.startsWith("0.NA/")) {
            HandleValue[] prefixHandleValues = new HandleValue[0];
            try {
                prefixHandleValues = GlobalIdClientFactory.getIdResolver().resolveHandle(handle);
                log.info("解析前缀{}成功, 内容: {}", handle, Arrays.toString(prefixHandleValues));
            } catch (IDException e) {
                HandleResponseCode responseCode = HandleResponseCode.value(e.getCode());
                if (HandleResponseCode.RC_HANDLE_NOT_FOUND.equals(responseCode)) {
                    return new HandleValue[]{};
                }
                log.info("解析前缀 {} 失败, code: {},原因: {}", handle,responseCode.getStatus(),responseCode.getMsg());
                throw new BusinessRuntimeException(CodeMessage.of(BusinessCodeMessage.SYSTEM_IDHUB_ERROR.getCode(), responseCode.getMsg()));
            }
            return prefixHandleValues;
        }
        // 解析标识
        IDClient idClient = idClient();
        try {
            HandleValue[] resolveHandle = idClient.resolveHandle(handle);
            log.info("解析标识{}成功, 内容: {}", handle, Arrays.toString(resolveHandle));
            return resolveHandle;
        } catch (IDException e) {
            HandleResponseCode responseCode = HandleResponseCode.value(e.getCode());
            if (HandleResponseCode.RC_HANDLE_NOT_FOUND.equals(responseCode)) {
                return new HandleValue[]{};
            }
            log.info("解析标识 {} 失败, code: {},原因: {}", handle,responseCode.getStatus(),e.getResponse());
            throw new BusinessRuntimeException(CodeMessage.of(BusinessCodeMessage.SYSTEM_IDHUB_ERROR.getCode(), responseCode.getMsg()));
        } finally {
            try {
                idClient.close();
            } catch (IOException e) {
            }
        }
    }

    /**
     * 解析标识
     *
     * @param handle
     */
    public HandleValue[] resolveHandle(String handle,String token) {
        // 解析前缀
        if (handle.indexOf("/") == -1 || handle.startsWith("0.NA/")) {
            HandleValue[] prefixHandleValues = new HandleValue[0];
            try {
                prefixHandleValues = GlobalIdClientFactory.getIdResolver().resolveHandle(handle);
                log.info("解析前缀{}成功, 内容: {}", handle, Arrays.toString(prefixHandleValues));
            } catch (IDException e) {
                HandleResponseCode responseCode = HandleResponseCode.value(e.getCode());
                if (HandleResponseCode.RC_HANDLE_NOT_FOUND.equals(responseCode)) {
                    return new HandleValue[]{};
                }
                log.info("解析前缀 {} 失败, code: {},原因: {}", handle,responseCode.getStatus(),responseCode.getMsg());
                throw new BusinessRuntimeException(CodeMessage.of(BusinessCodeMessage.SYSTEM_IDHUB_ERROR.getCode(), responseCode.getMsg()));
            }
            return prefixHandleValues;
        }
        // 解析标识
        IDClient idClient = idClient();
        try {
            HandleValue[] resolveHandle = idClient.resolveHandle(handle, null, null, token);
            log.info("解析标识{}成功, 内容: {}", handle, Arrays.toString(resolveHandle));
            return resolveHandle;
        } catch (IDException e) {
            HandleResponseCode responseCode = HandleResponseCode.value(e.getCode());
            if (HandleResponseCode.RC_HANDLE_NOT_FOUND.equals(responseCode)) {
                return new HandleValue[]{};
            }
            log.info("解析标识 {} 失败, code: {},原因: {}", handle,responseCode.getStatus(),responseCode.getMsg());
            throw new BusinessRuntimeException(CodeMessage.of(BusinessCodeMessage.SYSTEM_IDHUB_ERROR.getCode(), responseCode.getMsg()));
        } finally {
            try {
                idClient.close();
            } catch (IOException e) {
            }
        }
    }

    /**
     * 解析标识
     *
     * @param handle
     */
    public HandleValue[] resolveHandleBySecretKey(String handle,String token) {
        // 解析前缀
        if (handle.indexOf("/") == -1 || handle.startsWith("0.NA/")) {
            HandleValue[] prefixHandleValues = new HandleValue[0];
            try {
                prefixHandleValues = GlobalIdClientFactory.getIdResolver().resolveHandle(handle);
                log.info("解析前缀{}成功, 内容: {}", handle, Arrays.toString(prefixHandleValues));
            } catch (IDException e) {
                HandleResponseCode responseCode = HandleResponseCode.value(e.getCode());
                if (HandleResponseCode.RC_HANDLE_NOT_FOUND.equals(responseCode)) {
                    return new HandleValue[]{};
                }
                log.info("解析前缀 {} 失败, code: {},原因: {}", handle,responseCode.getStatus(),responseCode.getMsg());
                throw new BusinessRuntimeException(CodeMessage.of(BusinessCodeMessage.SYSTEM_IDHUB_ERROR.getCode(), responseCode.getMsg()));
            }
            return prefixHandleValues;
        }

        // 解析标识
        IDClient idClient = idClient();
        try {
            HandleValue[] resolveHandle = idClient.resolveHandle(handle, null, null, token);
            log.info("解析标识{}成功, 内容: {}", handle, Arrays.toString(resolveHandle));
            return resolveHandle;
        } catch (IDException e) {
            HandleResponseCode responseCode = HandleResponseCode.value(e.getCode());
            if (HandleResponseCode.RC_HANDLE_NOT_FOUND.equals(responseCode)) {
                return new HandleValue[]{};
            }
            if (HandleResponseCode.RC_INSUFFICIENT_PERMISSIONS.equals(responseCode)) {
                throw new BusinessRuntimeException(CodeMessage.of(BusinessCodeMessage.SYSTEM_IDHUB_ERROR.getCode(), "密钥错误，请重新输入"));
            }

            AbstractResponse response = e.getResponse();
            if (response instanceof ErrorResponse) {
                byte[] message = ((ErrorResponse) response).message;
                throw new BusinessRuntimeException(CodeMessage.of(BusinessCodeMessage.SYSTEM_IDHUB_ERROR.getCode(), Util.decodeString(message)));
            }

            log.info("解析标识 {} 失败, code: {},原因: {}", handle,responseCode.getStatus(),responseCode.getMsg());
            throw new BusinessRuntimeException(CodeMessage.of(BusinessCodeMessage.SYSTEM_IDHUB_ERROR.getCode(), responseCode.getMsg()));
        } finally {
            try {
                idClient.close();
            } catch (IOException e) {
            }
        }
    }
}
