package cn.teleinfo.idhub.log.client.idclient;

import lombok.AllArgsConstructor;
import lombok.Getter;
import lombok.extern.slf4j.Slf4j;

/**
 * 标识操作响应码
 *
 * <AUTHOR>
 */
@Getter
@AllArgsConstructor
@Slf4j
public enum HandleResponseCode {

    RC_SUCCESS(3001,"成功"),
    RC_ERROR(3002,"发生错误"),
    RC_SERVER_TOO_BUSY(3003,"服务器繁忙"),
    RC_PROTOCOL_ERROR(3004,"协议错误"),
    RC_OPERATION_NOT_SUPPORTED(3005,"不支持的操作"),
    RC_RECURSION_COUNT_TOO_HIGH(3006,"递归次数太多"),
    RC_SERVER_BACKUP(3007,"服务器在备份"),

    RC_HANDLE_NOT_FOUND(3100,"没有找到标识"),
    RC_HANDLE_ALREADY_EXISTS(3101,"标识已存在"),
    RC_INVALID_HANDLE(3102,"无效的标识"),

    RC_VALUES_NOT_FOUND(3200,"标识值没有找到"),
    RC_VALUE_ALREADY_EXISTS(3201,"值已存在"),
    RC_INVALID_VALUE(3202,"无效的值"),

    RC_OUT_OF_DATE_SITE_INFO(3300,"过时的站点信息"),
    RC_SERVER_NOT_RESP(3301,"服务器没有响应"),
    RC_SERVICE_REFERRAL(3302,"服务转介"),
    RC_PREFIX_REFERRAL(3303,"前缀转介"),

    RC_INVALID_ADMIN(3400,"无效的管理"),
    RC_INSUFFICIENT_PERMISSIONS(3401,"权限不足"),
    RC_AUTHENTICATION_NEEDED(3402,"需要认证"),
    RC_AUTHENTICATION_FAILED(3403,"认证失败"),
    RC_INVALID_CREDENTIAL(3404,"无效的证书"),
    RC_AUTHEN_TIMEOUT(3405,"认证超时"),
    RC_AUTHEN_ERROR(3406,"认证错误"),

    RC_SESSION_TIMEOUT(3500,"Session超时"),
    RC_SESSION_FAILED(3501,"Session失败"),
    RC_INVALID_SESSION_KEY(3502,"无效的session key"),
    RC_NEED_RSAKEY_FOR_SESSIONEXCHANGE(3503,"需要rsa密钥进行会话交换"),
    RC_INVALID_SESSIONSETUP_REQUEST(3504,"无效的会话建立的请求"),
    RC_SESSION_MESSAGE_REJECTED(3505,"会话消息被拒绝"),
    RC_UNRECOGNIZED_METHOD(3506, "会话消息被拒绝"),

    /**
     * 自定义异常
     */
    RC_UNKNOW_ERROR(2999, "未知错误(-1:UNKNOWN_ERROR)"),

    /**
     * SDK自定义状态码
     */
    INVALID_VALUE(0, "INVALID_VALUE"),
    INTERNAL_ERROR(1, "INTERNAL_ERROR"),
    SERVICE_NOT_FOUND(2, "SERVICE_NOT_FOUND"),
    NO_ACCEPTABLE_INTERFACES(3, "NO_ACCEPTABLE_INTERFACES"),
    UNKNOWN_PROTOCOL(4, "UNKNOWN_PROTOCOL"),
    HANDLE_ALREADY_EXISTS(5, "HANDLE_ALREADY_EXISTS"),
    MESSAGE_FORMAT_ERROR(6, "MESSAGE_FORMAT_ERROR"),
    CANNOT_CONNECT_TO_SERVER(7, "CANNOT_CONNECT_TO_SERVER"),
    UNABLE_TO_AUTHENTICATE(8, "UNABLE_TO_AUTHENTICATE"),
    HANDLE_DOES_NOT_EXIST(9, "HANDLE_DOES_NOT_EXIST"),
    SECURITY_ALERT(10, "SECURITY_ALERT"),
    CONFIGURATION_ERROR(11, "CONFIGURATION_ERROR"),
    REPLICATION_ERROR(12, "REPLICATION_ERROR"),
    MISSING_OR_INVALID_SIGNATURE(13, "MISSING_OR_INVALID_SIGNATURE"),
    MISSING_CRYPTO_PROVIDER(14, "MISSING_CRYPTO_PROVIDER"),
    SERVER_ERROR(15, "SERVER_ERROR"),
    UNKNOWN_ALGORITHM_ID(16, "UNKNOWN_ALGORITHM_ID"),
    STORAGE_RDONLY(18, "STORAGE_RDONLY"),
    UNABLE_TO_SIGN_REQUEST(19, "UNABLE_TO_SIGN_REQUEST"),
    INVALID_SESSION_EXCHANGE_PRIVKEY(20, "INVALID_SESSION_EXCHANGE_PRIVKEY"),
    NEED_RSAKEY_FOR_SESSIONEXCHANGE(21, "NEED_RSAKEY_FOR_SESSIONEXCHANGE"),
    NEED_PUBLICKEY_FOR_SESSIONIDENTITY(22, "NEED_PUBLICKEY_FOR_SESSIONIDENTITY"),
    SESSION_TIMEOUT(23, "SESSION_TIMEOUT"),
    SERVER_ERRINCOMPLETE_SESSIONSETUPOR(24, "INCOMPLETE_SESSIONSETUP"),
    SERVER_CANNOT_PROCESS_SESSION(25, "SERVER_CANNOT_PROCESS_SESSION"),
    ENCRYPTION_ERROR(26, "ENCRYPTION_ERROR"),
    OTHER_CONNECTION_ESTABLISHED(27, "OTHER_CONNECTION_ESTABLISHED"),
    DUPLICATE_SESSION_COUNTER(28, "DUPLICATE_SESSION_COUNTER"),
    SERVICE_REFERRAL_ERROR(29, "SERVICE_REFERRAL_ERROR"),
    CHANNEL_GET_ERROR(1000, "CHANNEL_GET_ERROR"),
    PROMISE_GET_ERROR(1001, "PROMISE_GET_ERROR"),
    CLIENT_ERROR(1002, "CLIENT_ERROR");

    private int status;
    private String msg;

    public static HandleResponseCode value(int status) {
        HandleResponseCode[] sdkResultCodes = values();
        for (HandleResponseCode responseCodeEnum : sdkResultCodes) {
            if (responseCodeEnum.status == status) {
                return responseCodeEnum;
            }
        }
        log.info("IDHUB响应未知的状态码: {}" , status);
        return RC_UNKNOW_ERROR;
    }
}
