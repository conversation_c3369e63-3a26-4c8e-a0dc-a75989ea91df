package cn.teleinfo.idhub.log.client;

import org.springframework.boot.SpringApplication;
import org.springframework.boot.autoconfigure.SpringBootApplication;
import org.springframework.boot.autoconfigure.domain.EntityScan;
import org.springframework.data.jpa.repository.config.EnableJpaAuditing;
import org.springframework.data.jpa.repository.config.EnableJpaRepositories;
import org.springframework.scheduling.annotation.EnableAsync;
import org.springframework.scheduling.annotation.EnableScheduling;


@EnableAsync
@EnableScheduling
@EnableJpaAuditing
@EntityScan("cn.teleinfo.idycprovince.infrastructure.db")
@EnableJpaRepositories(basePackages = {"cn.teleinfo.idycprovince.infrastructure.db"})
@SpringBootApplication
public class IdhubLogClientServer {

    public static void main(String[] args) {
        SpringApplication.run(IdhubLogClientServer.class, args);
    }

}
