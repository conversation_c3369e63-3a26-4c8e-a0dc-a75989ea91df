package cn.teleinfo.idhub.log.client.config;

import cn.teleinfo.idhub.log.client.idclient.RecursionProperties;
import cn.teleinfo.idpointer.sdk.client.GlobalIdClientFactory;
import cn.teleinfo.idpointer.sdk.client.IDClientFactory;
import cn.teleinfo.idpointer.sdk.config.IDClientConfig;
import lombok.RequiredArgsConstructor;
import org.springframework.context.annotation.Bean;
import org.springframework.context.annotation.Configuration;
import org.springframework.core.annotation.Order;

@Configuration
@RequiredArgsConstructor
public class IdClientConfiguration {
    private final RecursionProperties recursionProperties;
    @Order(Integer.MIN_VALUE)
    @Bean
    public IDClientFactory idClientFactory() {
        IDClientConfig idClientConfig = IDClientConfig.builder()
                .recursionServerIp(recursionProperties.getRecursionIp())
                .recursionServerPort(recursionProperties.getRecursionPort())
                .build();
        GlobalIdClientFactory.init(idClientConfig);
        return GlobalIdClientFactory.getIdClientFactory();
    }
}
