package cn.teleinfo.idhub.log.client.vo;

import lombok.Data;

@Data
public class DataLogVO {
    /**
     * 解析的handle
     */
    private String handleName;
    /**
     * 自身ip
     */
    private String idisIp;
    /**
     * 操作类型
     * 1-解析，100-注册，101-删除，102-新增属性，103-移除属性，104-更新数据
     */
    private Integer opCode;
    private String opName;

    /**
     * 日志产生时间
     */
    private String operatorTime;

    /**
     * 前缀
     */
    private String prefix;
    //    @JsonProperty("self")
//    private Boolean self;
//    @JsonProperty("subPrefix")
    private String subPrefix;
    /**
     * 默认值trace
     * "type":"yc_99.2000.1/ACXXX"
     * yc 下划线后面跟上对象标识
     */
    private String type;

    /**
     * 来源ip，一般为递归节点ip
     */
    private String userIp;
//    @JsonProperty("uuid")
//    private String uuid;


    private DataLogResponseVO response;
}
