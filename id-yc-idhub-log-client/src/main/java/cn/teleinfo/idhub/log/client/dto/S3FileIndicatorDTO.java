package cn.teleinfo.idhub.log.client.dto;

import lombok.Data;
import lombok.experimental.Accessors;

@Data
@Accessors(chain = true)
public class S3FileIndicatorDTO {

    /**
     * 省级前缀
     */
    private String subPrefix;

    /**
     * 企业前缀
     */
    private String prefix;

    /**
     * 统计日期
     */
    private String dateStr;

    /**
     * 注册量
     */
    private Long registerNum;

    /**
     * 解析量
     */
    private Long parseNum;

    /**
     * 删除量
     */
    private Long delNum;

    /**
     * 新增属性数
     */
    private Long addAttributeNum;

    /**
     * 更新数据数
     */
    private Long updateAttributeNum;

    /**
     * 移除属性数
     */
    private Long delAttributeNum;


    public String getDateStrPrefix() {
        return this.dateStr + this.prefix;
    }
}
