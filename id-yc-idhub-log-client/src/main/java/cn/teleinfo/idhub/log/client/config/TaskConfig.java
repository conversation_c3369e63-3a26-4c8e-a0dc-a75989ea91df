package cn.teleinfo.idhub.log.client.config;

import cn.teleinfo.idhub.log.client.service.ReadIdhubLogService;
import lombok.RequiredArgsConstructor;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.context.annotation.Configuration;
import org.springframework.scheduling.annotation.Scheduled;

@Configuration
@RequiredArgsConstructor
public class TaskConfig {

    private final ReadIdhubLogService readIdhubLogService;

    /**
     * 读取.log 和 .part 结尾的文件，过滤出60秒未更新的文件
     */
    @Scheduled(fixedDelayString = "${app.task.fixed-rate}")
    public void readNeedUploadFile() {
        readIdhubLogService.readNeedUploadFile();
    }

}
