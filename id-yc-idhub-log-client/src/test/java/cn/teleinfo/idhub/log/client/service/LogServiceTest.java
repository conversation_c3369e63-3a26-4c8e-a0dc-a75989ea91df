package cn.teleinfo.idhub.log.client.service;

import cn.hutool.core.io.FileUtil;
import cn.teleinfo.idhub.log.client.vo.DataLogResponseValueVO;
import cn.teleinfo.idhub.log.client.vo.DataLogVO;
import com.fasterxml.jackson.core.JsonProcessingException;
import com.fasterxml.jackson.databind.DeserializationFeature;
import com.fasterxml.jackson.databind.ObjectMapper;
import org.junit.jupiter.api.Test;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.boot.test.context.SpringBootTest;

import java.nio.charset.Charset;
import java.util.regex.Pattern;


@SpringBootTest
public class LogServiceTest {
    @Autowired
    private ReadIdhubLogService readIdhubLogService;
    @Autowired
    private ObjectMapper objectMapper;

    @Test
    public void readLog() throws JsonProcessingException {
        String s = "{\"handleName\":\"99.2000.4646/ent_user6_bs1\",\"idisIp\":\"\",\"opCode\":1,\"operatorTime\":\"20230410155042\",\"period\":2112005,\"prefix\":\"99.2000.4646\",\"self\":false,\"subPrefix\":\"99.2000\",\"type\":\"trace\",\"userIp\":\"*************\",\"uuid\":\"54_1hjmaga1000crswu66yd5vf1i0vcgqu5\",\"response\":{\"handle\":\"99.1000.1/*********002V9S72QXHHZ7DKEYTPO88K\",\"responseCode\":1,\"value\":[{\"data\":{\"format\":\"string\",\"value\":\"HTTPS://Y2WM.CN/99.1000.1/AC2008477001ZUP1YCMKNTC28IO1M9LCR\"},\"index\":2021,\"type\":\"BAR_TID\"},{\"data\":{\"format\":\"string\",\"value\":\"20510002\"},\"index\":2005,\"type\":\"BA_QR_PROD_ORG_CODE\"},{\"data\":{\"format\":\"string\",\"value\":\"四川中烟工业有限责任公司\"},\"index\":2006,\"type\":\"BA_QR_PROD_ORG_NAME\"},{\"data\":{\"format\":\"string\",\"value\":\"12510101\"},\"index\":2018,\"type\":\"BA_QR_PROD_SITE_CODE\"},{\"data\":{\"format\":\"string\",\"value\":\"成都卷烟厂\"},\"index\":2019,\"type\":\"BA_QR_PROD_SITE_NAME\"},{\"data\":{\"format\":\"string\",\"value\":\"AC008476\"},\"index\":2003,\"type\":\"MA02_QR_BRAND_CODE\"},{\"data\":{\"format\":\"string\",\"value\":\"娇子\"},\"index\":2004,\"type\":\"MA02_QR_BRAND_NAME\"},{\"data\":{\"format\":\"string\",\"value\":\"HTTPS://Y2WM.CN/99.1000.1/*********002V9S72QXHHZ7DKEYTPO88K\"},\"index\":2000,\"type\":\"MA02_QR_CODE_TID\"},{\"data\":{\"format\":\"string\",\"value\":\"\"},\"index\":2020,\"type\":\"MA02_QR_PACKAGE_RATIO\"},{\"data\":{\"format\":\"string\",\"value\":\"99.0151.0101/YMC0402N02_2024112920241129GDXZBZ210010050491400400521\"},\"index\":2007,\"type\":\"MA02_QR_PRODUCE_BATCH_NO\"},{\"data\":{\"format\":\"string\",\"value\":\"2024-11-29\"},\"index\":2008,\"type\":\"MA02_QR_PRODUCE_DATE\"},{\"data\":{\"format\":\"string\",\"value\":\"\"},\"index\":2015,\"type\":\"MA02_QR_RELATION_CODE_ARGS_INFO_productDeviceCode\"},{\"data\":{\"format\":\"string\",\"value\":\"\"},\"index\":2016,\"type\":\"MA02_QR_RELATION_CODE_ARGS_INFO_productDeviceName\"},{\"data\":{\"format\":\"string\",\"value\":\"\"},\"index\":2013,\"type\":\"MA02_QR_RELATION_CODE_ARGS_INFO_shiftCode\"},{\"data\":{\"format\":\"string\",\"value\":\"\"},\"index\":2014,\"type\":\"MA02_QR_RELATION_CODE_ARGS_INFO_shiftName\"},{\"data\":{\"format\":\"string\",\"value\":\"\"},\"index\":2011,\"type\":\"MA02_QR_RELATION_CODE_ARGS_INFO_teamCode\"},{\"data\":{\"format\":\"string\",\"value\":\"\"},\"index\":2012,\"type\":\"MA02_QR_RELATION_CODE_ARGS_INFO_teamName\"},{\"data\":{\"format\":\"string\",\"value\":\"\"},\"index\":2009,\"type\":\"MA02_QR_RELATION_CODE_ARGS_INFO_workshopDeptCode\"},{\"data\":{\"format\":\"string\",\"value\":\"\"},\"index\":2010,\"type\":\"MA02_QR_RELATION_CODE_ARGS_INFO_workshopDeptName\"},{\"data\":{\"format\":\"string\",\"value\":\"AC008476\"},\"index\":2001,\"type\":\"MA02_QR_SPEC_CODE\"},{\"data\":{\"format\":\"string\",\"value\":\"娇子(格调细支)\"},\"index\":2002,\"type\":\"MA02_QR_SPEC_NAME\"},{\"data\":{\"format\":\"string\",\"value\":\"2024-11-29T00:41:28.000+00:00\"},\"index\":2017,\"type\":\"MA02_SCAN_TIME\"},{\"data\":{\"format\":\"string\",\"value\":\"99.1000.1/*********\"},\"index\":1000,\"type\":\"OBJECT_HANDLE\"}]}}";


        ObjectMapper objectMapper = new ObjectMapper();
        objectMapper.configure(DeserializationFeature.FAIL_ON_UNKNOWN_PROPERTIES, false);

        DataLogVO dataLogVO = objectMapper.readValue(s, DataLogVO.class);

        System.out.println(dataLogVO.getHandleName());
        System.out.println("-----");

        for (DataLogResponseValueVO each : dataLogVO.getResponse().getValue()) {
            String handle = each.getData();
            if (Pattern.compile("^(HTTPS|https)://.*").matcher(handle).matches()) {
                // 移除匹配的部分
                handle = handle.replaceFirst("^(HTTPS|https)?://[^/]+(/)", "").replaceFirst("^(/)?", "");
            }

            System.out.println("no http  " + handle);

            if (Pattern.compile("99\\.\\d+\\.\\d+").matcher(handle).find()) {
                System.out.println(each.getData());
                System.out.println(each.getType());
            }
        }


    }

    @Test
    void removeHttp() {
        String handle = "HTTPS://Y2WM.CN/99.1000.1/*********002V9S72QXHHZ7DKEYTPO88K";
        if (Pattern.compile("^(HTTPS|https)://.*").matcher(handle).matches()) {
            // 移除匹配的部分
            handle = handle.replaceFirst("^(HTTPS|https)?://[^/]+(/)", "").replaceFirst("^(/)?", "");
        }
        System.out.println(handle);
    }

    @Test
    void prefixVerify() {
        //String handle = "99.0151.0101/YMC0402N02_2024112920241129GDXZBZ210010050491400400521";
        String handle = "HTTPS://Y2WM.CN/99.1000.1/*********002V9S72QXHHZ7DKEYTPO88K";

        if (Pattern.compile("^(HTTPS|https)://.*").matcher(handle).matches()) {
            // 移除匹配的部分
            handle = handle.replaceFirst("^(HTTPS|https)?://[^/]+(/)", "").replaceFirst("^(/)?", "");
        }

        if (Pattern.compile("99\\.\\d+\\.\\d+").matcher(handle).find()) {
            System.out.println(handle);
            String prefix = handle.substring(0, handle.indexOf("/"));
            System.out.println(prefix);
        }


    }


    @Test
    void writeHandleAutoMaintain() throws JsonProcessingException {
        String s = FileUtil.readString("/Users/<USER>/Desktop/temp.json", Charset.defaultCharset());
        DataLogVO dataLogVO = objectMapper.readValue(s, DataLogVO.class);
        readIdhubLogService.writeHandleAutoMaintain(dataLogVO);
    }
}
