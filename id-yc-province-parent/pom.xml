<?xml version="1.0" encoding="UTF-8"?>
<project xmlns="http://maven.apache.org/POM/4.0.0"
         xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance"
         xsi:schemaLocation="http://maven.apache.org/POM/4.0.0 http://maven.apache.org/xsd/maven-4.0.0.xsd">
    <modelVersion>4.0.0</modelVersion>

    <groupId>cn.teleinfo</groupId>
    <artifactId>id-yc-province-parent</artifactId>
    <packaging>pom</packaging>
    <version>0.4.0.RELEASE</version>

    <parent>
        <groupId>com.abluepoint.summer</groupId>
        <artifactId>summer-parent</artifactId>
        <version>3.3.0-SNAPSHOT</version>
    </parent>

    <modules>
        <module>../id-yc-province-common</module>
        <module>../id-yc-province-manage-server</module>
        <module>../id-yc-province-infrastructure</module>
        <module>../id-yc-idhub-log-client</module>
        <module>../id-yc-mq-transfer</module>
        <module>../id-yc-sdk</module>
    </modules>

    <properties>
        <maven.compiler.source>8</maven.compiler.source>
        <maven.compiler.target>8</maven.compiler.target>
        <java.version>1.8</java.version>
        <mapstruct.version>1.4.2.Final</mapstruct.version>
        <resteasy.spring.version>2.0.0.Final</resteasy.spring.version>

        <spring-boot.version>2.7.14</spring-boot.version>
        <spring-cloud.version>2021.0.8</spring-cloud.version>
        <spring-cloud-alibaba.version>2021.0.5.0</spring-cloud-alibaba.version>
        <spring-security.version>5.7.12</spring-security.version>
    </properties>

    <dependencyManagement>
        <dependencies>
            <dependency>
                <groupId>org.apache.tomcat.embed</groupId>
                <artifactId>tomcat-embed-core</artifactId>
                <version>9.0.99</version>
            </dependency>
            <dependency>
                <groupId>org.apache.tomcat.embed</groupId>
                <artifactId>tomcat-embed-websocket</artifactId>
                <version>9.0.99</version>
            </dependency>
            <dependency>
                <groupId>org.apache.tomcat.embed</groupId>
                <artifactId>tomcat-embed-el</artifactId>
                <version>9.0.99</version>
            </dependency>
            <dependency>
                <groupId>org.springframework</groupId>
                <artifactId>spring-framework-bom</artifactId>
                <version>5.3.39</version>
                <type>pom</type>
                <scope>import</scope>
            </dependency>
            <!-- spring cloud alibaba 依赖 -->
            <dependency>
                <groupId>com.alibaba.cloud</groupId>
                <artifactId>spring-cloud-alibaba-dependencies</artifactId>
                <version>${spring-cloud-alibaba.version}</version>
                <type>pom</type>
                <scope>import</scope>
            </dependency>
            <!-- spring Security 依赖 -->
            <dependency>
                <groupId>org.springframework.security</groupId>
                <artifactId>spring-security-bom</artifactId>
                <version>${spring-security.version}</version>
                <type>pom</type>
                <scope>import</scope>
            </dependency>
            <!-- https://mvnrepository.com/artifact/org.springframework/spring-web -->
            <dependency>
                <groupId>org.springframework</groupId>
                <artifactId>spring-web</artifactId>
                <version>5.3.36</version>
            </dependency>
            <dependency>
                <groupId>cn.tobacco.tcaf</groupId>
                <artifactId>tcaf-api</artifactId>
                <version>1.0.5</version>
            </dependency>
            <dependency>
                <groupId>cn.tobacco.tcaf</groupId>
                <artifactId>tcaf-impl</artifactId>
                <version>1.0.5</version>
            </dependency>
            <dependency>
                <groupId>cn.teleinfo</groupId>
                <artifactId>id-yc-province-common</artifactId>
                <version>${project.version}</version>
            </dependency>
            <dependency>
                <groupId>cn.teleinfo</groupId>
                <artifactId>id-yc-province-core</artifactId>
                <version>${project.version}</version>
            </dependency>
            <dependency>
                <groupId>cn.teleinfo</groupId>
                <artifactId>id-yc-province-infrastructure</artifactId>
                <version>${project.version}</version>
            </dependency>
            <dependency>
                <groupId>cn.teleinfo.id-pointer</groupId>
                <artifactId>id-pointer-sdk</artifactId>
                <version>2.2.4</version>
            </dependency>
            <dependency>
                <groupId>io.netty</groupId>
                <artifactId>netty-all</artifactId>
                <version>4.1.79.Final</version>
            </dependency>
            <dependency>
                <groupId>com.anji-plus</groupId>
                <artifactId>captcha</artifactId>
                <version>1.3.0</version>
            </dependency>
            <dependency>
                <groupId>cn.hutool</groupId>
                <artifactId>hutool-all</artifactId>
                <version>5.8.4</version>
            </dependency>
            <dependency>
                <groupId>com.amazonaws</groupId>
                <artifactId>aws-java-sdk-s3</artifactId>
                <version>1.11.125</version>
            </dependency>
            <!-- 二级节点同步idis -->
            <dependency>
                <groupId>cn.teleinfo.sdk</groupId>
                <artifactId>idis-client</artifactId>
                <version>1.5.0-SNAPTSHOT</version>
            </dependency>
            <dependency>
                <groupId>org.apache.commons</groupId>
                <artifactId>commons-compress</artifactId>
                <version>1.21</version>
            </dependency>
            <dependency>
                <groupId>io.minio</groupId>
                <artifactId>minio</artifactId>
                <version>8.4.0</version>
            </dependency>
            <!--  SQL构建  -->
            <dependency>
                <groupId>org.jooq</groupId>
                <artifactId>jooq</artifactId>
                <version>3.14.4</version>
            </dependency>

            <!--restEasy spring-->
            <dependency>
                <groupId>org.jboss.resteasy.spring</groupId>
                <artifactId>resteasy-spring</artifactId>
                <version>${resteasy.spring.version}</version>
            </dependency>
            <dependency>
                <groupId>org.jboss.resteasy</groupId>
                <artifactId>resteasy-jackson2-provider</artifactId>
                <version>5.0.4.Final</version>
                <exclusions>
                    <exclusion>
                        <groupId>com.fasterxml.jackson.core</groupId>
                        <artifactId>jackson-databind</artifactId>
                    </exclusion>
                    <exclusion>
                        <groupId>com.fasterxml.jackson.core</groupId>
                        <artifactId>jackson-annotations</artifactId>
                    </exclusion>
                </exclusions>
            </dependency>
            <!--Lombok-->
            <dependency>
                <groupId>org.projectlombok</groupId>
                <artifactId>lombok</artifactId>
                <version>1.18.26</version>
                <scope>provided</scope>
            </dependency>
        </dependencies>
    </dependencyManagement>


    <build>
        <plugins>
            <plugin>
                <groupId>org.apache.maven.plugins</groupId>
                <artifactId>maven-gpg-plugin</artifactId>
                <configuration>
                    <skip>true</skip>
                </configuration>
            </plugin>
        </plugins>
        <pluginManagement>
            <plugins>
                <plugin>
                    <groupId>org.apache.maven.plugins</groupId>
                    <artifactId>maven-gpg-plugin</artifactId>
                    <version>1.6</version>
                </plugin>
                <plugin>
                    <groupId>com.google.cloud.tools</groupId>
                    <artifactId>jib-maven-plugin</artifactId>
                    <version>3.2.1</version>
                </plugin>
            </plugins>
        </pluginManagement>
    </build>

    <repositories>
        <repository>
            <id>nexus</id>
            <name>HFQ Nexus</name>
            <url>http://*************:8081/repository/maven-public/</url>
            <releases>
                <enabled>true</enabled>
            </releases>
            <snapshots>
                <enabled>true</enabled>
                <updatePolicy>always</updatePolicy>
            </snapshots>
        </repository>
        <repository>
            <id>nexus-snapshots</id>
            <name>HFQ Nexus Snapshots</name>
            <url>http://*************:8081/repository/maven-snapshots/</url>
            <releases>
                <enabled>false</enabled>
            </releases>
            <snapshots>
                <enabled>true</enabled>
                <updatePolicy>always</updatePolicy>
            </snapshots>
        </repository>
        <repository>
            <id>teleinfo-snapshots</id>
            <name>teleinfo-snapshots</name>
            <url>https://teleinfo.abluepoint.com/nexus/repository/maven-snapshots/</url>
            <releases>
                <enabled>false</enabled>
            </releases>
            <snapshots>
                <enabled>true</enabled>
                <updatePolicy>always</updatePolicy>
            </snapshots>
        </repository>
    </repositories>

    <distributionManagement>
        <repository>
            <id>teleinfo-releases</id>
            <name>teleinfo-releases</name>
            <url>https://teleinfo.abluepoint.com/nexus/repository/maven-releases/</url>
        </repository>

        <snapshotRepository>
            <id>teleinfo-snapshots</id>
            <name>teleinfo-snapshots</name>
            <url>https://teleinfo.abluepoint.com/nexus/repository/maven-snapshots/</url>
        </snapshotRepository>
    </distributionManagement>

</project>
