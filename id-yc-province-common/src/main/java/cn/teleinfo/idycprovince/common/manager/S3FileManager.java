package cn.teleinfo.idycprovince.common.manager;

import com.amazonaws.SdkClientException;
import com.amazonaws.services.s3.model.ListObjectsRequest;

import java.io.InputStream;
import java.util.List;


public interface S3FileManager extends FileManager {

    List<S3ManagerSummary> listObjects(ListObjectsRequest listObjectsRequest)
            throws SdkClientException;

    // TODO: 2022/12/2  新写一个方法，添加文件名称
    String uploadByFileName(InputStream inputStream, long size, String contentType, String extension, String fileName) throws Exception;

    //删除
    void delete(String fullPath);

    String getBucketName();

}
