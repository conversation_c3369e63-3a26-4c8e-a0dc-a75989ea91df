package cn.teleinfo.idycprovince.common.exception;

import com.abluepoint.summer.common.exception.CodeMessage;

import java.util.HashMap;
import java.util.Map;

/**
 * 前台异常枚举类(状态码设计参考阿里巴巴规范)
 * 1 表示成功
 * 2 表示登录相关
 * 3 表示错误来源于用户，比如参数错误，用户安装版本过低，用户支付超时等问题
 * 4 表示错误来源于当前系统，往往是业务逻辑出错，或程序健壮性差等问题
 * 5 表示错误来源于第三方服务
 *
 * <AUTHOR>
 * @date 2022年7月4日
 */

public enum BusinessCodeMessage implements CodeMessage {

    VALIDATE_BIND_ERROR(30000, "参数校验异常, 请查看是否缺少参数或参数格式错误及长度超出限制"),

    DATA_NOT_EXIST(30001, "数据不存在，请稍后刷新重试"),

    HANDLE_NOT_EXIST(30001, "标识不存在"),

    PREFIX_EXIST(30002, "企业前缀已存在"),

    PREFIX_NOT_EXIST(30002, "企业前缀不存在"),

    IDHUB_ERROR(30002, "IDHUB服务异常"),

    ENT_NOT_EXIST(30003, "企业不存在"),

    IP_TYPE_ILLEGAL(30004, "IP类型非法"),

    PREFIX_AUDITED(30005, "企业申请已审核，不能重复审核"),

    IP_FORMAT_ERROR(30006, "IP格式错误"),

    PREFIX_FORMAT_ERROR(30007, "企业前缀格式错误，请输入正确格式的企业前缀。例：99.2000.2222"),

    APP_HANDLE_FORMAT_ERROR(30007, "格式错误，请重新输入"),

    PREFIX_BETO_ERROR(30008, "system.entPrefix.notbelong.secondaryPrefix.error"),

    OBJECT_HANDLE_EXIST(30009, "标识已存在"),

    HANDLE_EXIST(30009, "标识已存在"),

    OBJECT_HANDLE_BELONG_ENT_NODE(30009, "标识属于当前企业节点，请到【标识注册-编辑】中进行维护"),

    WILDCARD_HANDLE_EXIST(30010, "通配符已存在"),

    PREFIX_NOT_HOSTING(30011, "前缀尚未托管"),

    OBJECT_HANDLE_NOT_EXIST(30012, "未找到此实例标识匹配的标识"),

    PREFIX_YES_HOSTING(30013, "前缀已托管"),

    OBJECT_HANDLE_TYPE_DUPLICATION(30014, "基本属性-英文名称不能重复"),

    HANDLE_TYPE_DUPLICATION(30014, "基础属性-英文名称不能重复"),

    HANDLE_TYPE_DESCRIPTION_DUPLICATION(30014, "基础属性-中文名称不能重复"),

    OBJECT_HANDLE_FIELD_DUPLICATION(30014, "标识属性重复，维护失败，请重新维护"),

    PREFIX_RULE_ERROR(300015, "前缀格式不符合统一规则，请输入2+4+4格式前缀"),

    OBJECT_PROTING_ERROR(30015, "标识上报接收接口连接错误"),

    USER_REPORT_ERROR(90001, "用户上报接口连接错误"),

    APP_REPORT_ERROR(90002, "应用上报接口连接错误"),
    ENT_PREFIX_ERROR(90004, "企业前缀同步接口连接错误"),

    CHANNEL_REPORT_ERROR(90003, "通道上报接口连接错误"),
    DATA_COPY_ERROR(90004, "通道同步错误"),
    HANDLE_REPORT_ERROR(90005, "标识同步错误"),
    DATA_PROTING_ERROR(30015, "企业标识数据上报接收接口连接错误"),

    PREFIX_AUDITING_EXIST(30016, "该前缀申请正在审核中，请更换其他前缀"),

    APP_DELETE_ERROR(30017, "该应用关联有生效的标识，不能进行删除！"),

    OBJECT_HANDLE_OR_APP_DELETE_ERROR(30018, "已有标识或应用关联该前缀，不能进行删除！"),

    DATA_SERVICE_CONNECT_ERROR(30019, "数据服务连接错误，请重新添加"),

    DATA_SERVICE_NAME_NO_REPEAT(30020, "数据服务名称不能重复"),

    DATA_SERVICE_NOT_EXIST(34021, "数据服务不存在"),
    DATA_CHANNEL_NOT_EXIST(34021, "数据通道不存在"),
    DATA_SERVICE_ID_NOT_EXIST(34021, "数据源ID不能为空"),

    DATASOURCE_NOT(34021, "数据通道ID非法或为空"),
    FIELDTYPECODE_NOT(34021, "属性类型非法"),
    ENTITYTYPE_NOT(34021, "实体类型非法"),
    DATA_CHANNEL_TYPE_NOT_EXIST(34022, "数据通道类型非法或为空，1.基本 2.数组"),

    PUBLIC_READ_NOT_EXIST(33021, "属性权限取值为空或不合法"),

    FIELD_NOT_NULL(35021, "标识属性英文名不能空或者长度大于30"),

    FIELDVALUE_NOT(35021, "标识属值为空或长度超过255"),

    REMARK_NOT(35021, "标识属性备注长度超过100"),

    DESCRIPTION_NOT_NULL(35021, "标识属性中文名不能空或长度大于30"),

    MASTER_DATA_TYPE_IS_EXIST(35021, "当前主数据类型已注册，请重新选择主数据标识"),


    DATA_SERVICE_SOURCE_ERROR(30022, "数据服务获取数据源列表失败"),

    DATA_SERVICE_IN_USE_DELETE(30023, "标识属性已使用，不可删除"),

    DATA_SERVICE_SOURCE_CONNECT_TEST_ERROR(30024, "未获取到数据，请输入正确的英文名称"),

    DATA_SERVICE_ADDRESS_NO_REPEAT(30025, "数据服务地址不能重复"),

    FIELDINDEX_NOT_EXIST(30026, "索引不能为空"),
    PUBLICREAD_ERROR(34026, "publicRead非法，0非公开1公开"),
    PUBLICREAD_NULL(34026, "publicRead不能为空"),

    FIELD_NOT_EXIST(30026, "英文名称不能为空"),

    QUERYPROP_NOT_EXIST(30026, "queryprop不能为空"),

    PARAMPROP_ERROR(33026, "paramprop需填当前标识属性"),
    PARAMPROPHandle_ERROR(33046, "referenceHandle引用标识不存在或为空"),
    QUERYPROP_ERROR(33027, "queryprop需填关联标识属性"),
    REFERENCEHANDLEPROP_ERROR(33028, "referenceHandleProp需填关联标识属性"),
    DESCRIPTION_ERROR(33029, "description不能重复"),
    EDIT_FIELD_ERROR(33129, "edit数组中item属性英文名不存在"),

    DESCRIPTION_NULL(33030, "description不能为空"),
    FIELD_NULL(33031, "field不能为空"),

    FIELD_TYPE_NULL(33031, "fieldType不能为空"),

    CLOSE_USER_ADD(33032, "新增用户已关闭，请联系管理员"),


    HANDLE_AUDIT_STATE_ERROR(30111, "标识审核状态非法，请检查"),
    HANDLE_AUDIT_PENDING_ERROR(30112, "该对象正在审核中，无法进行编辑！"),
    HANDLE_AUDIT_PENDING_DEL_ERROR(30113, "该对象正在审核中，无法进行删除！"),
    HANDLE_AUDIT_APPROVED_ERROR(30114, "该对象已经通过审核"),
    HANDLE_AUDIT_REJECTED_ERROR(30115, "该对象已经驳回审核"),

    HANDLE_AUDIT_REMARK_ERROR(30116, "审批备注非法"),
    HANDLE_AUDIT_TYPE_ERROR(30117, "标识审核类型非法，请检查"),


    PULL_MONITOR_CHANNEL_LIST_ERROR(30027, "获取数据通道列表失败"),
    PULL_MONITOR_CHANNEL_OBJECT_HANDLE_RELATION_ERROR(30028, "获取对象标识关联关系失败"),

    INSTANCE_HANDLE_NOT_OBJECT_HANDLE_STARTS_WITH(30029, "该实例标识不属于当前对象标识，请重新输入正确的实例标识。"),

    OPEN_API_HANDLE_DATA_SOURCE_NAME_IS_NULL_ERROR(30030, "标识解析数据源类型属性，数据源名称不能为空"),

    OPEN_API_HANDLE_DATA_SOURCE_ID_IS_NULL_ERROR(30031, "标识解析数据源类型属性，数据源ID不能为空"),

    OPEN_API_HANDLE_DATA_SERVICE_ID_IS_NULL_ERROR(30032, "标识解析数据源类型属性，数据服务ID不能为空"),

    OPEN_API_HANDLE_DATA_CHANNEL_ID_IS_NULL_ERROR(30033, "标识解析数据源类型属性，数据通道id不能为空"),

    OPEN_API_HANDLE_TABLE_NAME_IS_NULL_ERROR(30034, "标识解析数据源类型属性，所属表名称不能为空"),

    OPEN_API_HANDLE_COLUNM_NAME_IS_NULL_ERROR(30035, "标识解析数据源类型属性，所属字段名称不能为空"),

    OPEN_API_HANDLE_DATABASE_NAME_IS_NULL_ERROR(30036, "标识解析数据源类型属性，数据库名称不能为空"),

    OPEN_API_HANDLE_DATABASE_IP_IS_NULL_ERROR(30037, "标识解析数据源类型属性，数据库ip不能为空"),

    OPEN_API_DELETE_APPID_IS_NOT_ERROR(30038, "该标识不属于当前应用，不能删除！"),

    OPEN_API_HANDLE_ADD_ERROR(30039, "至少新增一条标识解析数据源属性"),

    OPEN_API_HANDLE_ITEM_IS_NULL_ERROR(30040, "标识修改时属性集合不能为空"),

    OPEN_API_HANDLE_ITEM_FIELD_IS_NULL(30041, "对外接口-属性英文名称不允许修改"),

    OPEN_API_DATA_SERVICE_ID_OR_DATA_CHANNEL_ID_IS_NOT_NULL(30042, "对外接口-属性所属服务id或数据通道id不能为空"),

    OPEN_API_DATA_SOURCE_ID_IS_NOT_NULL(30043, "对外接口-属性数据源id不能为空"),

    MIDDLEGROUND_LISTORGROOTORGUNITS_IS_NULL(30050, "用户中心-获取根节点组织异常"),

    MIDDLEGROUND_PAGEQUERYORGUNITBYATTR_IS_NULL(30050, "用户中心-获取根节点下一级组织异常"),

    USERNAME_IS_REPETITION_ERROR(30051, "新增用户-该用户已添加"),

    /**
     * 身份管理相关提示
     */
    HANDLE_CODE_IN_ENT_IS_EXIST(30090, "此应用身份标识已存在，请重新输入"),

    /**
     * 用户端错误
     */
    USER_ERROR(39000, "user.error"),

    /**
     * 没有权限
     */
    USER_NO_PERMISSION(31001, "user.no.permission"),

    /**
     * 账户锁定
     */
    USER_LOCKED(31002, "user.locked"),
    EMAIL_LOCKED(31003, "email.locked"),

    /**
     * 系统内部错误
     */
    SYSTEM_EXECUTION_ERROR(40000, "system.error"),

    FILE_EXECUTION_ERROR(39999, "上传文件存储异常，导出报告失败"),

    ROLE_NOT_EXIST(40001, "角色ID不能为空"),

    PWD_NOT_DUPLICATE(40002, "新密码两次结果不同"),

    USERNAME_NOT_EXIST(40003, "用户名重复"),
    PHONE_IS_EXIST(40004, "手机号重复"),
    EMAIL_IS_EXIST(40005, "邮箱重复"),
    PWD_NOT_EQUAL(40005, "原密码不匹配"),
    ROLE_IS_UNDELETE(40006, "该角色为系统默认角色，不可更改"),
    ID_NOT_EXIST(40007, "ID不能为空"),
    NO_ALLOW_DELETE_SELF(44001, "禁止删除当前登录用户账号"),
    CODE_NOT_EXIST(40007, "验证码不能为空"),
    EMAIL_NOT_EXIST(40007, "邮箱不能为空"),
    HANDLE_NOT_MATCH(40008, "标识不符合格式要求"),

    PWD_EQUAL_OLD(40009, "新密码不能与原密码相同"),

    PWD_IDENTIFY_FAIL(40010, "密码解密失败"),

    AUTH_NOT_EXIST(40011, "权限参数不能为空"),
    USER_NOT_EXIST(40012, "用户不存在"),

    PWD_FORMAT_ERROR(40013, "密码格式错误"),

    PHONE_FORMAT_ERROR(40014, "手机号格式错误"),
    EMAIL_FORMAT_ERROR(40015, "邮箱格式错误"),
    PHONE_NOT_NULL(40015, "手机号不能为空"),
    EMAIL_NOT_NULL(40015, "邮箱不能为空"),
    ENT_NOT_NULL(40016, "企业信息为空"),
    ROLE_NAME_IS_EXIST(40017, "角色名称重复"),

    LEVEL_TYPE_IS_ERROR(40018, "查询类型不合法"),
    LEVEL_TYPE_IS_NULL(40019, "查询类型不能为空"),
    ROLE_HAS_USERS(40020, "角色下存在用户，不可删除"),

    PWD_NOT_NULL(40021, "密码不能为空"),

    ROLE_NAME_NOT_NULL(40022, "角色名称不能为空"),

    ROLE_NAME_ALREADY_SET(40023, "该角色名称已预设，请换名称"),

    TYPE_NOT_NULL(40024, "查询用户类型不能为空"),
    TYPE_NOT_EXIST(40025, "查询用户类型不合法"),

    ENT_NAME_NOT_NULL(40026, "企业名称不能为空"),

    KEY_NOT_EXIST(40026, "idhub入参key不能为空"),
    VALUE_NOT_EXIST(40027, "idhub入参value不能为空"),
    TTL_NOT_EXIST(40028, "idhub入参ttl不能为空"),

    IDHUB_DATA_NOT_EXIST(40029, "idhub参数不能为空"),
    TTL_NOT_NUMBER(40030, "idhub入参ttl必须是数字"),
    APPID_NOT_EXIST(40031, "appId不能为空"),
    PRIVATE_KEY_NOT_EXIST(40032, "私钥不能为空"),
    BIZ_CODE_NOT_EXIST(40033, "组织机构编码不能为空"),
    PROVINCE_TENANT_USER_EXIST(40034, "该省级租户下有用户存在，不能删除"),
    PROVINCE_TENANT_HANDLE_EXIST(40035, "该省级租户下有标识存在，不能删除"),
    PROVINCE_TENANT_MAINTAIN_HANDLE_EXIST(40035, "该省级租户下有维护标识存在，不能删除"),
    PROVINCE_TENANT_ENT_EXIST(40035, "该省级租户下有企业数据存在，不能删除"),
    NODE_NAME_NOT_EXIST(40036, "节点名称不能为空"),
    PROVINCE_TENANT_NOT_EXIST(40037, "多租户模式下，租户ID不能为空"),
    PROVINCE_TENANT_PREFIX_EXIST(40038, "该前缀在系统已存在"),
    PROVINCE_TENANT_BIZCODE_EXIST(40039, "该组织机构编码在系统已存在"),
    PROVINCE_TENANT_PREFIX_CHECK_ERROR(40040, "省级前缀格式错误"),
    PROVINCE_ID_NOT_EXIST(40040, "省级Id不能为空"),
    URL_PATTERN_ERROR(40041, "URL格式错误"),
    PUBLIC_KEY_PATTERN_ERROR(40042, "私钥格式错误"),
    ACCESS_DENIED(40043, "该接口无访问权限"),
    PREFIX_DATA_NOT_EXIST(31001, "当前前缀标识数据不存在，请稍后刷新重试"),
    PREFIX_CHECK(31002, "请输入非当前创建标识所属企业前缀"),


    HANDLE_NOT_FOUND(40050, "标识不能为空，请刷新页面后重试"),
    OBJECT_HANDLE_NOT_EXISTS(40051, "标识不存在，请刷新页面后重试"),
    OBJECT_HANDLE_ITEM_NOT_EXISTS(40052, "标识属性无数据，请先创建属性"),
    OBJECT_HANDLE_ITEM_DATA_NOT_EXISTS(40053, "标识属性数据不存在"),
    OBJECT_HANDLE_ITEM_DATA_NO_PERMISSION(40054, "标识属性没有查看权限"),
    HANDLE_DATA_NOT_EXISTS(40080, "标识数据不存在"),
    HANDLE_DATA_ABNORMAL(40054, "标识数据格式不合法，请检查"),
    HANDLE_PATTERN_ABNORMAL(40055, "标识格式异常"),
    REFERENCE_HANDLE_QUERY_ABNORMAL(40056, "关联标识并行查询失败"),
    OBJECT_HANDLE_ITEM_FIELD_TYPE_ERROR(40057, "标识属性类型错误"),
    IDCLIENT_SQUIRE_FAIL(40058, "IdClientFactory获取失败"),

    USER_UNBOUND_APP(40059, "用户未绑定应用"),
    USER__DELETE_ERROR(40059, "该用户关联有生效的标识，不能进行删除！"),
    USER_NOT_APP(40081, "请登录应用账号进行标识维护"),
    ORG_INFO_GET_ERROR(40082, "远程获取组织机构信息失败"),

    /**
     * 托管服务器异常
     */
    HOSTING_PROXY_USING(40059, "该托管服务器已分配企业"),

    HOSTING_SERVER_NOT_EXIST(40060, "托管服务器不存在"),

    HOSTING_SERVER_IP_NOT_NULL(40061, "托管服务器内网IP不能为空"),
    HOSTING_SERVER_IP_RESLOVE_NOT_NULL(40062, "托管服务器外网IP不能为空"),

    HOSTING_SERVER_NAME_NOT_NULL(40063, "托管服务器名称不能为空"),

    HOSTING_SERVER_IP_TYPE_NOT_NULL(40064, "托管服务器内网IP类型不能为空"),
    HOSTING_SERVER_IP_TYPE_RESLOVE_NOT_NULL(40065, "托管服务器外网IP类型不能为空"),

    HOSTING_SERVER_TCP_NOT_NULL(40066, "托管服务器内网TCP不能为空"),
    HOSTING_SERVER_TCP_RESLOVE_NOT_NULL(40067, "托管服务器外网TCP不能为空"),

    HOSTING_SERVER_UDP_NOT_NULL(40068, "托管服务器内网UDP不能为空"),
    HOSTING_SERVER_UDP_RESLOVE_NOT_NULL(40069, "托管服务器外网UDP不能为空"),

    HOSTING_SERVER_HTTP_NOT_NULL(40070, "托管服务器内网HTTP不能为空"),
    HOSTING_SERVER_HTTP_RESLOVE_NOT_NULL(40071, "托管服务器外网HTTP不能为空"),

    HOSTING_SERVER_NAME_ILLEGAL(40064, "托管服务器名称不合法"),
    HOSTING_SERVER_PORT_ILLEGAL(40065, "托管服务器端口号不合法"),

    HOSTING_SERVER_NAME_IS_EXIST(40066, "托管服务器名称已存在"),

    HOSTING_APPLY_NOT_EXIST(40072, "托管申请不存在"),
    HOSTING_SERVER_ID_NOT_NULL(40073, "托管服务器id为空"),
    HOSTING_APPLY_UNHOSTING(40074, "托管申请不是未托管状态"),
    HOSTING_APPLY_UNAUDIT(40075, "托管申请不是待审核状态"),
    HOSTING_APPLY_UNHOSTING_AND_AUDIT(40076, "托管申请是未托管或审核中状态无法进行取消托管"),
    /**
     * 实例标识异常
     */
    INSTANCE_ITEM_NOT_NULL(40067, "实例标识属性不能为空，请检查数据"),
    INSTANCE_TO_JSON_ERROR(40068, "实例标识转换json异常"),
    INSTANCE_TO_IDHUB_ERROR(40069, "实例标识发送IDHUB异常"),
    INSTANCE_IS_NULL(40070, "实例标识为空，请检查数据"),

    INSTANCE_ITEM_NAME_IS_DUPLICATE(40071, "实例标识属性名称不可重复，请检查数据"),
    INSTANCE_NAME_IS_DUPLICATE(40072, "实例标识名称不可重复，请检查数据"),
    INSTANCE_ITEM_VALUE_NOT_NULL(40073, "实例标识属性值不可为空，请检查数据"),
    INSTANCE_HANDLE_IS_DUPLICATE(40074, "实例标识不可重复，请检查数据"),
    PREFIX_IS_NULL(40078, "前缀信息不能为空"),
    AUDIT_STATUS_IS_NULL(40079, "审核状态值不能为空"),
    ORG_CODE_IS_NULL(40079, "企业编码不能为空"),
    HANDLE_IS_NULL_ERROR(40080, "标识tid不能为空，请检查数据"),

    /**
     * 当前用户角色没有权限*
     */
    USER_TYPE_IS_NOT_AUTH(40090, "当前账号角色没有权限，请检查身份标识是否正确"),

    /**
     * 权限申请--申请身份不能为空*
     */
    HANDLE_USER_IS_NOT_NULL(40091, "申请身份不能为空，请选择申请身份！"),

    HANDLE_ALREADY_HAVE_AUTH(40092, "标识已有权限，请输入其他标识"),

    HANDLE_APPLY_IS_AUDITED(40093, "该申请已被其他管理员审核，请刷新页面"),

    HANDLE_IS_DELETED(40094, "该标识已被删除，请将该申请驳回"),

    REFERENCEHANDLE_NOT_IS_ENT(40095, "关联标识/关联属性，关联的标识不属于本企业的标识，请重新输入"),

    REFERENCEHANDLE_NOT_EXIT(40095, "关联标识/关联属性，referenceHandle不能为空"),

    /**
     * IDHUB服务异常
     */
    SYSTEM_IDHUB_ERROR(40100, "system.idhub.error"),

    SYSTEM_IDHUB_REC_ERROR(40100, "递归节点连接失败"),

    SYSTEM_IDHUB_HANDLE_NOT_FOUND(40100, "标识不存在"),

    SYSTEM_HANDLE_DATA_DESERIALIZE_ERROR(40200, "数据反序列化失败"),
    HANDLEUSER_PRIVATEKEY_ERROR(40200, "标识身份私钥解析失败"),

    FILE_STORE_FAIL(50010, "文件上传失败"),
    FILE_DOWN_FAIL(50011, "文件下载失败"),
    FILE_SIZE_ILLEGAL(50012, "文件大小不合法"),
    FILE_TYPE_ILLEGAL(50012, "文件格式不合法"),

    PREPARE_PREFIX_EXIST(50013, "预分配企业前缀已存在"),
    PREFIX_PREPARE_IS_NULL(50014, "前缀信息查询不存在"),
    ROLE_CODE_IS_EXIST(50015, "角色编码重复"),
    PREFIX_EXCEL_COUNT_LIMIT(50016, "文件前缀行数限制超过5000，请删减行数"),
    HANDLE_EXCEL_COUNT_LIMIT(50027, "批量注册行数限制超过2000，请删减行数"),
    HANDLE_EXCEL_COUNT_NOT_LIMIT(50028, "文件为空或无注册标识"),
    USER_NOT_NULL(50017, "用户名不能为空"),

    USER_PASSWORD_ERROR(50018, "用户名或密码错误"),
    USER_CENTER_ERROR(50019, "业务中台接口调用异常"),
    USER_CENTER_NOT_EXIST_USER(50020, "业务中台不存在此员工"),
    USER_CENTER_BINGING_ERROR(50021, "业务中台员工绑定异常"),
    USER_CENTER_NOT_EXIST_BINDING(50022, "不存在绑定关系"),
    USER_CENTER_UNBINDING_FAIL(50023, "调用业务中台解绑失败"),

    APP_NOT_EXIST(50024, "应用不存在"),
    USER_EXIST_BINDING(50025, "该用户已关联业务中台账户"),
    USER_CENTER_EXIST_BINDING(50026, "该中台账户已绑定系统用户"),
    DATA_CHANNEL_NAME_IS_EXSIT(50027, "数据通道名称重复，请重新输入"),

    /**
     * 标识服务异常
     */
    HANDLE_FIELD_VALUE_NOT_NULL(31000, "标识属性类型为固定值，属性值不能为空，请检查数据"),
    HANDLE_DATA_SOURCE_NOT_NULL(31001, "标识属性类型为标识解析数据源，数据源不能为空，请检查数据"),
    HANDLE_DATA_CHANNEL_TYPE_IS_NULL(31001, "标识属性类型为标识解析数据源，数据通道类型不能为空，请检查数据"),
    HANDLE_DATA_SERVICE_NOT_NULL(31001, "标识属性类型为标识解析数据源，数据服务不能为空，请检查数据"),
    HANDLE_HANDLE_VALUE_REFERENCE_NOT_NULL(31002, "标识属性类型为标识值，关联信息不能为空，请检查数据"),
    HANDLE_HANDLE_PROP_REFERENCE_NOT_NULL(31003, "标识属性类型为标识-属性，关联信息不能为空，请检查数据"),
    HANDLE_HANDLE_RETURN_SEND_ERROR(31004, "扩展属性回传发送失败，请再次重试"),
    HANDLE_VERSION_ALL_UPDATED(31005, "标识内容已变更，请刷新后再次重试"),

    OBJECT_HANDLE_QUERY_PROP_IS_NULL(31006, "关联标识类型，关联标识属性不能为空"),

    /**
     * 省级上报行业服务异常
     */
    REPORT_ERROR(50000, "接口连接错误"),
    REPORT_SUCCESS(1000, "成功"),
    REPORT_CHECK_ERROR(500, "接口参数校验失败"),
    REPORT_CREATE_SIGN_ERROR(50001, "生成签名错误"),
    REPORT_MISSING_PARAMETERS(2003, "缺少参数"),
    REPORT_NOT_IN_WHITELIST(2004, "不在白名单范围内"),
    REPORT_PARAMETER_IS_INVALID(2005, "参数不合法"),
    REPORT_AUTHENTICATION_ERROR(2200, "认证错误"),
    REPORT_OBJECT_ALREADY_EXISTS(2302, "对象已存在"),
    REPORT_OBJECT_DOES_NOT_EXIST(2303, "对象不存在"),
    REPORT_OPERATION_FAILED(2400, "操作失败"),
    REPORT_PREFIX_STOP(2522, "省级前缀已停用"),
    REPORT_PREFIX_UNREGIST(2523, "省级前缀已注销"),
    REPORT_PREFIX_NOT_BELONG(2524, "省级前缀不属于该省级节点"),
    REPORT_ENT_ID_NOT_EXIST(2525, "企业ID不存在"),

    APP_NAME_IS_EXIST(2526, "应用名称重复，请更换名称"),
    CAPTCHA_CHECK_FAILE(2527, "邮箱验证码错误"),
    CAPTCHA_CHECK_TIME_OUT(2532, "验证码超时，请重新发送"),
    CAPTCHA_CHECK_NO_SENT(2533, "请先发送邮箱验证码"),
    SYSTEM_EMAIL_NOT_FOUNT(2528, "若未收到验证码，请联系系统管理员"),
    CACHE_READ_FAILED(2529, "缓存读取失败"),
    PASSWORD_CHECKED_FAILED(2530, "密码验证失败"),
    EMAIL_HAD_SENT(2531, "邮件发送过于频繁，请稍后再试"),

    /**
     * 消息转发服务异常
     */
    MQ_TRANSFER_PREFIX_NOT_EXIST(3001, "该前缀不存在行业云中"),
    MQ_SEND_FAILED(3002, "MQ/Http消息发送失败"),
    MQ_MESSAGE_PARSE_FAILED(3003, "MQ消息解析失败"),
    MQ_HTTP_REQUEST_FAILED(3004, "MQHttp消息发送失败"),


    /**
     * OpenAPI 返回异常
     */
    OPENAPI_DATA_CHANNEL_NAME_IS_EXSIT(50027, "数据通道名称重复，请重新输入"),
    OPENAPI_DATA_CHANNEL_ID_IS_EXSIT(50027, "数据通道ID重复，请重新输入"),
    OPENAPI_DATA_SERVICE_NOT_APP(50027, "数据服务不属于当前应用，请重新输入"),
    OPENAPI_DATA_CHANNEL_OBJECT_HANDLE_IS_NOT_APP(60001, "所属对象标识不属于当前应用"),
    OPENAPI_DATA_CHANNEL_IS_NOT_APP(60001, "数据通道不属于当前应用，请重新输入"),
    OPENAPI_ENT_PREFIX_NOT_MATCH(60001, "前缀不匹配"),
    OPENAPI_USER_DEAL_NO_PERMISSION(60002, "没有权限"),
    ROW_LIMIT(70002, "Excel行数不能超过2000行"),
    OPENAPI_HANDLE_EXIST(60003, "标识已存在"),
    OPENAPI_HANDLE_TYPE_DUPLICATION(60004, "参数校验失败，基础属性-英文名称不能重复"),
    OPENAPI_HANDLE_ERROR(60005, "参数校验失败"),
    OPENAPI_ENT_NOT_EXIST(60006, "企业不存在"),
    OPENAPI_APP_NOT_EXIST(60007, "所属应用不能为空"),
    OPENAPI_HANDLE_NOT_ITSELF(60008, "属性不能关联标识本身"),
    OPENAPI_EQUAL_FIELD_NOT_DUPLICATION_HANDLE(60009, "同一个属性不能关联重复的标识"),
    OPENAPI_NOT_EQUAL_FIELD_NOT_DUPLICATION_HANDLE(60010, "不同属性不能关联重复的标识"),
    OPENAPI_HANDLE_NOT_NULL(60011, "关联标识不能为空"),
    OPENAPI_FIELD_NOT_EXIST(60012, "关联属性不存在"),
    OPENAPI_DATA_SERVICE_NOT_EXIST(60013, "数据服务不存在"),
    OPENAPI_DATA_SOURCE_NOT_EXIST(60014, "数据源不存在"),
    OPENAPI_HANDLE_NOT_EXIST(60015, "标识不存在"),
    OPENAPI_REFERENCE_HANDLE_NOT_EXIST(60015, "关联标识不存在"),
    OPENAPI_HANDLE_RESOLVE_FAIL(60016, "关联标识解析失败"),
    OPENAPI_HANDLE_UPDATE_INDEX_DUPLICATION(60017, "索引不能重复"),
    OPENAPI_HANDLE_UPDATE_INDEX_NOT_EXIST(60018, "索引不存在"),
    OPENAPI_HANDLE_FIELD_TYPE_PARAM(60021, "参数校验失败，请检查属性类型是否正确"),
    OPENAPI_MAINTAIN_ACK_MAINTAINING(60022, "正在维护中，不能确认"),
    OPENAPI_MAINTAIN_ACK_NOT_EXIST(60023, "该维护记录不存在"),
    OPENAPI_HANDLE_VALID_ERROR(60024, "参数校验失败，请检查标识是否正确"),
    OPENAPI_HANDLE_NAME_VALID_ERROR(60025, "参数校验失败，标识名称不能为空/标识名称长度不正确，请检查标识名称是否正确"),
    OPENAPI_ENTITY_TYPE_VALID_ERROR(60026, "参数校验失败，实体类型不能为空/实体类型值不正确，请检查实体类型是否正确"),
    OPENAPI_ITEM_VALID_ERROR(60027, "参数校验失败，属性不能为空"),
    OPENAPI_INDEX_VALID_ERROR(60028, "参数校验失败，请检查索引是否正确"),
    OPENAPI_FIELD_VALID_ERROR(60029, "参数校验失败，请检查英文名称是否正确"),
    OPENAPI_FIELD_IS_CONTAIN_CHINESE_ERROR(60029, "参数校验失败，英文名称不能包含中文字符，请检查英文名称是否正确"),
    OPENAPI_DESCRIPTION_VALID_ERROR(60030, "参数校验失败，属性描述（中文名称）不能为空/长度不正确，请检查中文名称是否正确"),
    OPENAPI_FIELD_TYPE_VALID_ERROR(60031, "参数校验失败，请检查属性类型是否正确"),
    OPENAPI_FIELD_TYPE_NULL_VALID_ERROR(60031, "参数校验失败，属性类型不能为空/属性类型不正确"),
    OPENAPI_REMARK_VALID_ERROR(60032, "参数校验失败，请检查备注是否正确"),
    OPENAPI_VALUE_VALID_ERROR(60033, "参数校验失败，属性值不能为空/属性值长度不正确，请检查属性值是否正确"),
    OPENAPI_SERVICE_VALID_ERROR(60034, "参数校验失败，请检查数据服务id是否正确"),
    OPENAPI_SOURCE_VALID_ERROR(60035, "参数校验失败，请检查数据源id是否正确"),
    OPENAPI_REFERENCES_VALID_ERROR(60036, "参数校验失败，属性关联信息不能为空"),
    OPENAPI_REFERENCE_HANDLE_VALID_ERROR(60037, "参数校验失败，关联标识不能为空/关联标识长度不正确，请检查关联标识是否正确"),
    OPENAPI_REFERENCE_HANDLE_VALID_NOT(60052, "参数校验失败，关联标识重复"),
    OPENAPI_REFERENCE_HANDLE_VALID_NOT_OCCUPY(60052, "参数校验失败，关联标识已被占用，请检查关联标识是否正确"),
    OPENAPI_REFERENCE_QUERY_INDEX_VALID_ERROR(60038, "参数校验失败，请检查查询属性索引是否正确"),
    OPENAPI_REFERENCE_PARAM_VALID_ERROR(60039, "参数校验失败，属性名称（英文名称）不能为空/英文名称长度不正确，请检查英文名称是否正确"),
    OPENAPI_REFERENCE_PARAM_NULL_ERROR(60039, "参数校验失败，当前标识属性不能为空"),
    OPENAPI_REFERENCE_PARAM_NOTNULL_ERROR(60039, "参数校验失败，关联标识全为固定值，则参数属性和查询属性为空"),
    OPENAPI_REFERENCE_HANDLE_ITEM_VALID_ERROR(60040, "参数校验失败，关联标识无标识解析数据源属性，并且属性不全是固定值，不能建立关联关系"),
    OPENAPI_REFERENCE_HANDLE_PROP_INDEX_VALID_ERROR(60041, "参数校验失败，请检查关联标识属性索引是否正确"),
    OPENAPI_REFERENCE_HANDLE_PROP_INDEX_VALID_NOT_ERROR(60055, "参数校验失败，关联标识属性不存在"),
    OPENAPI_REFERENCE_QUERY_VALID_FAIL(60042, "参数校验失败，查询属性的属性类型不为标识解析数据源"),
    OPENAPI_REFERENCE_PROP_VALID_FAIL(60043, "参数校验失败，参数属性的属性类型不为标识解析数据源或固定值"),
    OPENAPI_PARAM_PROP_IS_EXIST_FAIL(60043, "参数校验失败，当前标识属性不存在，请检查当前标识属性是否正确"),
    OPENAPI_REFERENCE_HANDLE_PROP_VALID_FAIL(60044, "参数校验失败，关联标识属性的属性类型不为标识解析数据源或固定值"),
    OPENAPI_HANDLE_HANDLE_RETURN_SEND_ERROR(60045, "扩展属性回传发送失败，请再次重试"),
    OPENAPI_DATA_SERVICE_NAME_NO_REPEAT(60046, "数据服务名称不能重复"),
    OPENAPI_DATA_SERVICE_ADDRESS_NO_REPEAT(60047, "数据服务地址不能重复"),
    OPENAPI_DATA_SERVICE_IN_USE_UPDATE(60048, "标识属性已使用，不可修改"),
    OPENAPI_DATA_SERVICE_IN_USE_DELETE(60049, "标识属性已使用，不可删除"),
    OPENAPI_DATA_SERVICE_CONNECT_ERROR(60050, "openapi.data.service.connect.error"),
    OPENAPI_DATA_CHANNEL_TEST_CONNECT_ERROR(60050, "openapi.data.channel.test.connect.error"),
    FILE_ADDRESS_NOT_NULL(60051, "文件地址为空"),
    INDEX_FORMAT_ERROR(60052, "index格式错误"),
    OPENAPI_PARAM_FIELD_VALID_ERROR(60053, "参数校验失败，英文名称不能重复"),
    OPENAPI_PARAM_DESCRIPTION_VALID_ERROR(60054, "参数校验失败，中文名称不能重复"),
    OPENAPI_HANDLE_TYPE_DESCRIPTION_DUPLICATION(60055, "参数校验失败，基础属性-中文名称不能重复"),
    OPENAPI_HANDLE_PROP_ERROR(60055, "参数校验失败，当前标识属性不能与当前行的英文名称重复，请检查当前标识属性是否正确"),
    HANDLE_LENGTH_MAX_LIMIT(60056, "标识数据长度过长"),
    HANDLE_ITEM_LENGTH_MAX_LIMIT(60057, "标识属性值长度过长"),
    HANDLE_NAME_MAX_LIMIT(60058, "标识名称长度最长为255"),
    HANDLE_ITEM_DESCRIPTION_IS_NULL(60058, "标识属性中文名称不能为空"),
    HANDLE_ITEM_FIELD_PREFIX_IS_NUMBER(60058, "标识属性英文名称不能以数字开头"),
    HANDLE_ITEM_DESCRIPTION_PREFIX_IS_NUMBER(60058, "标识属性中文名称不能以数字开头"),
    HANDLE_HANDLE_NAME_IS_NUMBER(60058, "标识名称不能以数字开头"),
    HANDLE_IS_DATA_NULL(60058, "属性类型为标识解析数据源，标识在数据通道中不存在，请检查数据是否正确"),
    HANDLE_ITEM_FIELD_VALUE_IS_NULL_ERROR(60058, "标识属性类型不为固定值，属性值应当为空"),

    HANDLE_ITEM_FIELD_LENGTH_ERROR(60058, "参数校验失败，field长度不能超过255"),

    HANDLE_ITEM_DESCRIPTION_LENGTH_ERROR(60058, "参数校验失败，description长度不能超过255"),

    HANDLE_FIELD_TABLE_COLUMN_REPETITION(60058, "handle.field.table.column.repetition"),

    HANDLE_DESCRIPTION_VALID_ERROR(60054, "handle.description.valid.error"),

    HANDLE_FIELD_VALID_ERROR(60053, "handle.field.valid.error"),

    HANDLE_OPEN_API_HANDLE_CREATE_FIELD_IS_NULL(60062, "标识注册对外API--扩展属性英文名称不能为空"),

    HANDLE_OPEN_API_HANDLE_CREATE_DESCRIPTION_IS_NULL(60062, "标识注册对外API--扩展属性中文名称不能为空"),

    HANDLE_OPEN_API_HANDLE_CREATE_REFERENCEHANDLE_IS_NULL(60062, "标识注册对外API--扩展属性关联标识类型的关联标识字段不能为空"),

    HANDLE_OPEN_API_HANDLE_CREATE_QUERY_PROP_IS_NULL(60062, "标识注册对外API--扩展属性关联标识类型的queryProp字段不能为空"),

    HANDLE_OPEN_API_HANDLE_MAINTAIN_FIELD_IS_EXIST(60062, "标识维护对外API--扩展属性英文名称已存在，请检查属性英文名称是否正确"),
    HANDLE_OPEN_API_HANDLE_MAINTAIN_DESCRIPTION_IS_EXIST(60062, "标识维护对外API--扩展属性中文名称已存在，请检查属性中文名称是否正确"),

    HANDLE_OPEN_API_HANDLE_UPDATE_REFERENCEHANDLE_ERROR(60062, "标识修改对外API--关联标识不能关联当前标识"),

    HANDLE_OPEN_API_HANDLE_CREATE_REFERENCEHANDLE_ERROR(60062, "标识创建对外API--关联标识不能关联当前标识"),

    HANDLE_OPEN_API_HANDLE_UPDATE_DEL_FIELD_NOT_EXIST(60062, "标识修改对外API--del-当前删除的field不存在"),
    NOT_ALLOW_INSTANCE_HANDLE(60059, "解析标识不可以为实例标识"),
    FIELD_TYPE_IS_NOT_REFERENCE(60060, "标识类型不合法"),
    FIELD_NOT_EXISTS(60061, "标识属性不存在"),

    ITEM_AUTH_DELETE_ERROR(50053, "不可删除创建标识所属企业或应用身份标识"),

    FIELD_EXISTS(60062, "field属性重名"),

    NO_AUTH_UPDATE_KEY_PAIR(60063, "无权限更新密钥对"),

    NO_AUTH_DOWNLOAD_PRIVATE_KEY(60064, "无权限下载私钥"),

    NO_AUTH_UPLOAD_PRIVATE_KEY(60065, "无权限上传私钥"),

    UPLOAD_PRIVATE_KEY_MISMATCHING(60066, "私钥不匹配"),

    ENT_PREFIX_NOT_EXISTS(60067, "前缀不存在"),

    HANDLE_USER_PARAMETER_TRANSFER_FORMAT_ERROR(60068, "标识用户传参格式错误"),

    DATABASE_IS_RENAMED(60069, "数据库已存在，请检查后重新输入"),

    METADATA_NOT_EXIST(60070, "元数据不存在"),

    METADATA_ALREADY_EXISTS(60071, "元数据已存在,请重新导入"),

    DATABASE_NOT_EXIST(60072, "数据库不存在"),

    METADATA_IS_USED(60073, "元数据被使用，禁止删除"),

    SOCIAL_LOGIN_ERROR(70001, "省级中台登录接口异常，请稍后重试"),

    UPLOAD_FILE_CANNOT_BE_EMPTY(60073, "上传文件不能为空"),

    AUTH_GROUP_ID_NOT_EXIST(60074, "权限组ID不能为空"),
    AUTH_GROUP_HAS_HANDLE(60075, "该权限组下存在已授权的标识或已授权身份，无法进行删除"),

    AUTH_GROUP_NAME_REPEAT(60076, "此权限组名称已存在，请重新输入"),
    NOT_CANCEL_SHARE(60077, "此数据通道已有关联对象标识，无法进行取消共享操作");


    private Integer code;

    private String messageKey;

    BusinessCodeMessage(Integer code, String messageKey) {
        this.code = code;
        this.messageKey = messageKey;
    }

    @Override
    public Integer getCode() {
        return this.code;
    }

    @Override
    public String getMessageKey() {
        return this.messageKey;
    }


    private static final Map<Integer, BusinessCodeMessage> MAPPINGS = new HashMap<>();

    static {
        for (BusinessCodeMessage businessCodeMessage : values()) {
            MAPPINGS.put(businessCodeMessage.code, businessCodeMessage);
        }
    }

    public static BusinessCodeMessage getMessage(Integer code) {
        return MAPPINGS.get(code);
    }
}

