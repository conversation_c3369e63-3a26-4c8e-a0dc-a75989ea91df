package cn.teleinfo.idycprovince.common.constant;

/**
 *
 *
 * @Author: liusp
 * @Date: 2023/10/19/16:45
 * @Description:主数据范围
 */
public enum MasterDataScopeEnum {

    HY(1,"盒烟"),
    <PERSON><PERSON>(2,"条烟"),
    <PERSON><PERSON>(3,"件烟"),
    <PERSON><PERSON>(4,"原烟"),
    <PERSON><PERSON>(5,"成品烟"),
    SS(6,"丝束"),
    LB(7,"滤棒");

    private Integer code;
    private String desc;
    MasterDataScopeEnum(int code, String desc) {
     this.code = code;
     this.desc = desc;
    }

    public Integer getCode() {
        return code;
    }

    public String getDesc() {
        return desc;
    }
}
