package cn.teleinfo.idycprovince.common.manager;

import com.amazonaws.SdkClientException;
import com.amazonaws.services.s3.AmazonS3;
import com.amazonaws.services.s3.model.*;
import lombok.RequiredArgsConstructor;
import org.springframework.util.CollectionUtils;
import org.springframework.web.multipart.MultipartFile;

import java.io.InputStream;
import java.util.*;

@RequiredArgsConstructor
public class AmazonS3FileManager implements S3FileManager {

    private final AmazonS3 amazonS3;

    private final String bucketName;

    @Override
    public String upload(InputStream inputStream, long size, String contentType, String extension) throws Exception {
        String fileName = new StringBuilder("s3_").append(UUID.randomUUID()).append(".").append(extension).toString();
        ObjectMetadata metadata = new ObjectMetadata();
        metadata.setContentType(contentType);
        metadata.setContentLength(size);
        amazonS3.putObject(bucketName, fileName, inputStream, metadata);
        return fileName;
    }

    public String upload(MultipartFile file) throws Exception {
        String fileName = file.getOriginalFilename();
        ObjectMetadata metadata = new ObjectMetadata();
        metadata.setContentType("application/octet-stream");
        metadata.setContentLength(file.getSize());
        amazonS3.putObject(bucketName, fileName, file.getInputStream(), metadata);
        return fileName;
    }

    public String upload(String fileName, MultipartFile file) throws Exception {
        ObjectMetadata metadata = new ObjectMetadata();
        metadata.setContentType("application/octet-stream");
        metadata.setContentLength(file.getSize());
        amazonS3.putObject(bucketName, fileName, file.getInputStream(), metadata);
        return fileName;
    }

    @Override
    public InputStream downloadFile(String fullPath) {
        S3Object s3Object = amazonS3.getObject(bucketName, fullPath);
        if (s3Object != null) {
            S3ObjectInputStream s3is = s3Object.getObjectContent();
            return s3is;
        }
        return null;
    }

    @Override
    public List<S3ManagerSummary> listObjects(ListObjectsRequest listObjectsRequest) throws SdkClientException {
        ObjectListing objectListing = amazonS3.listObjects(listObjectsRequest);
        if (Objects.isNull(objectListing)) {
            return Collections.emptyList();
        }
        List<S3ObjectSummary> summaries = objectListing.getObjectSummaries();
        if (CollectionUtils.isEmpty(summaries)) {
            return Collections.emptyList();
        }
        List<S3ManagerSummary> resultList = new ArrayList<>();
        summaries.forEach(index -> {
            S3ManagerSummary summary = new S3ManagerSummary();
            summary.setFileSize(index.getSize());
            String[] filePath = index.getKey().split("/");
            summary.setName(filePath[filePath.length - 1]);
            summary.setStorePath(index.getKey());
            resultList.add(summary);
        });
        return resultList;
    }
    
    @Override
    public String uploadByFileName(InputStream inputStream, long size, String contentType, String extension, String fileName)
            throws Exception {
        if (Objects.isNull(inputStream)) {
            return null;
        }
        ObjectMetadata metadata = new ObjectMetadata();
        metadata.setContentType(contentType);
        metadata.setContentLength(size);
        amazonS3.putObject(bucketName, fileName, inputStream, metadata);
        return fileName;
    }

    @Override
    public void delete(String fullPath) {
        if (Objects.isNull(fullPath)) {
            return;
        }
        amazonS3.deleteObject(bucketName, fullPath);
    }

    @Override
    public String getBucketName() {
        return bucketName;
    }

}
