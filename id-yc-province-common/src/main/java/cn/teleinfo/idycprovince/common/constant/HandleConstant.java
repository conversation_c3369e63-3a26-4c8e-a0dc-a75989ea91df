package cn.teleinfo.idycprovince.common.constant;

/**
 * <AUTHOR>
 * @version 1.0
 * @date Created in 2022/11/9
 * @description 标识常量
 */
public interface HandleConstant {
    Integer ENT_PREFIX_PUBLIC_KEY = 300;
    Integer OBJECT_HANDLE_INDEX = 1000;

    Integer OBJECT_DEFINITION_INDEX = 1100;

    Integer OBJECT_INSTANCE_INDEX = 1200;

    Integer APP_INSTANCE_INDEX = 1300;

    Integer HANDLE_USER_INDEX = 1400;




    //对象标识
    String OBJECT_HANDLE = "OBJECT_HANDLE";

    //对象标识的定义字段
    String OBJECT_DEFINITION = "OBJECT_DEFINITION";

    //实例标识
    String OBJECT_INSTANCE = "OBJECT_INSTANCE";

    // 自身实例标识
    String OBJECT_INSTANCE_SELF = "OBJECT_INSTANCE_SELF";

    String APP_INSTANCE = "APP_INSTANCE";
    /**
     * 标识身份对象List
     */
    String HANDLE_USER_TYPE = "HANDLE_USER_LIST";

    /**
     * 固定值
     */
    Integer FIELD_TYPE_BASIC = 1;

    /**
     * 标识解析数据源
     */
    Integer FIELD_TYPE_DATASOURCE = 2;

    /**
     * 关联值
     */
    Integer FIELD_TYPE_REFERENCE = 3;

    /**
     * 关联属性
     */
    Integer FIELD_TYPE_REFERENCE_FIELD = 4;

    Integer INSTANCE_HANDLE_INDEX = 2000;
    Integer OBJECT_INSTANCE_SELF_INDEX = 1500;

}
