package cn.teleinfo.idycprovince.common.constant;

import cn.hutool.core.util.ObjectUtil;
import cn.hutool.core.util.StrUtil;

/**
 * <AUTHOR>
 * @version 1.0
 * @date Created in 2022/11/11
 * @description
 */
public enum ObjectHandleItemTypeEnum {

    BASIC(1,"字符串"),
    HANDLE(2,"标识属性"),
    REFERENCE(3,"关联属性");


    private Integer typeId;

    private String typeName;

    ObjectHandleItemTypeEnum(Integer typeId, String typeName) {
        this.typeId = typeId;
        this.typeName = typeName;
    }

    public Integer getTypeId() {
        return typeId;
    }

    public String getTypeName() {
        return typeName;
    }

    public static ObjectHandleItemTypeEnum value(Integer typeId) {
        ObjectHandleItemTypeEnum[] values = values();
        for (ObjectHandleItemTypeEnum itemTypeEnum : values) {
            if (ObjectUtil.equals(itemTypeEnum.getTypeId(),typeId)) {
                return itemTypeEnum;
            }
        }
        return null;
    }
}
