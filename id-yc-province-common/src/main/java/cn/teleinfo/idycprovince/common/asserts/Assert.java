package cn.teleinfo.idycprovince.common.asserts;

import cn.teleinfo.idycprovince.common.exception.BusinessCodeMessage;
import com.abluepoint.summer.mvc.exception.BusinessRuntimeException;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.StringUtils;

import java.math.BigDecimal;
import java.util.Collection;
import java.util.List;
import java.util.Objects;
import java.util.regex.Matcher;
import java.util.regex.Pattern;

@Slf4j
public class Assert {

    private Assert() {
        //Assert
    }

    public static void checkPassword(String password) {
        String regex = "^(?![A-Z]+$)(?![a-z]+$)(?!\\d+$)\\S{6,15}$";
        if (!Pattern.matches(regex, password)) {
            throwEx(BusinessCodeMessage.PWD_FORMAT_ERROR);
        }
    }

    public static void checkMobile(String mobile) {
        String regex = "(\\+\\d+)?1[3456789]\\d{9}$";
        if (!Pattern.matches(regex, mobile)) {
            throwEx(BusinessCodeMessage.PHONE_FORMAT_ERROR);
        }
    }

    public static void checkHandle(String handle) {
        String handleRegex = "^\\d+\\:(\\d+\\.)*\\d+\\/.*$";
        Pattern handlePattern = Pattern.compile(handleRegex);
        if (handle == null) {
            throw new BusinessRuntimeException(BusinessCodeMessage.HANDLE_NOT_FOUND);
        }
        Matcher matcher = handlePattern.matcher(handle);
        boolean matches = matcher.matches();
        if (!matches) {
            throw new BusinessRuntimeException(BusinessCodeMessage.HANDLE_PATTERN_ABNORMAL);
        }
    }

    public static void checkEmail(String email) {
        String regex = "^$|^\\w+([-+.]\\w+)*@\\w+([-.]\\w+)*\\.\\w+([-.]\\w+)*$";
        if (!Pattern.matches(regex, email)) {
            throwEx(BusinessCodeMessage.EMAIL_FORMAT_ERROR);
        }
    }

    /**
     * 检验字符串是否为空，为空抛异常
     */
    public static void notNull(String str, BusinessCodeMessage BusinessCodeMessage) {
        if (StringUtils.isBlank(str)) {
            throwEx(BusinessCodeMessage);
        }
    }


    /**
     * 检验字符串是否为空且最大长度限制，为空且超过最大长度抛异常
     */
    public static void notNull(String str, int maxLength, BusinessCodeMessage BusinessCodeMessage) {
        if (StringUtils.isBlank(str) || str.length() > maxLength) {
            throwEx(BusinessCodeMessage);
        }
    }


    public static void notNull(Integer num, int maxLength, BusinessCodeMessage BusinessCodeMessage) {
        if (num == null || num.toString().length() > maxLength) {
            throwEx(BusinessCodeMessage);
        }
    }

    public static void notNull(BigDecimal num, int maxLength, BusinessCodeMessage businessCodeMessage) {
        if (num == null || num.toString().length() > maxLength) {
            throwEx(businessCodeMessage);
        }
    }

    /**
     * 检验最大长度不能超过maxLength 可以为空
     */
    public static void maxLength(Object obj, int maxLength, BusinessCodeMessage businessCodeMessage) {
        if (obj != null) {
            if (obj instanceof String && ((String) obj).length() > maxLength) {
                throwEx(businessCodeMessage);
            }

            if (obj instanceof Integer && obj.toString().length() > maxLength) {
                throwEx(businessCodeMessage);
            }
        }
    }

    /**
     * 最小长度限制 可以为空
     */
    public static void minLength(Object obj, int minLength, BusinessCodeMessage businessCodeMessage) {
        if (obj != null) {
            if (obj instanceof String && ((String) obj).length() < minLength) {
                throwEx(businessCodeMessage);
            }

            if (obj instanceof Integer && obj.toString().length() < minLength) {
                throwEx(businessCodeMessage);
            }
        }
    }


    public static void isNull(Object obj, BusinessCodeMessage code) {
        if (Objects.nonNull(obj)) {
            throwEx(code);
        }
    }


    /**
     * 检验对象是否为null，为空抛异常
     */
    public static void notNull(Object obj, BusinessCodeMessage BusinessCodeMessage) {
        if (obj == null) {
            throwEx(BusinessCodeMessage);
        }
    }

    /**
     * 检验对象都不为null，其中一个为null抛异常
     */
    public static void notAllNull(List<Object> lists, BusinessCodeMessage BusinessCodeMessage) {
        for (Object obj : lists) {
            if (Objects.isNull(obj)) {
                throwEx(BusinessCodeMessage);
                return;
            }
        }
    }

    public static void notNull(Collection collection, BusinessCodeMessage BusinessCodeMessage) {
        if (collection == null || collection.isEmpty()) {
            throwEx(BusinessCodeMessage);
        }
    }


    public static void assertTrue(boolean condition, BusinessCodeMessage BusinessCodeMessage) {
        if (!condition) {
            throwEx(BusinessCodeMessage);
        }
    }

    public static void assertFalse(boolean condition, BusinessCodeMessage BusinessCodeMessage) {
        if (condition) {
            throwEx(BusinessCodeMessage);
        }
    }

    public static void notBlankInt(Integer number, BusinessCodeMessage BusinessCodeMessage) {
        if (!(Objects.nonNull(number) && number > 0)) {
            throwEx(BusinessCodeMessage);
        }
    }

    public static void notBlankLong(Long number, BusinessCodeMessage BusinessCodeMessage) {
        if (!(Objects.nonNull(number) && number > 0)) {
            throwEx(BusinessCodeMessage);
        }
    }

    public static void notBlankStr(String str, BusinessCodeMessage BusinessCodeMessage) {
        if (StringUtils.isBlank(str)) {
            throwEx(BusinessCodeMessage);
        }
    }


    public static void throwEx(BusinessCodeMessage BusinessCodeMessage) {
        throw new BusinessRuntimeException(BusinessCodeMessage);
    }

    public static void throwEx(BusinessCodeMessage BusinessCodeMessage, Throwable e) {
        throw new BusinessRuntimeException(BusinessCodeMessage, e);
    }

    public static void bigDecimalNotEqual(BigDecimal source, BigDecimal target, BusinessCodeMessage msg) {
        if (!Objects.equals(source, target) || source.compareTo(target) != 0) {
            throw new BusinessRuntimeException(msg);
        }
    }

    public static void bigDecimalSmall(BigDecimal source, BigDecimal target, BusinessCodeMessage BusinessCodeMessage) {
        if (source.compareTo(target) < 0) {
            throw new BusinessRuntimeException(BusinessCodeMessage);
        }
    }

    public static void bigDecimalNotGreater(BigDecimal source, BigDecimal target, BusinessCodeMessage BusinessCodeMessage) {
        if (Objects.equals(source, target) || source.compareTo(target) < 0) {
            throw new BusinessRuntimeException(BusinessCodeMessage);
        }
    }

    public static void bigDecimalNotGreaterZero(BigDecimal source, BusinessCodeMessage BusinessCodeMessage) {
        bigDecimalNotGreater(source, BigDecimal.ZERO, BusinessCodeMessage);
    }


    public static void notEquals(Object source, Object target, BusinessCodeMessage BusinessCodeMessage) {
        if (Objects.equals(source, target)) {
            throw new BusinessRuntimeException(BusinessCodeMessage);
        }
    }

    public static void equals(Object source, Object target, BusinessCodeMessage BusinessCodeMessage) {
        if (!Objects.equals(source, target)) {
            throw new BusinessRuntimeException(BusinessCodeMessage);
        }
    }

    public static void notZero(Integer source, BusinessCodeMessage businessCodeMessage) {
        if (source.intValue() == 0) {
            throw new BusinessRuntimeException(businessCodeMessage);
        }
    }

}
