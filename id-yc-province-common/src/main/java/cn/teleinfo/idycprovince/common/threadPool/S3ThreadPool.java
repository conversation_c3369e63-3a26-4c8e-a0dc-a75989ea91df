package cn.teleinfo.idycprovince.common.threadPool;

import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.stereotype.Component;

import java.util.concurrent.*;
import java.util.concurrent.atomic.AtomicInteger;

/**
 * @description: 线程池
 * @author: liuyan
 * @create: 2022−06-20 3:03 PM
 */
@Slf4j
@Component
public class S3ThreadPool {
    /**
     * 线程池核心线程数
     */
    @Value("${app.threadpool.s3threadpool}")
    private int corepoolSize;
    /**
     * 线程池中最大线程数
     */
    @Value("${app.threadpool.maxpoolsize}")
    private int maxpoolSize;
    /**
     * 线程空闲时间
     */
    private static long KEEPALIVETIME = 60;


    /**
     * 线程空闲时间单位
     */
    private static final TimeUnit UNIT = TimeUnit.SECONDS;
    /**
     * 线程池阻塞队列
     */
    private static final LinkedBlockingQueue<Runnable> WORK_QUEUE = new LinkedBlockingQueue<>();
    /**
     * 线程池线程工厂对象
     */
    private static final ThreadFactory THREAD_FACTORY = new ExecutorThreadFactory();
    /**
     * 线程池对象
     */
    private static ExecutorService EXEC;

    private ExecutorService getExecutor() {
        if (EXEC == null) {
            EXEC = new ThreadPoolExecutor(corepoolSize, maxpoolSize, KEEPALIVETIME, UNIT, WORK_QUEUE, THREAD_FACTORY);
        }
        return EXEC;
    }

    /**
     * 线程池，创建线程工厂
     */
    private static class ExecutorThreadFactory implements ThreadFactory {
        private static final AtomicInteger POOL_NUMBER = new AtomicInteger(1);
        private final ThreadGroup group;
        private final AtomicInteger threadNumber = new AtomicInteger(1);
        private final String namePrefix;

        ExecutorThreadFactory() {
            SecurityManager s = System.getSecurityManager();
            group = (s != null) ? s.getThreadGroup() : Thread.currentThread().getThreadGroup();
            namePrefix = "executor-pool-" + POOL_NUMBER.getAndIncrement() + "-thread-";
        }

        @Override
        public Thread newThread(Runnable r) {
            Thread t = new Thread(group, r, namePrefix + threadNumber.getAndIncrement(), 0);
            if (t.isDaemon()) {
                t.setDaemon(false);
            }
            if (t.getPriority() != Thread.NORM_PRIORITY) {
                t.setPriority(Thread.NORM_PRIORITY);
            }
            return t;
        }
    }

    /**
     * 执行任务方法
     */
    public void execute(Runnable runnable) {
        getExecutor().execute(runnable);
    }

    /**
     * 执行任务方法
     *
     * @param callable callable任务对象
     * @param <T>      返回结果泛型
     * @return 任务
     */
    public <T> Future<T> submit(Callable<T> callable) {
        return getExecutor().submit(callable);
    }


}
