package cn.teleinfo.idycprovince.common.file;

import cn.hutool.core.util.ZipUtil;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.compress.archivers.tar.TarArchiveEntry;
import org.apache.commons.compress.archivers.tar.TarArchiveInputStream;
import org.apache.commons.compress.compressors.gzip.GzipCompressorInputStream;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;

import java.io.*;
import java.nio.file.Files;
import java.util.zip.GZIPInputStream;

/**
 * @description: 文件处理
 * @author: liuyan
 * @create: 2022−11-07 10:57 AM
 */
@Slf4j
public class FileUtil {

    private static final Logger logger = LoggerFactory.getLogger(FileUtil.class);
    private static final int BUFFER_SIZE = 1024;
    public static final String EXT = ".gz";

    /**
     * 压缩为gzip文件
     * @param file  文件
     * @param filePath 压缩后的路径
     * @return
     */
    public static File compressGzip(File file,String filePath){
        byte[] gzip = ZipUtil.gzip(file);
        BufferedOutputStream bos = null;
        FileOutputStream fos = null;
        File returnFile = null;
        try{
            File dir = new File(filePath);
            if (!dir.exists() && dir.isDirectory()){
                dir.mkdirs();
            }
            returnFile = new File(filePath + File.separator + file.getName()+".gz");
            fos = new FileOutputStream(returnFile);
            bos = new BufferedOutputStream(fos);
            bos.write(gzip);
        }catch (Exception e){
            log.error(e.getMessage(), e);
        }
        finally{
            if (bos != null){
                try{
                    bos.close();
                }catch (IOException e){
                    log.error(e.getMessage(), e);
                }
            }
            if (fos != null){
                try{
                    fos.close();
                }catch (IOException e){
                    log.error(e.getMessage(), e);
                }
            }
        }
        return returnFile;
    }

    /**
     * byte 转file
     */
    public static File byte2File(byte[] buf, String filePath, String fileName){
        BufferedOutputStream bos = null;
        FileOutputStream fos = null;
        File file = null;
        try{
            File dir = new File(filePath);
            if (!dir.exists() && dir.isDirectory()){
                dir.mkdirs();
            }
            file = new File(filePath + File.separator + fileName);
            fos = new FileOutputStream(file);
            bos = new BufferedOutputStream(fos);
            bos.write(buf);
        }catch (Exception e){
            log.error(e.getMessage(), e);
        }
        finally{
            if (bos != null){
                try{
                    bos.close();
                }catch (IOException e){
                    log.error(e.getMessage(), e);
                }
            }
            if (fos != null){
                try{
                    fos.close();
                }catch (IOException e){
                    log.error(e.getMessage(), e);
                }
            }
        }
        return file;
    }


    /**
     * 解压 gzip 文件
     *
     * @param input
     * @param output
     */
    public static void decompressGzip(File input, File output) throws IOException {
        try (GZIPInputStream in = new GZIPInputStream(Files.newInputStream(input.toPath()))) {
            try (FileOutputStream out = new FileOutputStream(output)) {
                byte[] buffer = new byte[BUFFER_SIZE];
                int len;
                while ((len = in.read(buffer)) != -1) {
                    out.write(buffer, 0, len);
                }
            }
        }
    }

    /**
     * 文件解压缩
     *
     * @param file
     * @throws Exception
     */
    public static void deCompress(File file) throws Exception {
        deCompress(file, true);
    }

    /**
     * 文件解压缩
     *
     * @param file
     * @param delete 是否删除原始文件
     * @throws Exception
     */
    public static void deCompress(File file, boolean delete) throws Exception {
        FileInputStream fis = new FileInputStream(file);
        FileOutputStream fos = new FileOutputStream(file.getPath().replace(EXT,
                ""));
        decompress(fis, fos);
        fis.close();
        fos.flush();
        fos.close();

        if (delete) {
            file.delete();
        }
    }

    /**
     * 文件解压缩
     *
     * @param file
     * @throws Exception
//     */
//    public static FileOutputStream deCompressFileOutputStream(File file) throws Exception {
//        FileInputStream fis = new FileInputStream(file);
//        FileOutputStream fos = new FileOutputStream(file.getPath().replace(EXT,
//                ""));
//        decompress(fis, fos);
//        fis.close();
//        fos.flush();
//        fos.close();
//        return fos;
//    }



    /**
     * 数据解压缩
     *
     * @param is
     * @param os
     * @throws Exception
     */
    public static void decompress(InputStream is, OutputStream os)
            throws Exception {

        GZIPInputStream gis = new GZIPInputStream(is);

        int count;
        byte data[] = new byte[BUFFER_SIZE];
        while ((count = gis.read(data, 0, BUFFER_SIZE)) != -1) {
            os.write(data, 0, count);
        }

        gis.close();
    }

    /**
     * 解压 tar.gz 文件到指定目录
     *
     * @param tarGzFile tar.gz 文件路径
     * @param destDir   解压到 destDir 目录，如果没有则自动创建
     */
    public static void extractTarGZ(File tarGzFile, String destDir) throws IOException {

        GzipCompressorInputStream gzipIn = new GzipCompressorInputStream(Files.newInputStream(tarGzFile.toPath()));
        try (TarArchiveInputStream tarIn = new TarArchiveInputStream(gzipIn)) {
            TarArchiveEntry entry;

            while ((entry = (TarArchiveEntry) tarIn.getNextEntry()) != null) {
                if (entry.isDirectory()) {
                    File f = new File(destDir + "/" + entry.getName());
                    boolean created = f.mkdirs();
                    if (!created) {
                        System.out.printf("Unable to create directory '%s', during extraction of archive contents.\n",
                                f.getAbsolutePath());
                    }
                } else {
                    int count;
                    byte[] data = new byte[BUFFER_SIZE];
                    FileOutputStream fos = new FileOutputStream(destDir + "/" + entry.getName(), false);
                    try (BufferedOutputStream dest = new BufferedOutputStream(fos, BUFFER_SIZE)) {
                        while ((count = tarIn.read(data, 0, BUFFER_SIZE)) != -1) {
                            dest.write(data, 0, count);
                        }
                    }
                }
            }
        }
    }
}
