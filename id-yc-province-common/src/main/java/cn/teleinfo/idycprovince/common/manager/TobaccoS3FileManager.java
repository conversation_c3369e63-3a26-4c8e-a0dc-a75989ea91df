package cn.teleinfo.idycprovince.common.manager;

import cn.teleinfo.idycprovince.common.asserts.Assert;
import cn.teleinfo.idycprovince.common.exception.BusinessCodeMessage;
import cn.tobacco.tcaf.api.file.AFileService;
import cn.tobacco.tcaf.util.FileInfo;
import com.amazonaws.SdkClientException;
import com.amazonaws.services.s3.model.ListObjectsRequest;
import lombok.AllArgsConstructor;
import org.springframework.boot.autoconfigure.condition.ConditionalOnProperty;

import java.io.ByteArrayInputStream;
import java.io.ByteArrayOutputStream;
import java.io.InputStream;
import java.util.*;

@ConditionalOnProperty(prefix = "app", name = "file-manager", havingValue = "tbc")
@AllArgsConstructor
public class TobaccoS3FileManager implements S3FileManager {

    private final AFileService aFileService;

    private final String bucketName;

    @Override
    public String upload(InputStream in, long size, String contentType, String extension) throws Exception {
        String fileName = new StringBuilder("s3_").append(UUID.randomUUID()).append(".").append(extension).toString();
        if (Objects.isNull(in)) {
            return null;
        }

        FileInfo fileInfo = aFileService.writeFile(fileName, in);
        Assert.notNull(fileInfo, BusinessCodeMessage.FILE_STORE_FAIL);
        return fileInfo.getStorePath();
    }

    @Override
    public InputStream downloadFile(String fullPath) throws Exception {
        if (Objects.isNull(fullPath)) {
            return null;
        }
        ByteArrayInputStream inputStream;
        try (ByteArrayOutputStream baos = new ByteArrayOutputStream();) {
            aFileService.readFile(fullPath, baos);
            inputStream = new ByteArrayInputStream(baos.toByteArray());
        }
        return inputStream;

    }

    @Override
    public void delete(String fullPath) {
        if (Objects.isNull(fullPath)) {
            return;
        }
        aFileService.deleteFile(Collections.singletonList(fullPath));
    }

    @Override
    public String getBucketName() {
        return bucketName;
    }

    @Override
    public List<S3ManagerSummary> listObjects(ListObjectsRequest listObjectsRequest) throws SdkClientException {
        List<FileInfo> fileInfos = aFileService.listFile(listObjectsRequest.getBucketName(), listObjectsRequest.getPrefix(), null, 100);
        List<S3ManagerSummary> resultList = new ArrayList<>();
        fileInfos.forEach(index -> {
            S3ManagerSummary summary = new S3ManagerSummary();
            summary.setFileSize(index.getFileSize());
            summary.setName(index.getName());
            summary.setStorePath(index.getStorePath());
            resultList.add(summary);
        });
        return resultList;
    }
    
    @Override
    public String uploadByFileName(InputStream inputStream, long size, String contentType, String extension, String fileName)
            throws Exception {
        if (Objects.isNull(inputStream)) {
            return null;
        }
        FileInfo fileInfo = aFileService.writeFile(fileName, inputStream);
        Assert.notNull(fileInfo, BusinessCodeMessage.FILE_STORE_FAIL);
        return fileInfo.getStorePath();
    }
    
}
