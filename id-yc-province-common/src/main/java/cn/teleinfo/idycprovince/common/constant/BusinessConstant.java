package cn.teleinfo.idycprovince.common.constant;

/**
 * 业务常量
 *
 * <AUTHOR>
 */
public interface BusinessConstant {

    /**
     * 已删除
     */
    Integer DELETED = 0;

    /**
     * 未删除
     */
    Byte NO_DELETED = null;

    /**
     * 禁用
     */
    Integer STATE_DISABLE = 0;

    /**
     * 启用
     */
    Integer STATE_ENABLE = 1;

    /**
     * 待审核
     */
    Integer AUDIT_STATE_WAITING = 1;

    /**
     * 审核通过
     */
    Integer AUDIT_STATE_PASS = 2;

    /**
     * 已驳回
     */
    Integer AUDIT_STATE_REJECT = 3;

    /**
     * IPV4
     */
    Integer IP_V4 = 4;

    /**
     * IPV6
     */
    Integer IP_V6 = 6;

    /**
     * 已上报
     */
    Integer UPLOADED = 1;

    /**
     * 未上报
     */
    Integer NO_UPLOAD = 0;
    
    /**
     * 上报行业：正常
     */
    String REPORT_STATE_ENABLE = "1";
    
    /**
     * 上报行业：停用
     */
    String REPORT_STATE_DISABLE = "2";
    
    /**
     * 系统类型 1省级节点 2企业节点
     */
    Integer ENT = 2;
    Integer PROVINCE = 1;

    /**
     * 托管状态 0未托管 1已托管
     */
    Integer NO_HOSTING = 0;
    Integer HOSTING = 1;
    
    /**
     * 账号角色 0超级管理员 1 省级管理员 3企业应用角色 2企业
     */
    Integer SUPER_ADMIN_ROLE = 0;
    Integer PROVINCE_ROLE = 1;
    Integer ENT_ROLE = 2;
    Integer APP_ROLE = 3;
    
    /**
     * 属性类型 1固定值 2标识解析数据源 3关联标识 4标识-属性
     */
    Integer FIELD_FIXED_VALUE = 1;
    Integer FIELD_DATA_SERVICE = 2;
    Integer FIELD_HANDLE_VALUE= 3;
    Integer FIELD_HANDLE_PROP = 4;
    
    /**
     * 属性来源类型 0基础属性 1扩展属性
     */
    Integer FIELD_SOURCE_BASE = 0;
    Integer FIELD_SOURCE_EXTEND = 1;

    /**
     * 属性权限 0非公开  1公开*
     */
    Integer FIELD_PUBLIC_READ_NO = 0;
    Integer FIELD_PUBLIC_READ_YES = 1;

    /**
     * 权限申请状态：0待审核，1审核通过，2驳回*
     */
    Integer APPLY_STATE_PEND = 0;
    Integer APPLY_STATE_AUDIT = 1;
    Integer APPLY_STATE_REJECT = 2;

    /**
     * 用户中台接口返回码
     */
    String USER_CENTER_RESPONSE_CODE_SUCCESS = "200";
    String USER_CENTER_RESPONSE_CODE_REPEAT_BINGING = "BWE_00_00_0403";
    
    /**
     * 第三方用户来源  1阿里 2华为
     */
    Integer THIRD_SOURCE_ALI = 1;
    
    /**
     * 用户中台 认证方式(密码:password;其他方式暂时不支持)
     */
    String USER_CENTER_AUTH_PASSWORD = "password";
    
    /**
     * 登录失败
     */
    String USER_LOGIN_ERROR = "login_error";

    String FIELD_PREFIX_IS_NUMBER = "[0-9].*";

    /**
     * 数据类型-1中台标识，2非中台标识*
     */
    Integer ZT_HANDLE = 1;
    Integer NON_ZT_HANDLE = 2;

    /**
     * 标识状态-1草稿，2发布*
     */
    Integer HANDLE_STATUS_DRAFT = 1;
    Integer HANDLE_STATUS_RELEASE = 2;

    /**
     * 注册状态-0注册中，1注册成功，2注册失败*
     */
    Integer REGISTER_STATUS_ING = 0;
    Integer REGISTER_STATUS_SUCCEED = 1;
    Integer REGISTER_STATUS_FAIL = 1;

    Integer BUILT_IN_AUTH_GROUP = 1;
    Integer ADD_AUTH_GROUP = 0;


    Integer IS_SHARE_YES = 1;
    Integer IS_SHARE_NO = 2;


}
