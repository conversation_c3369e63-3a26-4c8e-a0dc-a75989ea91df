package cn.teleinfo.idycprovince.common.constant;

import java.util.Arrays;

public enum LevelTypeEnum {

    PROVINCE(1, "省级"),
    ENT(2, "企业级"),
    ;

    private Integer code;
    private String desc;

    LevelTypeEnum(Integer code, String desc) {
        this.code = code;
        this.desc = desc;
    }

    public int getCode() {
        return this.code;
    }

    public String getDesc() {
        return this.desc;
    }

    public static String getDescByCode(Integer code) {
        LevelTypeEnum[] opportunityStatuses = LevelTypeEnum.values();
        for (LevelTypeEnum status : opportunityStatuses) {
            if (code.equals(status.code)) {
                return status.getDesc();
            }
        }
        return "默认空公司";
    }

    public static boolean exist(Integer code) {
        return Arrays.stream(LevelTypeEnum.values())
                .anyMatch((e) -> e.getCode() == code);
    }
}
