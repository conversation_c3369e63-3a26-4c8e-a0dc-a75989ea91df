package cn.teleinfo.idycprovince.common.constant;

import lombok.Getter;

// 自动维护状态，0：未维护，1：维护中，2：已维护
@Getter
public enum HandleAutoMaintainStateEnum {

    /**
     * 未维护
     */
    NOT_MAINTAINED(0, "未维护"),

    /**
     * 维护中
     */
    MAINTAINING(1, "维护中"),

    /**
     * 已维护
     */
    MAINTAINED(2, "已维护");


    private final int code;
    private final String desc;

    HandleAutoMaintainStateEnum(int code, String desc) {
        this.code = code;
        this.desc = desc;
    }


}
