system.message.ok=\u6210\u529F
system.undefined.error=\u7CFB\u7EDF\u5185\u90E8\u9519\u8BEF
user.param.verify.fail=\u53C2\u6570\u6821\u9A8C\u5931\u8D25
user.no.permission=\u6CA1\u6709\u6743\u9650
user.data.not.exist=\u6570\u636E\u4E0D\u5B58\u5728
user.app.not.exist=\u5E94\u7528\u6570\u636E\u4E0D\u5B58\u5728
user.app.is.not.bound.hdl.user=\u5E94\u7528\u5C1A\u672A\u7ED1\u5B9A\u6807\u8BC6\u7528\u6237
user.app.cannot.be.bound.repeatedly=\u5E94\u7528\u4E0D\u80FD\u88AB\u91CD\u590D\u7ED1\u5B9A
user.prefix.not.exist=\u524D\u7F00\u4E0D\u5B58\u5728
user.prefix.has.reached.upper.limit=\u524D\u7F00\u5DF2\u8FBE\u4E0A\u9650
user.prefix.of.binding.app.cannot.be.deleted=\u5DF2\u7ED1\u5B9A\u5E94\u7528\u7684\u524D\u7F00\u4E0D\u80FD\u88AB\u5220\u9664
user.hdl.user.not.exist=\u6807\u8BC6\u7528\u6237\u4E0D\u5B58\u5728
user.hdl.user.cannot.be.bound.repeatedly=\u6807\u8BC6\u7528\u6237\u4E0D\u80FD\u88AB\u91CD\u590D\u7ED1\u5B9A
user.hdl.user.is.not.bound.app=\u6807\u8BC6\u7528\u6237\u672A\u7ED1\u5B9A\u5E94\u7528
appDTO.appName.not.empty=\u5E94\u7528\u540D\u79F0\u4E0D\u80FD\u4E3A\u7A7A
appDTO.appName.length=\u5E94\u7528\u540D\u79F0\u4E0D\u80FD\u8D85\u8FC750\u4E2A\u5B57\u7B26
appDTO.description.not.empty=\u5E94\u7528\u63CF\u8FF0\u4E0D\u80FD\u4E3A\u7A7A
appDTO.description.length=\u5E94\u7528\u63CF\u8FF0\u4E0D\u80FD\u8D85\u8FC7200\u4E2A\u5B57\u7B26
appBindDTO.hdlUserId.not.null=\u6807\u8BC6\u7528\u6237ID\u4E0D\u80FD\u4E3A\u7A7A
hdlUserDTO.entPrefixId.not.null=\u524D\u7F00ID\u4E0D\u80FD\u4E3A\u7A7A
hdlUserBindDTO.appId.not.null=\u5E94\u7528ID\u4E0D\u80FD\u4E3A\u7A7A
system.message.login_failed=\u7528\u6237\u540D\u6216\u5BC6\u7801\u9519\u8BEF\uFF0C\u8BF7\u91CD\u65B0\u8F93\u5165
system.message.unauthorized=\u767B\u5F55\u5931\u6548\uFF0C\u8BF7\u91CD\u65B0\u767B\u5F55
system.message.forbidden=\u7528\u6237\u6CA1\u6709\u6743\u9650
system.config.error=\u7CFB\u7EDF\u914D\u7F6E\u9519\u8BEF
validate.error=\u53C2\u6570\u6821\u9A8C\u9519\u8BEF
validate.errors=\u53C2\u6570\u6821\u9A8C\u9519\u8BEF
validate.illegal_json_http_method=\u8BF7\u6C42\u65B9\u6CD5\u975E\u6CD5
validate.field.required=\u53C2\u6570\u5FC5\u987B
validate.field.error=\u53C2\u6570\u6821\u9A8C\u5931\u8D25
validate.field.list_illegal_count=\u5217\u8868\u6570\u91CF\u53C2\u6570\u9519\u8BEF
system.request_not_cached=\u8BF7\u6C42\u65E0\u6CD5\u7F13\u5B58
system.data.multi_column=\u6570\u636E\u91CD\u590D\u5217
system.data.logic_delete_not_support=\u4E0D\u652F\u6301\u903B\u8F91\u5220\u9664
system.idhub.error=idhub\u670D\u52A1\u5F02\u5E38, \u539F\u56E0:{0}
system.entPrefix.notbelong.secondaryPrefix.error=\u4F01\u4E1A\u6807\u8BC6{0}\u4E0D\u5C5E\u4E8E\u672C\u7CFB\u7EDF\u4E0B\u7684\u4E8C\u7EA7\u524D\u7F00{1}
openapi.data.service.connect.error=\u6570\u636E\u670D\u52A1\u8FDE\u63A5\u9519\u8BEF
openapi.data.channel.test.connect.error={0}\u6570\u636E\u901A\u9053\u6D4B\u8BD5\u8FDE\u63A5\u4E0D\u901A
gateway.proxy_channel_error=\u8DEF\u7531\u670D\u52A1\u5931\u8D25
gateway.proxy_time_out=\u8DEF\u7531\u670D\u52A1\u8D85\u65F6
gateway.request_limit_daily=\u6682\u65F6\u65E0\u6CD5\u8BBF\u95EE\u670D\u52A1
gateway.total_create_count_limit=\u6B64\u524D\u7F00\u6CE8\u518C\u6807\u8BC6\u6570\u91CF\u8D85\u51FA\u670D\u52A1\u4E0A\u9650

validate.vcode.error_or_timeout=\u77ED\u4FE1\u9A8C\u8BC1\u7801\u9519\u8BEF\u6216\u5DF2\u8FC7\u671F

user.locked=\u60A8\u7684\u7528\u6237\u5DF2\u7ECF\u88AB\u9501\u5B9A\uFF0C\u9501\u5B9A\u65F6\u95F4\u4E3A{0}\u5206\u949F,\u8BF7\u7A0D\u540E\u518D\u8BD5\u3002
email.locked=\u60A8\u7684\u90AE\u7BB1\u5DF2\u7ECF\u88AB\u9501\u5B9A\uFF0C\u9501\u5B9A\u65F6\u95F4\u4E3A{0}\u5206\u949F\uFF0C\u8BF7\u7A0D\u540E\u518D\u8BD5\u3002
handle.field.table.column.repetition=\u57FA\u7840\u5C5E\u6027\u6240\u5C5E\u8868{0}\u3001\u6240\u5C5E\u5B57\u6BB5{1}\u91CD\u590D\uFF0C\u8BF7\u91CD\u65B0\u9009\u62E9\u3002
handle.description.valid.error=\u5C5E\u6027\u7684\u4E2D\u6587\u540D\u79F0-{0}-\u4E0D\u80FD\u91CD\u590D\uFF0C\u8BF7\u91CD\u65B0\u9009\u62E9\u3002
handle.field.valid.error=\u5C5E\u6027\u7684\u82F1\u6587\u540D\u79F0-{0}-\u4E0D\u80FD\u91CD\u590D\uFF0C\u8BF7\u91CD\u65B0\u9009\u62E9\u3002