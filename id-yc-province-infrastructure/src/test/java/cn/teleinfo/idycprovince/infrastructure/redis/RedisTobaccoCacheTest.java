package cn.teleinfo.idycprovince.infrastructure.redis;

import org.junit.jupiter.api.Test;
import org.springframework.beans.factory.annotation.Autowired;

import java.util.HashMap;
import java.util.Map;

import static org.junit.jupiter.api.Assertions.*;

class RedisTobaccoCacheTest {
    @Autowired
    private RedisCache redisCache;

    @Test
    void renameCachingTest() {
        Map<String,Object> data = new HashMap<>();
        data.put("a","a");
        data.put("b","b");

        redisCache.hPutAll("test", new HashMap<>(data));

        Map<String, Object> test = redisCache.hEntries("test");
        test.forEach((key,value) ->{
            System.out.println(key);
            System.out.println(value);
        });

        redisCache.rename("test","test1");

        Map<String, Object> testMap = redisCache.hEntries("test");
        System.out.println(testMap.size());
        testMap.forEach((key,value) ->{
            System.out.println(key);
            System.out.println(value);
        });

        Map<String, Object> test1Map = redisCache.hEntries("test1");
        test1Map.forEach((key,value) ->{
            System.out.println(key);
            System.out.println(value);
        });
    }
}