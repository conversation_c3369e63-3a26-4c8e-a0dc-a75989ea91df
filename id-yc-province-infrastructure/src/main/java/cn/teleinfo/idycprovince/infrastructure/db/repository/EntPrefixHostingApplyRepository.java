package cn.teleinfo.idycprovince.infrastructure.db.repository;

import cn.teleinfo.idycprovince.infrastructure.db.entity.EntPrefixHostingApplyEntity;
import cn.teleinfo.idycprovince.infrastructure.db.to.EntProfixHostingApplyTO;
import cn.teleinfo.idycprovince.infrastructure.db.to.EntProfixHostingAuditTO;
import org.springframework.data.domain.Page;
import org.springframework.data.domain.Pageable;
import org.springframework.data.jpa.repository.Query;
import org.springframework.data.repository.query.Param;

import java.util.Date;
import java.util.List;


public interface EntPrefixHostingApplyRepository extends BaseRepository<EntPrefixHostingApplyEntity, Long> {


    @Query(nativeQuery = true, value =
            "SELECT " +
                    " a.ent_prefix as entPrefix, " +
                    " IFNULL(b.hosting_state,1) as hostingState, " +
                    " a.created_time as createdTime, " +
                    " b.updated_time as updatedTime " +
                    " FROM " +
                    " yc_ent_prefix a  left join  yc_ent_prefix_hosting_apply b ON a.id = b.ent_prefix_id " +
                    " WHERE " +
                    " a.is_deleted = 0 " +
                    " AND a.province_id = :provinceId " +
                    " AND a.ent_id = :entId " +
                    " AND if(:entPrefix != '' , a.ent_prefix like CONCAT('%',:entPrefix,'%'), 1=1 ) "+
                    " AND if(:startTime != '' ,b.updated_time >= :startTime, 1=1 ) "+
                    " AND if(:endTime != '' , b.updated_time <= :endTime, 1=1 ) ",
            countQuery =
                    " SELECT " +
                            "count(*) " +
                            " FROM " +
                            " yc_ent_prefix a  left join  yc_ent_prefix_hosting_apply b ON a.id = b.ent_prefix_id " +
                            " WHERE " +
                            " a.is_deleted = 0 " +
                            " AND a.province_id = :provinceId " +
                            " AND a.ent_id = :entId " +
                            " AND if(:entPrefix != '' , a.ent_prefix like CONCAT('%',:entPrefix,'%'), 1=1 ) "+
                            " AND if(:startTime != '' , b.updated_time  >= :startTime, 1=1 ) "+
                            " AND if(:endTime != '' and :endTime is not null, b.updated_time <= :endTime, 1=1 ) ")
    Page<EntProfixHostingApplyTO> pageEntPrefixHostingApply(@Param("entPrefix")String  entPrefix, @Param("startTime") Date startTime, @Param("endTime") Date endTime,
                                                            @Param("provinceId") Long provinceId, @Param("entId") Long entId, @Param("pageable") Pageable pageable);



    @Query(nativeQuery = true, value =
            "SELECT " +
                    " b.ent_prefix  AS entPrefix, " +
                    " c.org_name AS entName, " +
                    " a.hosting_state AS auditState, " +
                    " a.updated_time  AS updatedTime " +
                    " FROM " +
                    " yc_ent_prefix_hosting_apply a" +
                    " LEFT JOIN yc_ent_prefix b  ON a.ent_prefix_id = b.id " +
                    " LEFT JOIN yc_ent c ON  c.id = b.ent_id " +
                    " WHERE " +
                    " b.is_deleted = 0 " +
                    " AND c.is_deleted = 0 " +
                    " AND b.province_id = :provinceId " +
                    " AND if(:entPrefix != '', b.ent_prefix like CONCAT('%',:entPrefix,'%'), 1=1 ) "+
                    " AND if(:entName != '' , c.org_name like CONCAT('%',:entName,'%'), 1=1 ) "+
                    " AND if(:auditState != '' , a.hosting_state = :auditState, 1=1 ) "+
                    " AND if(:startTime != '' , a.updated_time  >= :startTime, 1=1 ) "+
                    " AND if(:endTime != '' , a.updated_time <= :endTime, 1=1 ) ",
            countQuery =
                    " SELECT " +
                            "count(*) " +
                            " FROM " +
                            " yc_ent_prefix_hosting_apply a" +
                            " LEFT JOIN yc_ent_prefix b  ON a.ent_prefix_id = b.id " +
                            " LEFT JOIN yc_ent c ON  c.id = b.ent_id " +
                            " WHERE " +
                            " b.is_deleted = 0 " +
                            " AND c.is_deleted = 0 " +
                            " AND b.province_id = :provinceId " +
                            " AND if(:entPrefix != '', b.ent_prefix like CONCAT('%',:entPrefix,'%'), 1=1 ) "+
                            " AND if(:entName != '', c.org_name like CONCAT('%',:entName,'%'), 1=1 ) "+
                            " AND if(:auditState != '', a.hosting_state = :auditState, 1=1 ) "+
                            " AND if(:startTime != '' , a.updated_time >= :startTime, 1=1 ) "+
                            " AND if(:endTime != ''  , a.updated_time <= :endTime, 1=1 ) ")
    Page<EntProfixHostingAuditTO> pageEntPrefixHostingAudit(@Param("entPrefix")String  entPrefix,@Param("entName")String  entName,
                                                            @Param("auditState")String  auditState, @Param("startTime") Date startTime,
                                                            @Param("endTime") Date endTime, @Param("provinceId") Long provinceId, @Param("pageable") Pageable pageable);


    EntPrefixHostingApplyEntity findEntPrefixHostingApplyEntityByEntPrefixId(Long id);



    List<EntPrefixHostingApplyEntity> findByEntId(Long entId);

    boolean existsByEntIdAndHostingState(Long entId, String hostingState);


}
