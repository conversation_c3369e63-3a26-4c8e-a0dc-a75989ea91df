package cn.teleinfo.idycprovince.infrastructure.db.entity;

import lombok.Data;

import javax.persistence.Column;
import javax.persistence.Entity;
import javax.persistence.Table;
import java.io.Serializable;

@Entity
@Data
@Table(name = "yc_ent_prefix_hosting_apply")
public class EntPrefixHostingApplyEntity extends BaseEntity implements Serializable {

    /** 企业前缀id */
    @Column(name = "ent_prefix_id")
    private Long entPrefixId ;
    /** 托管状态（分为 1.未托管、2.审核中、3.被驳回、4.已托管 */
    @Column(name = "hosting_state")
    private String hostingState ;
    /** 审核结果 */
    @Column(name = "audit_result_msg")
    private String auditResultMsg ;
    /** 所属企业 */
    @Column(name = "ent_id")
    private Long entId ;
}
