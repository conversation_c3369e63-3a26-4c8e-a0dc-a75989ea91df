package cn.teleinfo.idycprovince.infrastructure.db.repository;


import cn.teleinfo.idycprovince.infrastructure.db.entity.SyncAppEntity;
import org.springframework.data.jpa.repository.Query;
import org.springframework.data.repository.query.Param;

import java.util.List;

public interface SyncAppRepository extends BaseRepository<SyncAppEntity, Long> {

    List<SyncAppEntity> findBySourceType(Integer sourceType);

    @Query(
            value = "select * from yc_integrated_app_info ",
            nativeQuery = true
    )
    List<SyncAppEntity> findSyncAppEntity();

    @Query(
            value = "select * from yc_integrated_app_info where source_type = :sourceType ",
            nativeQuery = true
    )
    List<SyncAppEntity> findSyncAppEntityBySourceType(@Param("sourceType")Integer sourceType);

    List<SyncAppEntity> findByEntPrefix(String entPrefix);
}