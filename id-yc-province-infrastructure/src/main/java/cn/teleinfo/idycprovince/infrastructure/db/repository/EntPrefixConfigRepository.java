package cn.teleinfo.idycprovince.infrastructure.db.repository;

import cn.teleinfo.idycprovince.infrastructure.db.entity.EntPrefixConfigEntity;

import java.util.Collection;

public interface EntPrefixConfigRepository extends BaseRepository<EntPrefixConfigEntity, Long> {

    EntPrefixConfigEntity findByEntPrefixId(Long entPrefixId);

    void deleteByEntIdAndEntPrefixIdIn(Long entId, Collection<Long> entPrefixIds);
}