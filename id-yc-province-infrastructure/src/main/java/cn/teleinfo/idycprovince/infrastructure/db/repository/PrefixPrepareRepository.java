package cn.teleinfo.idycprovince.infrastructure.db.repository;

import cn.teleinfo.idycprovince.infrastructure.db.entity.PrefixPrepareEntity;
import cn.teleinfo.idycprovince.infrastructure.db.to.PrefixPrepareTO;
import org.springframework.data.domain.Page;
import org.springframework.data.domain.Pageable;
import org.springframework.data.jpa.repository.Query;
import org.springframework.data.repository.query.Param;

import java.util.Collection;
import java.util.Date;

public interface PrefixPrepareRepository extends BaseRepository<PrefixPrepareEntity, Long> {

    @Query(nativeQuery = true, value =
            "SELECT " +
                    " id AS id, " +
                    " ent_prefix AS entPrefix, " +
                    " `status` AS status, " +
                    " ent_id AS entId, " +
                    " province_id AS provinceId, " +
                    " remark AS remark, " +
                    " created_by AS createdBy, " +
                    " updated_by AS updatedBy, " +
                    " created_time AS createdTime, " +
                    " updated_time AS updatedTime, " +
                    " org_name AS orgName, " +
                    " org_code AS orgCode, " +
                    " parent_org_name AS parentOrgName, " +
                    " org_addr_province AS orgAddrProvince, " +
                    " org_addr_city AS orgAddrCity, " +
                    " org_addr_district AS orgAddrDistrict, " +
                    " org_address AS orgAddress, " +
                    " claim_time AS claimTime " +
                    " FROM " +
                    " yc_ent_prefix_prepare " +
                    " WHERE " +
                    " is_deleted = 0 " +
                    " AND province_id = :provinceId " +
                    " AND if(:entId != '' and :entId is not null, ent_id = :entId, 1=1 ) " +
                    " AND if(:entPrefix != '' and :entPrefix is not null, ent_prefix = :entPrefix, 1=1 ) " +
                    " AND if(:status is not null, `status` = :status, 1=1 ) " +
                    " AND if( :startTime != '' and :startTime is not null, claim_time >=  :startTime, 1=1 ) " +
                    " AND if( :endTime != '' and :endTime is not null, claim_time <=  :endTime, 1=1 ) " +
                    "", countQuery =
            "SELECT " +
                    " count(*) " +
                    " FROM " +
                    " yc_ent_prefix_prepare " +
                    "WHERE " +
                    " is_deleted = 0 " +
                    " AND province_id = :provinceId " +
                    " AND if(:entId != '' and :entId is not null, ent_id = :entId, 1=1 ) " +
                    " AND if(:entPrefix != '' and :entPrefix is not null, ent_prefix = :entPrefix, 1=1 ) " +
                    " AND if(:status is not null, `status` = :status, 1=1 ) " +
                    " AND if( :startTime != '' and :startTime is not null, claim_time >=  :startTime, 1=1 ) " +
                    " AND if( :endTime != '' and :endTime is not null, claim_time <=  :endTime, 1=1 ) "
    )
    Page<PrefixPrepareTO> listPrefixPrepare(@Param("entPrefix") String entPrefix,
                                            @Param("provinceId") Long provinceId, @Param("entId") Long entId
            , @Param("startTime") Date startTime
            , @Param("endTime") Date endTime
            , @Param("status") Integer status, @Param("pageable") Pageable pageable);

    @Query(nativeQuery = true, value =
            "SELECT " +
                    " id AS id, " +
                    " ent_prefix AS entPrefix, " +
                    " `status` AS status, " +
                    " ent_id AS entId, " +
                    " province_id AS provinceId, " +
                    " remark AS remark, " +
                    " created_by AS createdBy, " +
                    " updated_by AS updatedBy, " +
                    " created_time AS createdTime, " +
                    " updated_time AS updatedTime, " +
                    " org_name AS orgName, " +
                    " org_code AS orgCode, " +
                    " parent_org_name AS parentOrgName, " +
                    " org_addr_province AS orgAddrProvince, " +
                    " org_addr_city AS orgAddrCity, " +
                    " org_addr_district AS orgAddrDistrict, " +
                    " org_address AS orgAddress, " +
                    " claim_time AS claimTime " +
                    " FROM " +
                    " yc_ent_prefix_prepare " +
                    " WHERE " +
                    " is_deleted = 0 " +
                    " AND province_id = :provinceId " +
                    " AND if(:entPrefix != '' and :entPrefix is not null, ent_prefix = :entPrefix, 1=1 ) " +
                    " AND (ent_id = :entId or ent_id is null) " +
                    " AND if(:status is not null, `status` = :status, 1=1 ) " +
                    " AND if( :startTime != '' and :startTime is not null, claim_time >=  :startTime, 1=1 ) " +
                    " AND if( :endTime != '' and :endTime is not null, claim_time <=  :endTime, 1=1 ) " +
                    "", countQuery =
            "SELECT " +
                    " count(*) " +
                    " FROM " +
                    " yc_ent_prefix_prepare " +
                    "WHERE " +
                    " is_deleted = 0 " +
                    " AND province_id = :provinceId " +
                    " AND if(:entPrefix != '' and :entPrefix is not null, ent_prefix = :entPrefix, 1=1 ) " +
                    " AND (ent_id = :entId or ent_id is null) " +
                    " AND if(:status is not null, `status` = :status, 1=1 ) " +
                    " AND if( :startTime != '' and :startTime is not null, claim_time >=  :startTime, 1=1 ) " +
                    " AND if( :endTime != '' and :endTime is not null, claim_time <=  :endTime, 1=1 ) "
    )
    Page<PrefixPrepareTO> listPrefixPrepare4Ent(@Param("entPrefix") String entPrefix,
                                                @Param("provinceId") Long provinceId, @Param("entId") Long entId
            , @Param("startTime") Date startTime
            , @Param("endTime") Date endTime
            , @Param("status") Integer status, @Param("pageable") Pageable pageable);

    boolean existsByEntPrefix(String entPrefix);

    boolean existsByEntPrefixIn(Collection<String> entPrefixes);


}
