package cn.teleinfo.idycprovince.infrastructure.db.entity;

import lombok.Getter;
import lombok.Setter;

import javax.persistence.Column;
import javax.persistence.Entity;
import javax.persistence.Id;
import javax.persistence.Table;

/**
 * 卷烟规格基本信息
 */
@Getter
@Setter
@Entity
@Table(name = "TZ_AC_CIGARETTE")
public class CigaretteEntity {

    /**
     * 卷烟代码;卷烟规格唯一的标识代码
     */
    @Id
    @Column(name = "AC_CGT_CODE")
    private String code;

    /**
     * 卷烟名称;卷烟牌号名称和特征表述的组合,其表达方式为“卷烟牌号名称(特征表述)”
     */
    @Column(name = "AC_CGT_NAME")
    private String name;

}
