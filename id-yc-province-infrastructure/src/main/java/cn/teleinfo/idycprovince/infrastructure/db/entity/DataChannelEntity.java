package cn.teleinfo.idycprovince.infrastructure.db.entity;

import lombok.Getter;
import lombok.Setter;
import org.hibernate.annotations.SQLDelete;
import org.hibernate.annotations.SQLDeleteAll;
import org.hibernate.annotations.Where;

import javax.persistence.Column;
import javax.persistence.Entity;
import javax.persistence.Table;

@Getter
@Setter
@Entity
@Table(name = "yc_data_channel")
@SQLDelete(sql = "update yc_data_channel set is_deleted = null, updated_time = NOW() where id = ?")
@SQLDeleteAll(sql = "update yc_data_channel set is_deleted = null , updated_time = NOW()where id = ?")
@Where(clause = "is_deleted = 0")
public class DataChannelEntity extends BaseEntity{

    /**
     * 数据通道名称*
     */
    @Column(name = "data_channel_name")
    private String dataChannelName;

    /**
     * 对象标识id*
     */
    @Column(name = "object_handle_id")
    private Long objectHandleId;

    /**
     * 对象标识类型 1主数据，2非主数据*
     */
    @Column(name = "object_handle_type")
    private Integer objectHandleType;

    /**
     * 所属数据服务*
     */
    @Column(name = "data_service_id")
    private Long dataServiceId;

    /**
     * 数据通道id*
     */
    @Column(name = "data_channel_id")
    private Long dataChannelId;

    /**
     * 实例数据类型 1基础2数组*
     */
    @Column(name = "data_type")
    private Integer dataType;

    /**
     * 数据库id*
     */
    @Column(name = "database_id")
    private Long databaseId;

    /**
     * 解析sql*
     */
    @Column(name = "resolve_sql")
    private String resolveSql;

    /**
     * 查询sql*
     */
    @Column(name = "query_sql")
    private String querySql;

    /**
     * 下发状态 1成功2失败*
     */
    @Column(name = "send_status")
    private Integer sendStatus;

    /**
     * 企业id*
     */
    @Column(name = "ent_id")
    private Long entId;

    /**
     * 应用id*
     */
    @Column(name = "app_id")
    private Long appId;

    /**
     * 是否共享 1、已共享 2、未共享
     */
    @Column(name = "is_share")
    private Integer isShare;

}
