package cn.teleinfo.idycprovince.infrastructure.db.entity;

import com.fasterxml.jackson.annotation.JsonFormat;
import lombok.Data;
import org.springframework.data.annotation.CreatedBy;
import org.springframework.data.annotation.CreatedDate;
import org.springframework.data.annotation.LastModifiedBy;
import org.springframework.data.annotation.LastModifiedDate;

import javax.persistence.*;
import java.io.Serializable;
import java.time.LocalDateTime;

/**
 * <AUTHOR>
 */
@Entity
@Data
@Table(name = "yc_province")
public class ProvinceEntity implements Serializable {
    @Id
    @GeneratedValue(strategy = GenerationType.IDENTITY)
    private Long id;

    /**
     * 创建时间
     */
    @CreatedDate
    @Column(name = "created_time")
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss")
    private LocalDateTime createdTime;

    /**
     * 创建人
     */
    @CreatedBy
    @Column(name = "created_by")
    private Long createdBy;

    /**
     * 更新时间
     */
    @LastModifiedDate
    @Column(name = "updated_time")
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss")
    private LocalDateTime updatedTime;

    /**
     * 更新人
     */
    @LastModifiedBy
    @Column(name = "updated_by")
    private Long updatedBy;

    /**
     *  省级节点名称
     */
    @Column(name = "province_name")
    private Long provinceName;

    /**
     *  省级前缀
     */
    @Column(name = "shr_prefix")
    private Long shrPrefix;

    /**
     *  服务地址
     */
    @Column(name = "service_url")
    private String serviceUrl;

    /**
     *  运营公司名称
     */
    @Column(name = "manage_org_name")
    private String manageOrgName;

    /**
     *  运营公司性质
     */
    @Column(name = "org_nature")
    private String orgNature;

    /**
     *  组织机构代码
     */
    @Column(name = "org_crt_code")
    private String orgCrtCode;

    /**
     *  服务行业
     */
    @Column(name = "org_industry")
    private String orgIndustry;

    /**
     *  官方网址
     */
    @Column(name = "website")
    private String website;

    /**
     *  单位简介
     */
    @Column(name = "org_desc")
    private String orgDesc;

    /**
     *  单位所在省
     */
    @Column(name = "org_province")
    private String orgProvince;

    /**
     *  单位所在市
     */
    @Column(name = "org_city")
    private String orgCity;

    /**
     *  单位地址
     */
    @Column(name = "org_addr")
    private String orgAddr;

    /**
     *  过期时间
     */
    @Column(name = "expire_time")
    private LocalDateTime expireTime;

    /**
     *  路径
     */
    @Column(name = "path")
    private String path;

    /**
     *  状态
     */
    @Column(name = "enabled")
    private Integer enabled;

}