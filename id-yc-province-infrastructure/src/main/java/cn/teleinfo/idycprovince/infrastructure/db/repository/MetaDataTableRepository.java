package cn.teleinfo.idycprovince.infrastructure.db.repository;

import cn.teleinfo.idycprovince.infrastructure.db.entity.MetaDataTableEntity;

import java.util.List;

/**
 * Created with IntelliJ IDEA.
 *
 * @Author: liusp
 * @Date: 2023/10/18/18:31
 * @Description:
 */
public interface MetaDataTableRepository extends BaseRepository<MetaDataTableEntity,Long> {

    List<MetaDataTableEntity> findByDbIdAndAppId(Long databaseId,Long appId);
}
