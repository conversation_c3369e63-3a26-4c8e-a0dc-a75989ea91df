package cn.teleinfo.idycprovince.infrastructure.db.entity;

import lombok.Getter;
import lombok.Setter;
import org.hibernate.annotations.SQLDelete;
import org.hibernate.annotations.SQLDeleteAll;
import org.hibernate.annotations.Where;

import javax.persistence.Column;
import javax.persistence.Entity;
import javax.persistence.Table;

/***
 * @title HandleMaintainEntity
 * @description 标识维护属性表
 * <AUTHOR>
 * @version 1.0.0
 * @create 2023/2/27 11:15
 **/
@Getter
@Setter
@Entity
@Table(name = "yc_handle_maintain_item")
@SQLDelete(sql = "update yc_handle_maintain_item set is_deleted = null, updated_time = NOW()  where id = ?")
@SQLDeleteAll(sql = "update yc_handle_maintain_item set is_deleted = null, updated_time = NOW()  where id = ?")
@Where(clause = "is_deleted = 0")
public class HandleMaintainItemEntity extends BaseEntity {

    /**
     *  标识
     */
    @Column(name = "handle")
    private String handle;

    /**
     * 标识维护表ID
     */
    @Column(name = "maintain_id")
    private Long maintainId;

    /**
     * 索引
     */
    @Column(name = "field_index")
    private Integer fieldIndex;

    /**
     * 属性名称
     */
    @Column(name = "field")
    private String field;

    /**
     * 属性值
     */
    @Column(name = "field_value")
    private String fieldValue;

    /**
     * 属性描述
     */
    @Column(name = "description")
    private String description;

    /**
     * 属性类型:1固定值 3标识值 4标识-属性
     */
    @Column(name = "field_type")
    private Integer fieldType;

    /**
     *  企业ID
     */
    @Column(name = "ent_id")
    private Long entId;

}
