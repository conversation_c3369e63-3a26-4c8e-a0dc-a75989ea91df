package cn.teleinfo.idycprovince.infrastructure.db.entity;

import lombok.Getter;
import lombok.Setter;
import org.hibernate.annotations.SQLDelete;
import org.hibernate.annotations.SQLDeleteAll;
import org.hibernate.annotations.Where;
import org.springframework.data.annotation.CreatedBy;
import org.springframework.data.annotation.CreatedDate;
import org.springframework.data.annotation.LastModifiedBy;
import org.springframework.data.annotation.LastModifiedDate;
import org.springframework.data.jpa.domain.support.AuditingEntityListener;

import javax.persistence.*;
import java.io.Serializable;
import java.time.LocalDateTime;

/**
 * <p>
 * 权限表
 * </p>
 *
 * <AUTHOR>
 * @since 2022-11-04
 */
@Getter
@Setter
@Entity
@Table(name = "yc_auth")
@SQLDelete(sql = "update yc_auth set is_deleted = null, updated_time = NOW() where id = ?")
@SQLDeleteAll(sql = "update yc_auth set is_deleted = null, updated_time = NOW() where id = ?")
@Where(clause = "is_deleted = 0")
@EntityListeners(AuditingEntityListener.class)
public class AuthEntity implements Serializable {

    private static final long serialVersionUID = 1L;

    public static final Long ROOT_ID = 0L;

    public static final Integer SYSTEM_TYPE_SUPER_ADMIN = 0;
    public static final Integer SYSTEM_TYPE_PROVINCE = 1;
    public static final Integer SYSTEM_TYPE_ENT = 2;
    public static final Integer SYSTEM_TYPE_APP = 3;

    /**
     * 父级id
     */
    @Column(name = "parent_id")
    private Long parentId;
    @Column(name = "auth_code")
    /**
     * 权限编码;（menu和resource）
     */
    private String authCode;
    /**
     * 权限名称;MENU/RESOURCE
     */
    @Column(name = "auth_name")
    private String authName;
    /**
     * 描述
     */
    @Column(name = "auth_desc")
    private String authDesc;
    /**
     * 0:目录，1:菜单，2:按钮
     */
    @Column(name = "auth_type")
    private Integer authType;
    /**
     * 1省级；2企业级；3应用；
     */
    @Column(name = "level_type")
    private String levelType;
    /**
     * 接口权限编码
     */
    @Column(name = "auth_permission")
    private String authPermission;

    @Id
    @GeneratedValue(strategy = GenerationType.IDENTITY)
    private Long id;

    /**
     * 创建时间
     */
    @CreatedDate
    @Column(name = "created_time")
    private LocalDateTime createdTime;

    /**
     * 创建人
     */
    @CreatedBy
    @Column(name = "created_by")
    private Long createdBy;

    /**
     * 更新时间
     */
    @LastModifiedDate
    @Column(name = "updated_time")
    private LocalDateTime updatedTime;

    /**
     * 更新人
     */
    @LastModifiedBy
    @Column(name = "updated_by")
    private Long updatedBy;

    /**
     * 逻辑删除: 0 未删除 null 已删除
     */
    @Column(name = "is_deleted")
    private Integer isDeleted = 0;

}
