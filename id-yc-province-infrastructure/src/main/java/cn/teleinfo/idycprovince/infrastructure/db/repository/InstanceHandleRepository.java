package cn.teleinfo.idycprovince.infrastructure.db.repository;

import cn.teleinfo.idycprovince.infrastructure.db.entity.InstanceHandleEntity;
import cn.teleinfo.idycprovince.infrastructure.db.to.InstanceHandleTO;
import org.springframework.data.domain.Page;
import org.springframework.data.domain.Pageable;
import org.springframework.data.jpa.repository.Query;
import org.springframework.data.repository.query.Param;

import java.util.Date;

public interface InstanceHandleRepository extends BaseRepository<InstanceHandleEntity, Long> {
    @Query(nativeQuery = true, value =
            "SELECT " +
                    " id AS id, " +
                    " created_by AS createdBy, " +
                    " updated_by AS updatedBy, " +
                    " created_time AS createdTime, " +
                    " updated_time AS updatedTime, " +
                    " province_id AS provinceId, " +
                    " instance_handle AS instanceHandle, " +
                    " instance_handle_name AS instanceHandleName " +
                    " FROM " +
                    " yc_instance_handle " +
                    " WHERE " +
                    " is_deleted = 0 " +
                    " AND province_id = :provinceId " +
                    " AND ent_id = :entId " +
                    " AND if( :startTime != '' and :startTime is not null, created_time >=  :startTime, 1=1 ) " +
                    " AND if( :endTime != '' and :endTime is not null, created_time <=  :endTime, 1=1 ) " +
                    " AND if( :updateStartTime != '' and :updateStartTime is not null, updated_time >=  :updateStartTime, 1=1 ) " +
                    " AND if( :updateEndTime != '' and :updateEndTime is not null, updated_time <=  :updateEndTime, 1=1 ) " +
                    " AND if(:instanceHandle != '' and :instanceHandle is not null, instance_handle like CONCAT('%',:instanceHandle,'%'), 1=1 ) " +
                    " AND if(:instanceHandleName != '' and :instanceHandleName is not null, instance_handle_name like CONCAT('%',:instanceHandleName,'%'), 1=1 ) " +
                    "", countQuery =
            "SELECT " +
                    " count(*) " +
                    " FROM " +
                    " yc_instance_handle " +
                    " WHERE " +
                    " is_deleted = 0 " +
                    " AND province_id = :provinceId " +
                    " AND ent_id = :entId " +
                    " AND if( :startTime != '' and :startTime is not null, created_time >=  :startTime, 1=1 ) " +
                    " AND if( :endTime != '' and :endTime is not null, created_time <=  :endTime, 1=1 ) " +
                    " AND if( :updateStartTime != '' and :updateStartTime is not null, updated_time >=  :updateStartTime, 1=1 ) " +
                    " AND if( :updateEndTime != '' and :updateEndTime is not null, updated_time <=  :updateEndTime, 1=1 ) " +
                    " AND if(:instanceHandle != '' and :instanceHandle is not null, instance_handle like CONCAT('%',:instanceHandle,'%'), 1=1 ) " +
                    " AND if(:instanceHandleName != '' and :instanceHandleName is not null, instance_handle_name like CONCAT('%',:instanceHandleName,'%'), 1=1 ) " +
                    ""
    )
    Page<InstanceHandleTO> listInstanceHandle(@Param("provinceId") Long provinceId
            , @Param("entId") Long entId
            , @Param("instanceHandle") String instanceHandle
            , @Param("instanceHandleName") String instanceHandleName
            , @Param("startTime") Date startTime
            , @Param("endTime") Date endTime
            , @Param("updateStartTime") Date updateStartTime
            , @Param("updateEndTime") Date updateEndTime
            , @Param("pageable") Pageable pageable);


    InstanceHandleEntity findInstanceHandleEntitiesByInstanceHandleNameLike(String instanceHandleName);

    InstanceHandleEntity findInstanceHandleEntitiesByInstanceHandle(String instanceHandle);

}
