package cn.teleinfo.idycprovince.infrastructure.db.repository;

import cn.teleinfo.idycprovince.infrastructure.db.entity.DatabaseEntity;

import java.util.List;

/**
 *
 *
 * @Author: liusp
 * @Date: 2023/10/16/15:58
 * @Description:
 */
public interface DatabaseRepository extends BaseRepository<DatabaseEntity,Long> {

    List<DatabaseEntity> findByDataServiceId(Long dataServiceId);

    DatabaseEntity findByAppIdAndDataServiceIdAndDatabaseName(Long currentAppId, Long dataServiceId, String databaseName);

    DatabaseEntity findByDataServiceIdAndDatabaseName(Long dataServiceId, String databaseName);

    DatabaseEntity findByDataServiceIdAndDatabaseNameAndDatabaseIp(Long dataServiceId, String databaseName, String databaseIp);

    DatabaseEntity findByDataServiceIdAndDataSourceId(Long dataServiceId, long id);

}
