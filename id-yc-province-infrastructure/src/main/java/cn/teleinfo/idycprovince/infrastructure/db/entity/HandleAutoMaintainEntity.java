package cn.teleinfo.idycprovince.infrastructure.db.entity;

import lombok.Data;
import org.hibernate.annotations.SQLDelete;
import org.hibernate.annotations.SQLDeleteAll;
import org.hibernate.annotations.Where;

import javax.persistence.Column;
import javax.persistence.Entity;
import javax.persistence.Table;
import java.time.LocalDateTime;

/**
 * <AUTHOR>
 * @version 1.0
 * @date Created in 2023/6/22
 * @description
 */
@Entity
@Data
@SQLDelete(sql = "update yc_handle_data_service set is_deleted = null, updated_time = NOW() where id = ?")
@SQLDeleteAll(sql = "update yc_handle_data_service set is_deleted = null, updated_time = NOW() where id = ?")
@Where(clause = "is_deleted = 0")
@Table(name = "yc_handle_auto_maintain")
public class HandleAutoMaintainEntity extends BaseEntity{
    
    @Column(name = "monitor_handle")
    private String monitorHandle;

    @Column(name = "monitor_handle_name")
    private String monitorHandleName;

    @Column(name = "monitor_application")
    private String monitorApplication;
    
    @Column(name = "maintain_handle")
    private String maintainHandle;

    @Column(name = "maintain_handle_name")
    private String maintainHandleName;

    @Column(name = "maintain_handle_app_name")
    private String maintainHandleAppName;

    @Column(name = "maintain_time")
    private LocalDateTime maintainTime;
    
    @Column(name = "ent_id")
    private Long entId;
    
    @Column(name = "app_id")
    private Long appId;
}
