package cn.teleinfo.idycprovince.infrastructure.db.repository;

import cn.teleinfo.idycprovince.infrastructure.db.entity.HandleDataServiceEntity;

import java.util.List;

/**
 * <AUTHOR>
 */
public interface HandleDataServiceRepository extends BaseRepository<HandleDataServiceEntity,Long> {
    
    /**
     * 根据标识id查询关联数据服务
     *
     * @param id
     * @return 结果
     **/
    List<HandleDataServiceEntity> findByHandleId(Long id);
    
    /**
     * 根据标识id删除关联数据服务
     *
     * @param id
     * @return 结果
     **/
    void deleteByHandleId(Long id);

}