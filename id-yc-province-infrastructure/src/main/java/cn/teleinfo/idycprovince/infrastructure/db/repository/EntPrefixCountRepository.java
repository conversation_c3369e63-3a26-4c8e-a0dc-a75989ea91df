package cn.teleinfo.idycprovince.infrastructure.db.repository;

import cn.teleinfo.idycprovince.infrastructure.db.entity.EntPrefixCount;
import cn.teleinfo.idycprovince.infrastructure.db.to.EntPrefixReportTO;
import cn.teleinfo.idycprovince.infrastructure.db.to.GraphStatisticsTO;
import cn.teleinfo.idycprovince.infrastructure.db.to.OrgPrefixReportTO;
import cn.teleinfo.idycprovince.infrastructure.db.to.TotalStatisticsTO;
import cn.teleinfo.idycprovince.infrastructure.db.view.PrefixTotalCountView;
import org.springframework.data.jpa.repository.Modifying;
import org.springframework.data.jpa.repository.Query;
import org.springframework.data.repository.query.Param;
import org.springframework.transaction.annotation.Transactional;

import java.util.Collection;
import java.util.List;

/**
 * <AUTHOR>
 * @version 1.0
 * @date Created in 2022/11/5
 * @description 节点统计Repository
 */
public interface EntPrefixCountRepository extends BaseRepository<EntPrefixCount,Long> {

    EntPrefixCount findByEntPrefixAndStatisticsDate(String entPrefix,String statisticalDate);

    List<EntPrefixCount> findByStatisticsDate(String statisticalDate);

    /**
     * 查询当前账号全部统计数
     * @param provinceId
     * @param entId
     * @return
     */
    @Query(nativeQuery = true, value =
            "SELECT " +
            "ifnull( sum( yc_ent_prefix_count.handle_query_num ), 0 ) AS handleQuery, " +
            "ifnull( sum( yc_ent_prefix_count.handle_create_num ), 0 )  AS handleCreate " +
            "FROM " +
            "yc_ent_prefix_count " +
            "INNER JOIN (yc_ent_prefix) " +
            "ON " +
            "yc_ent_prefix_count.ent_prefix = yc_ent_prefix.ent_prefix " +
            "WHERE " +
            "yc_ent_prefix.province_id = :provinceId " +
            "AND if(:entId != '' and :entId is not null,yc_ent_prefix.ent_id = :entId, 1=1 ) " +
            "AND if(:entPrefix != '' and :entPrefix is not null,yc_ent_prefix.ent_prefix = :entPrefix, 1=1 ) " +
            "AND yc_ent_prefix.is_deleted = 0 ")
    TotalStatisticsTO findTotalCountByProvinceIdAndEntIdAndEntPrefix(@Param("provinceId") Long provinceId, @Param("entId") Long entId, @Param("entPrefix") String entPrefix);

    @Query(nativeQuery = true, value =
            "SELECT " +
                    "ifnull( sum( yc_ent_prefix_count.handle_query_num ), 0 ) AS handleQuery, " +
                    "ifnull( sum( yc_ent_prefix_count.handle_create_num ), 0 ) AS handleCreate, " +
                    "ifnull( yc_ent_prefix_count.statistics_mouth, :month) AS statisticsMonth " +
                    "FROM " +
                    "yc_ent_prefix_count " +
                    "INNER JOIN (yc_ent_prefix) " +
                    "ON " +
                    "yc_ent_prefix_count.ent_prefix = yc_ent_prefix.ent_prefix " +
                    "WHERE " +
                    "yc_ent_prefix.province_id = :provinceId " +
                    "AND if(:entId != '' and :entId is not null,yc_ent_prefix.ent_id = :entId, 1=1 ) " +
                    "AND if(:entPrefix != '' and :entPrefix is not null,yc_ent_prefix.ent_prefix = :entPrefix, 1=1 ) " +
                    "AND yc_ent_prefix_count.statistics_mouth = :month " +
                    "AND yc_ent_prefix.is_deleted = 0")
    GraphStatisticsTO findGraphCountByProvinceIdAndEntIdAndEntPrefix(@Param("provinceId") Long provinceId,
                                                         @Param("entId") Long entId,
                                                         @Param("entPrefix") String entPrefix,
                                                         @Param("month") String month);


    /**
     * 省级统计结果,省级当日累计量
     * @param date
     * @return
     */
    @Query(nativeQuery = true, value =
            "SELECT " +
                "ifnull( sum( yepc.handle_create_num ), 0 ) AS prefixRegCountDaily " +
                "FROM yc_ent_prefix_count yepc " +
                "LEFT JOIN yc_ent_prefix AS yep " +
                "ON yepc.ent_prefix = yep.ent_prefix " +
                "AND yep.is_deleted = 0 " +
                "WHERE yep.province_id = :provinceId " +
                "AND statistics_date = :date "
                )
    Long getProvinceRegCountToday(@Param("provinceId") Long provinceId,@Param("date") String date);


    @Query(nativeQuery = true, value =
            "SELECT " +
                    "ifnull( sum( yepc.handle_query_num ), 0 ) AS prefixRegCountDaily " +
                    "FROM yc_ent_prefix_count yepc " +
                    "LEFT JOIN yc_ent_prefix AS yep " +
                    "ON yepc.ent_prefix = yep.ent_prefix " +
                    "AND yep.is_deleted = 0 " +
                    "WHERE yep.province_id = :provinceId " +
                    "AND statistics_date = :date "
    )
    Long getProvinceResolveCountToday(@Param("provinceId") Long provinceId,@Param("date") String date);



    /**
     * 企业统计结果
     * @param date
     * @return
     */
    @Query(nativeQuery = true, value =
            "SELECT yepc.province_prefix as shrPrefix,yepc.ent_prefix as entPrefix," +
                "ifnull( sum( yepc.handle_query_num ), 0 ) AS resolveCountDaily," +
                "ifnull( sum( yepc.handle_create_num ), 0 ) AS regCountDaily," +
                "ifnull( sum( yepc.handle_delete_num ), 0 ) AS delCountDaily" +
                " FROM yc_ent_prefix_count yepc " +
                "LEFT JOIN yc_ent_prefix AS yep " +
                "ON yepc.ent_prefix = yep.ent_prefix " +
                "AND yep.is_deleted = 0 " +
                "WHERE yep.province_id = :provinceId " +
                "AND yepc.statistics_date = :date " +
                "GROUP BY yepc.province_prefix,yepc.ent_prefix")
    List<EntPrefixReportTO> findEntPrefixReportTOByDate(@Param("provinceId") Long provinceId,@Param("date") String date);

    /**
     * 企业当天上报统计量
     * @param date
     * @return
     */
    @Query(nativeQuery = true, value =
            "SELECT yepc.province_prefix as shrPrefix," +
                    "ifnull( sum( yepc.handle_query_num ), 0 ) AS resolveCountDaily," +
                    "ifnull( sum( yepc.handle_create_num ), 0 ) AS regCountDaily," +
                    "ifnull( sum( yepc.handle_delete_num ), 0 ) AS delCountDaily " +
                    "FROM yc_ent_prefix_count AS yepc " +
                    "LEFT JOIN yc_ent_prefix AS yep " +
                    "ON yepc.ent_prefix = yep.ent_prefix " +
                    "where if(:date != '' AND :date IS NOT NULL, yepc.statistics_date = :date,1=1) " +
                    "AND yep.province_id = :provinceId " +
                    "AND yep.is_deleted = 0 " +
                    "GROUP BY shrPrefix")
    List<EntPrefixReportTO> findEntPrefixReportTOByCurrentDate(@Param("provinceId") Long provinceId,@Param("date") String date);
    @Transactional
    @Modifying(clearAutomatically = true)
    @Query(nativeQuery = true, value = "update yc_ent_prefix_count set  handle_create_num=?2 where id=?1")
    void updateHandleCreateById(@Param("id") Long id,@Param("handleCreateNum") Long handleCreateNum);

    @Transactional
    @Modifying(clearAutomatically = true)
    @Query(nativeQuery = true, value = "update yc_ent_prefix_count set handle_delete_num=?2 where id=?1")
    void updateHandleDeleteById(@Param("id") Long id,@Param("handleDeleteNum")Long handleDeleteNum);




    @Transactional
    @Modifying
    @Query(nativeQuery = true,value = "update yc_ent_prefix_count set handle_query_num = handle_query_num + :num where ent_prefix = :entPrefix and statistics_date = :date")
    void updateHandleQueryNum(@Param("num") Long num,@Param("entPrefix") String entPrefix,@Param("date") String date);
    @Query(nativeQuery = true, value = "SELECT " +
            "t1.province_prefix AS provincePrefix, " +
            "t1.ent_prefix AS entPrefix, " +
            "CASE WHEN t2.is_deleted is null THEN 0 ELSE ifnull( sum( t1.handle_query_num ), 0 ) END AS handleQueryNum, "
            +
            "CASE WHEN t2.is_deleted is null THEN 0 ELSE ifnull( sum( t1.handle_create_num ), 0 ) END AS handleCreateNum, "
            +
            "CASE WHEN t2.is_deleted is null THEN 0 ELSE ifnull( sum( t1.handle_delete_num ), 0 ) END AS handleDeleteNum, "
            +
            "CASE WHEN t2.is_deleted is null THEN 0 ELSE ifnull( sum( t1.handle_value_add_num ), 0 ) END AS handleValueAddNum, "
            +
            "CASE WHEN t2.is_deleted is null THEN 0 ELSE ifnull( sum( t1.handle_value_modify_num ), 0 ) END AS handleValueModifyNum, "
            +
            "CASE WHEN t2.is_deleted is null THEN 0 ELSE ifnull( sum( t1.handle_value_remove_num ), 0 ) END AS handleValueRemoveNum "
            +
            "FROM " +
            "yc_ent_prefix_count AS t1 " +
            "LEFT JOIN yc_ent_prefix AS t2 ON t1.ent_prefix = t2.ent_prefix " +
            "WHERE " +
            "t1.statistics_date < :day " +
            "GROUP BY " +
            "provincePrefix," +
            "entPrefix")
    List<PrefixTotalCountView> findByStatisticsBeforeToday(@Param("day") String day);

    /**
     * 省级昨天解析累积量
     * @param day
     * @return
     */
    @Query(nativeQuery = true, value = "SELECT " +
            "ifnull( sum( yepc.handle_query_num ), 0 ) AS handleQueryNum " +
            "FROM " +
            "yc_ent_prefix_count yepc " +
            "LEFT JOIN yc_ent_prefix AS yep " +
            "ON yepc.ent_prefix = yep.ent_prefix  " +
            "LEFT JOIN  yc_ent_prefix_hosting yeph on yep.ent_prefix = yeph.ent_prefix  " +
            "WHERE yep.province_id = :provinceId " +
            "AND yepc.statistics_date < :day " +
            "AND yep.is_deleted = 0 "+
            "AND yeph.is_deleted= 0 AND yeph.hosting_state='1'"

    )
    Long getProvinceHostingResolveCount(@Param("provinceId") Long provinceId,@Param("day") String day);

    @Query(nativeQuery = true, value = "SELECT " +
            "ifnull( sum( yepc.handle_query_num ), 0 ) AS handleQueryNum " +
            "FROM " +
            "yc_ent_prefix_count yepc " +
            "LEFT JOIN yc_ent_prefix AS yep " +
            "ON yepc.ent_prefix = yep.ent_prefix " +
            "WHERE yep.province_id = :provinceId " +
            "AND yepc.statistics_date < :day " +
            "AND yep.is_deleted = 0 " +
            "AND yep.ent_id = :entId "
    )
    Long getEntResolveCount(@Param("provinceId") Long provinceId, @Param("entId") Long entId, @Param("day") String day);


    /**
     * 省级昨天解析累积量
     * @param day
     * @return
     */
    @Query(nativeQuery = true, value = "SELECT " +
            "yepc.ent_prefix AS entPrefix, " +
            "ifnull( sum( yepc.handle_query_num ), 0 ) AS handleQueryNum " +
            "FROM " +
            "yc_ent_prefix_count yepc " +
            "LEFT JOIN yc_ent_prefix AS yep " +
            "ON yepc.ent_prefix = yep.ent_prefix " +
            "WHERE yep.province_id = :provinceId " +
            "AND yepc.statistics_date < :day " +
            "AND yepc.source_type = 1 "+
            "AND yep.is_deleted = 0 " +
            "GROUP BY entPrefix")
    List<PrefixTotalCountView> findByStatisticsYesterday(@Param("provinceId") Long provinceId,@Param("day") String day);


    @Query(nativeQuery = true, value =
            "SELECT yepc.province_prefix as shrPrefix," +
                    "ifnull( sum( yepc.handle_query_num ), 0 ) AS resolveCountDaily," +
                    "ifnull( sum( yepc.handle_create_num ), 0 ) AS regCountDaily," +
                    "ifnull( sum( yepc.handle_delete_num ), 0 ) AS delCountDaily " +
                    "FROM yc_ent_prefix_count AS yepc " +
                    "LEFT JOIN yc_ent_prefix AS yep " +
                    "ON yepc.ent_prefix = yep.ent_prefix " +
                    "where if(:date != '' AND :date IS NOT NULL, yepc.statistics_date = :date,1=1) " +
                    "AND yep.province_id = :provinceId " +
                    "AND yep.is_deleted = 0 " +
                    "AND yep.ent_id = :entId  " +
                    "GROUP BY shrPrefix")
    List<EntPrefixReportTO> findEntPrefixReportTOByCurrentDateAndEntId(@Param("provinceId") Long provinceId,@Param("date") String yesterday,@Param("entId") Long entId);

    void deleteByEntPrefixIn(Collection<String> entPrefixes);
}
