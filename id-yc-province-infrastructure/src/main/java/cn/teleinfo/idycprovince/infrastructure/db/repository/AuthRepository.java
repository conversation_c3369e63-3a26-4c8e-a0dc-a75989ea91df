package cn.teleinfo.idycprovince.infrastructure.db.repository;

import cn.teleinfo.idycprovince.infrastructure.db.entity.AuthEntity;
import cn.teleinfo.idycprovince.infrastructure.db.to.AuthTo;
import org.springframework.data.jpa.repository.Query;
import org.springframework.data.repository.query.Param;

import java.util.Collection;
import java.util.List;

public interface AuthRepository extends BaseRepository<AuthEntity, Long> {

    @Query(nativeQuery = true, value = "select a.id, a.parent_id as parentId, a.auth_code as authCode, a.auth_name as authName, a.auth_desc as authDesc, a.auth_type as authType, a.level_type as levelType, a.sort, if(b.id is null, 0, 1) as selected from yc_auth as a " +
            "left join yc_role_auth as b on b.auth_id = a.id and b.role_id = :roleId and b.is_deleted = 0 " +
            " where a.is_deleted = 0 and FIND_IN_SET(:levelType, a.level_type) " +
            " AND IF(:unDelete != 1, a.auth_name != '标识注册', b.id is not null ) ")
    List<AuthTo> findAllAuthListByLevelTypeAndRoleIdAndUndelete(@Param("levelType") Integer levelType,@Param("roleId") Long roleId, @Param("unDelete") Integer unDelete);

    @Query(nativeQuery = true, value = "select a.id, a.parent_id as parentId, a.auth_code as authCode, a.auth_name as authName, a.auth_desc as authDesc, a.auth_type as authType, a.level_type as levelType, a.sort, if(b.id is null, 0, 1) as selected from yc_auth as a " +
            "inner join yc_role_auth as b on b.auth_id = a.id and b.role_id in (:roleIdList) and b.is_deleted = 0 " +
            " where a.is_deleted = 0 ")
    List<AuthTo> findRoleAuthListBySystemTypeAndRoleIds(@Param("roleIdList") Collection<Long> roleIdList);
}
