package cn.teleinfo.idycprovince.infrastructure.db.entity;

import java.io.Serializable;

import lombok.Data;
import org.hibernate.annotations.SQLDelete;
import org.hibernate.annotations.SQLDeleteAll;
import org.hibernate.annotations.Where;

import javax.persistence.*;

/**
 * <AUTHOR>
 */
@Entity
@Data
@SQLDelete(sql = "update yc_handle_data_service set is_deleted = null, updated_time = NOW() where id = ?")
@SQLDeleteAll(sql = "update yc_handle_data_service set is_deleted = null, updated_time = NOW() where id = ?")
@Where(clause = "is_deleted = 0")
@Table(name = "yc_handle_data_service")
public class HandleDataServiceEntity extends BaseEntity implements Serializable {

    /**
    *  数据服务id
    */
    @Column(name = "data_service_id")
    private Long dataServiceId;

    /**
    *  标识id
    */
    @Column(name = "handle_id")
    private Long handleId;

    /**
    *  企业节点id
    */
    @Column(name = "ent_id")
    private Long entId;


}