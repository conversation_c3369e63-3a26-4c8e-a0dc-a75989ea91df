package cn.teleinfo.idycprovince.infrastructure.db.repository;


import cn.teleinfo.idycprovince.infrastructure.db.entity.HandleAuditLogEntity;
import cn.teleinfo.idycprovince.infrastructure.db.view.HandleAuditLogView;
import org.springframework.data.jpa.repository.Query;
import org.springframework.data.repository.query.Param;

import java.util.List;
import java.util.Optional;

public interface HandleAuditLogRepository extends BaseRepository<HandleAuditLogEntity, Long> {


    @Query(nativeQuery = true, value = "select t.id,\n" +
            "       t.created_by AS createdBy,\n" +
            "       t.created_time AS createdTime,\n" +
            "       t.updated_by AS updatedBy,\n" +
            "       t.updated_time AS updatedTime,\n" +
            "       t.handle_id AS handleId,\n" +
            "       t.audit_type AS auditType,\n" +
            "       t.audit_state AS auditState,\n" +
            "       t.audit_remark AS auditRemark,\n" +
            "       t.province_id AS provinceId,\n" +
            "       t.ent_id AS entId,\n" +
            "       (SELECT u.nick_name FROM yc_user u WHERE u.id = t.created_by AND u.is_deleted = 0) AS createdByName,\n" +
            "       (SELECT u.nick_name FROM yc_user u WHERE u.id = t.updated_by AND u.is_deleted = 0) AS updatedByName\n" +
            "from yc_handle_audit_log t\n" +
            "where t.handle_id = :handleId\n" +
            "  and t.is_deleted = 0")
    List<HandleAuditLogView> findByHandleId(@Param("handleId") Long handleId);


    Optional<HandleAuditLogEntity> findTopByHandleIdOrderByCreatedTimeDesc(Long handleId);
}
