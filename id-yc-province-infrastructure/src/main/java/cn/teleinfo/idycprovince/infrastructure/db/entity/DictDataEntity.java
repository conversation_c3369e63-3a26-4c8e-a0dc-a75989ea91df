package cn.teleinfo.idycprovince.infrastructure.db.entity;


import lombok.Getter;
import lombok.Setter;

import javax.persistence.Column;
import javax.persistence.Entity;
import javax.persistence.Table;
import java.io.Serializable;

/**
 * <p>
 *
 * </p>
 *
 * <AUTHOR>
 * @since 2022-11-04
 */

@Getter
@Setter
@Entity
@Table(name = "yc_dict_data")
public class DictDataEntity extends BaseEntity implements Serializable {

    private static final long serialVersionUID = 1L;

    @Column(name = "dict_id")
    private String dictId;

    @Column(name = "dict_type")
    private String dictType;

    @Column(name = "dict_key")
    private String dictKey;

    @Column(name = "dict_value")
    private String dictValue;

    @Column(name = "sort")
    private Integer sort;

}
