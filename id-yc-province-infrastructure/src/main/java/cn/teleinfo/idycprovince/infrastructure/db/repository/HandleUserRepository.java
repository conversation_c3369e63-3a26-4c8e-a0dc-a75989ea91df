package cn.teleinfo.idycprovince.infrastructure.db.repository;

import cn.teleinfo.idycprovince.infrastructure.db.entity.HandleUserEntity;
import cn.teleinfo.idycprovince.infrastructure.db.view.HandleUserPageView;
import org.springframework.data.domain.Page;
import org.springframework.data.domain.Pageable;
import org.springframework.data.jpa.repository.Query;
import org.springframework.data.repository.query.Param;

import java.util.List;

/**
 *
 *
 * @Author: liusp
 * @Date: 2023/07/19/14:44
 * @Description:
 */
public interface HandleUserRepository extends BaseRepository<HandleUserEntity,Long>{


    HandleUserEntity findByAppId(Long appId);

    List<HandleUserEntity>  findByEntPrefixId(Long entPrefixId);

    List<HandleUserEntity> findByEntIdAndUserType(Long entId, Integer userType);

    HandleUserEntity  findByHandleUser(String handleUser);

    HandleUserEntity findByEntPrefixIdAndUserType(Long entprefixId, Integer userType);

    @Query(nativeQuery = true, value = "SELECT\n" +
            " a.ent_prefix as entPrefix," +
            " b.id as id," +
            " b.handle_name as handleName," +
            " b.handle_user as handleUser," +
            " b.handle_index as handleIndex," +
            " b.handle_pubkey as handlePubkey," +
            " b.created_time as createdTime," +
            " b.updated_time as updatedTime" +
            " FROM" +
            " yc_ent_prefix a" +
            " LEFT JOIN yc_handle_user b ON a.id = b.ent_prefix_id" +
            " AND (:userType IS NULL OR b.user_type = :userType) " +
            " AND b.is_deleted = 0 " +
            " WHERE" +
            " a.is_deleted = 0 " +
            " AND (:entId IS NULL OR a.ent_id = :entId) " +
            " AND (:id IS NULL OR b.id = :id)" +
            " ORDER BY b.id IS NULL DESC", countQuery = "SELECT" +
            " count(*) " +
            " FROM" +
            " yc_ent_prefix a\n" +
            " LEFT JOIN yc_handle_user b ON a.id = b.ent_prefix_id " +
            " AND (:userType IS NULL OR b.user_type = :userType) " +
            " AND b.is_deleted = 0 " +
            " WHERE" +
            " a.is_deleted = 0 " +
            " AND (:entId IS NULL OR a.ent_id = :entId) " +
            " AND (:id IS NULL OR b.id = :id)")
    Page<HandleUserPageView> pageQuery(
            @Param("id")Integer id,
            @Param("entId") Long entId,
            @Param("userType")Integer userType,
            Pageable pageable);
}
