package cn.teleinfo.idycprovince.infrastructure.db.repository;


import cn.teleinfo.idycprovince.infrastructure.db.entity.SyncChannelEntity;
import org.springframework.data.jpa.repository.Query;
import org.springframework.data.repository.query.Param;

import java.util.List;

public interface SyncChannelRepository extends BaseRepository<SyncChannelEntity, Long> {

    @Query(
            nativeQuery = true,
            value = "SELECT * FROM yc_integrated_data_channel WHERE source_type = :sourceType"
    )
    List<SyncChannelEntity> findBySourceType(@Param("sourceType")Integer sourceType);

    @Query(
            nativeQuery = true,
            value = "SELECT * FROM yc_integrated_data_channel"
    )
    List<SyncChannelEntity> findSyncChannelEntities();
}