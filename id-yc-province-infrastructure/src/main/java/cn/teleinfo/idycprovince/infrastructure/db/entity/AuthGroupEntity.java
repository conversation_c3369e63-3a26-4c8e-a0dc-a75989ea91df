package cn.teleinfo.idycprovince.infrastructure.db.entity;

import lombok.Getter;
import lombok.Setter;
import org.hibernate.annotations.SQLDelete;
import org.hibernate.annotations.SQLDeleteAll;
import org.hibernate.annotations.Where;

import javax.persistence.Column;
import javax.persistence.Entity;
import javax.persistence.Table;

@Getter
@Setter
@Entity
@Table(name = "yc_auth_group")
@SQLDelete(sql = "update yc_auth_group set is_deleted = null, updated_time = NOW() where id = ?")
@SQLDeleteAll(sql = "update yc_auth_group set is_deleted = null, updated_time = NOW() where id = ?")
@Where(clause = "is_deleted = 0")
public class AuthGroupEntity extends BaseEntity{

    @Column(name = "auth_group_name")
    private String authGroupName;

    @Column(name = "ent_id")
    private Long entId;

    @Column(name = "app_id")
    private Long appId;

    @Column(name = "type")
    private Integer type;

}
