package cn.teleinfo.idycprovince.infrastructure.db.entity;

import lombok.Getter;
import lombok.Setter;

import javax.persistence.Column;
import javax.persistence.Entity;
import javax.persistence.Id;
import javax.persistence.Table;

/**
 * 卷烟规格包装基本信息
 */
@Getter
@Setter
@Entity
@Table(name = "TZ_AC_CIGARETTE_SPECS_PACK")
public class CigaretteSpecsPackEntity {

    /**
     * 卷烟规格包装代码;卷烟规格包装代码编码规则为AC+“盒烟”、“条烟”、“件烟”类型编码，再选取“经营类型”+“卷烟规格”作为分类属性。“经营类型”标识：国内内销代码为0、国内出口代码为1、国外进口代码为2、国外来牌内销代码为3、国外来牌出口为4；“卷烟规格”国内内销烟取13位卷烟规格条码的第8位到第12位，进出口卷烟卷烟规格采用5位流水码重新编制。',
     */
    @Id
    @Column(name = "AC_CGT_PACK_CODE")
    private String packCode;

    /**
     * 卷烟代码;卷烟规格唯一的标识代码
     */
    @Column(name = "AC_CGT_CODE")
    private String code;



}
