package cn.teleinfo.idycprovince.infrastructure.redis;

import java.util.Date;
import java.util.List;
import java.util.Map;

public interface RedisCache {
    /**
     * Determine if given key exists.
     * Params:
     * key – must not be null.
     * Returns:
     *
     * See Also:
     * Redis Documentation: EXISTS
     * @param key
     * @return
     */
    Boolean hasKey(String key);

    String get(String key);

    void set(String key, String value);

    void set(String key, String value, int expiresInSeconds);

    boolean exists(String key);

    boolean delete(String key);

    boolean sAdd(String key, String value);

    List<String> sMembers(String key, Class clz);

    long increment(String key, int val);

    long hIncrement(String key, String field, long step);

    Map<String, Object> hEntries(String key);

    /**
     * Rename key {@code oldKey} to {@code newKey}.
     *
     * @param oldKey must not be {@literal null}.
     * @param newKey must not be {@literal null}.
     * @see <a href="https://redis.io/commands/rename">Redis Documentation: RENAME</a>
     */
    void rename(String oldKey, String newKey);

    void hPutAll(String key, Map<String, Object> map);

    void expireAt(String key, Date from);

    Long getTtl(String key);
}
