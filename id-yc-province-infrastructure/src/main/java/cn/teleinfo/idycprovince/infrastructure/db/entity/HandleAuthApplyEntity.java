package cn.teleinfo.idycprovince.infrastructure.db.entity;


import lombok.Data;
import org.hibernate.annotations.SQLDelete;
import org.hibernate.annotations.SQLDeleteAll;
import org.hibernate.annotations.Where;

import javax.persistence.Column;
import javax.persistence.Entity;
import javax.persistence.Table;

@Entity
@Data
@SQLDelete(sql = "update yc_handle_auth_apply set is_deleted = null, updated_time = NOW() where id = ? ")
@SQLDeleteAll(sql = "update yc_handle_auth_apply set is_deleted = null, updated_time = NOW() where id = ? ")
@Where(clause = "is_deleted = 0")
@Table(name = "yc_handle_auth_apply")
public class HandleAuthApplyEntity extends BaseEntity {

    /**
     * 申请人身份标识*
     */
    @Column(name = "apply_handle_user")
    private String applyHandleUser;

    /**
     * 标识名称*
     */
    @Column(name = "name")
    private String name;

    /**
     * 标识*
     */
    @Column(name = "handle")
    private String handle;

    /**
     * 实体类型：1业务实体 2资源实体*
     */
    @Column(name = "entity_type")
    private Integer entityType;

    /**
     * 申请状态：0待审核1审核通过2驳回*
     */
    @Column(name = "apply_state")
    private Integer applyState;

    /**
     * 企业id*
     */
    @Column(name = "ent_id")
    private Long entId;

    /**
     * 应用id*
     */
    @Column(name = "app_id")
    private Long appId;

    /**
     * 标识所属应用名称*
     */
    @Column(name = "app_name")
    private String appName;
}
