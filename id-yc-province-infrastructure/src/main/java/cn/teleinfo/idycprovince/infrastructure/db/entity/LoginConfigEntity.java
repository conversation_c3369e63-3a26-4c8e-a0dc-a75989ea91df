package cn.teleinfo.idycprovince.infrastructure.db.entity;

import lombok.Getter;
import lombok.Setter;
import org.hibernate.annotations.SQLDelete;
import org.hibernate.annotations.SQLDeleteAll;
import org.hibernate.annotations.Where;
import org.springframework.data.annotation.CreatedBy;
import org.springframework.data.annotation.CreatedDate;
import org.springframework.data.annotation.LastModifiedBy;
import org.springframework.data.annotation.LastModifiedDate;
import org.springframework.data.jpa.domain.support.AuditingEntityListener;

import javax.persistence.Column;
import javax.persistence.Entity;
import javax.persistence.EntityListeners;
import javax.persistence.GeneratedValue;
import javax.persistence.GenerationType;
import javax.persistence.Id;
import javax.persistence.Table;
import java.io.Serializable;
import java.time.LocalDateTime;

/**
 * <AUTHOR>
 */
@Getter
@Setter
@Entity
@Table(name = "yc_login_config")
@SQLDelete(sql = "update yc_login_config set is_deleted = null where id = ?")
@SQLDeleteAll(sql = "update yc_login_config set is_deleted = null where id = ?")
@Where(clause = "is_deleted = 0")
@EntityListeners(AuditingEntityListener.class)
public class LoginConfigEntity implements Serializable {

    @Id
    @GeneratedValue(strategy = GenerationType.IDENTITY)
    private Long id;

    /**
     * 创建时间
     */
    @CreatedDate
    @Column(name = "created_time")
    private LocalDateTime createdTime;

    /**
     * 创建人
     */
    @CreatedBy
    @Column(name = "created_by")
    private Long createdBy;

    /**
     * 更新时间
     */
    @LastModifiedDate
    @Column(name = "updated_time")
    private LocalDateTime updatedTime;

    /**
     * 更新人
     */
    @LastModifiedBy
    @Column(name = "updated_by")
    private Long updatedBy;

    /**
     * 逻辑删除: 0 未删除 null 已删除
     */
    @Column(name = "is_deleted")
    private Integer isDeleted = 0;
    
    /**
     * 双因子登录 0：开启；1：关闭
     */
    @Column(name = "double_factor_login")
    private Integer doubleFactorLogin;
    
    /**
     * 密码错误锁定 次
     */
    @Column(name = "password_err_lock_count")
    private Integer passwordErrLockCount;
    
    /**
     * 密码错误锁定时间 分钟
     */
    @Column(name = "password_err_lock_time")
    private Integer passwordErrLockTime;
    
    /**
     * 邮箱验证码错误锁定 次
     */
    @Column(name = "emial_err_lock_count")
    private Integer emialErrLockCount;
    
    /**
     * 邮箱验证码错误锁定 分钟
     */
    @Column(name = "emial_err_lock_time")
    private Integer emialErrLockTime;
    
    /**
     * 静止登出时间 分钟
     */
    @Column(name = "static_logout_time")
    private Integer staticLogoutTime;
    
    /**
     * 更换密码周期 天
     */
    @Column(name = "change_password_time")
    private Integer changePasswordTime;
    
    /**
     * 首次登录修改密码 0：开启；1：关闭
     */
    @Column(name = "first_login_change")
    private Integer firstLoginChange;
    
}
