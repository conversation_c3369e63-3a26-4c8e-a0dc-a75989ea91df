package cn.teleinfo.idycprovince.infrastructure.db.repository;

import cn.teleinfo.idycprovince.infrastructure.db.entity.AppCountEntity;
import cn.teleinfo.idycprovince.infrastructure.db.to.AppCountTO;
import org.springframework.data.jpa.repository.Query;
import org.springframework.data.repository.query.Param;

import java.util.List;

public interface AppCountRepository extends BaseRepository<AppCountEntity, Long> {

        /**
         * 统计应用接入数
         *
         * @return
         */
        @Query(nativeQuery = true, value = "SELECT " +
                        " yc_ent_prefix.ent_prefix AS entPrefix, " +
                        " yc_ent_prefix.id AS entPrefixID, " +
                        " CASE WHEN yc_ent_prefix.is_deleted IS  NULL THEN 0 ELSE ifnull( count( yc_app_info.id ), 0 ) END AS appNum  "
                        +
                        "FROM " +
                        " yc_ent_prefix " +
                        " LEFT JOIN yc_app_info ON yc_app_info.prefix_id = yc_ent_prefix.id  " +
                        " AND yc_app_info.is_deleted = 0  " +
                        "GROUP BY " +
                        " yc_ent_prefix.ent_prefix, " +
                        " yc_ent_prefix.id")
        List<AppCountTO> findAppCountByDate();

        /**
         * 查询前缀下的应用接入数量
         *
         * @return
         */
        @Query(nativeQuery = true, value = "SELECT ifnull(count(*), 0) AS appNum " +
                " FROM yc_ent_prefix " +
                " left join yc_app_info on yc_ent_prefix.id = yc_app_info.prefix_id " +
                " WHERE if(:prefixId != '' and :prefixId is not null, yc_ent_prefix.id = :prefixId, 1 = 1) " +
                "  AND yc_app_info.is_deleted = 0 " +
                "  and yc_ent_prefix.is_deleted = 0")
        Long findAppCountByPrefixId(@Param("prefixId") Long prefixId);

        /**
         * 根据上报日期查询应用接入数量
         *
         * @param statisticsDate
         * @return
         */
        List<AppCountEntity> findByStatisticsDate(String statisticsDate);

        AppCountEntity findByEntPrefixAndStatisticsDate(String entPrefix, String statisticsDate);

        /**
         * 查询当前账号全部接入应用统计数
         *
         * @param provinceId
         * @param entId
         * @param entPrefix
         * @return
         */
        @Query(nativeQuery = true, value = "SELECT " +
                        "ifnull( sum( yc_app_count.app_num ), 0 ) AS appNum " +
                        "FROM " +
                        "yc_app_count " +
                        "INNER JOIN (yc_ent_prefix) " +
                        "ON " +
                        "yc_app_count.ent_prefix = yc_ent_prefix.ent_prefix " +
                        "WHERE " +
                        "yc_ent_prefix.province_id = :provinceId " +
                        "AND  yc_ent_prefix.is_deleted = 0  " +
                        "AND  yc_app_count.version = 1 " +
                        "AND if(:entId != '' and :entId is not null,yc_ent_prefix.ent_id = :entId, 1=1 ) " +
                        "AND if(:entPrefix != '' and :entPrefix is not null,yc_ent_prefix.ent_prefix = :entPrefix, 1=1 )")
        AppCountTO findTotalAppCountByProvinceIdAndEntIdAndEntPrefixToEnt(@Param("provinceId") Long provinceId,
                        @Param("entId") Long entId, @Param("entPrefix") String entPrefix);

        /**
         * 查询当前账号全部接入应用统计数
         *
         * @param provinceId
         * @param entId
         * @param entPrefix
         * @return
         */
        @Query(nativeQuery = true, value = "SELECT " +
                        "ifnull( sum( yc_app_count.app_num ), 0 ) AS appNum " +
                        "FROM " +
                        "yc_app_count " +
                        "INNER JOIN (yc_ent_prefix) " +
                        "ON " +
                        "yc_app_count.ent_prefix = yc_ent_prefix.ent_prefix " +
                        "WHERE " +
                        "yc_ent_prefix.province_id = :provinceId " +
                        "AND yc_ent_prefix.is_deleted = 0 " +
                        "AND if(:entId != '' and :entId is not null,yc_ent_prefix.ent_id = :entId, 1=1 ) " +
                        "AND if(:entPrefix != '' and :entPrefix is not null,yc_ent_prefix.ent_prefix = :entPrefix, 1=1 ) "
                        +
                        "AND (" +
                        "  (yc_app_count.version = 1 AND EXISTS (SELECT 1 FROM yc_app_count ac2 WHERE ac2.ent_prefix = yc_app_count.ent_prefix AND ac2.version = 1)) OR "
                        +
                        "  (yc_app_count.version IS NULL AND NOT EXISTS (SELECT 1 FROM yc_app_count ac2 WHERE ac2.ent_prefix = yc_app_count.ent_prefix AND ac2.version = 1))"
                        +
                        ")")
        AppCountTO findTotalAppCountByProvinceIdAndEntIdAndEntPrefixToProvince(@Param("provinceId") Long provinceId,
                        @Param("entId") Long entId, @Param("entPrefix") String entPrefix);

        AppCountEntity findByEntPrefixAndVersion(String entPrefix, long l);
}
