package cn.teleinfo.idycprovince.infrastructure.db.entity;

import lombok.Data;
import org.hibernate.annotations.SQLDelete;
import org.hibernate.annotations.SQLDeleteAll;
import org.hibernate.annotations.Where;
import org.springframework.data.annotation.LastModifiedBy;
import org.springframework.data.annotation.LastModifiedDate;
import org.springframework.data.jpa.domain.support.AuditingEntityListener;

import javax.persistence.Column;
import javax.persistence.Entity;
import javax.persistence.EntityListeners;
import javax.persistence.GeneratedValue;
import javax.persistence.GenerationType;
import javax.persistence.Id;
import javax.persistence.Table;
import java.io.Serializable;
import java.time.LocalDateTime;

/**
 * <AUTHOR>
 */
@Entity
@Data
@SQLDelete(sql = "update yc_report_handle set is_deleted = null, updated_time = NOW()  where id = ?")
@SQLDeleteAll(sql = "update yc_report_handle set is_deleted = null, updated_time = NOW()  where id = ?")
@Where(clause = "is_deleted = 0")
@Table(name = "yc_report_handle")
@EntityListeners(AuditingEntityListener.class)
public class ReportHandleEntity implements Serializable {
    
    @Id
    @GeneratedValue(strategy = GenerationType.IDENTITY)
    private Long id;

    /**
    *  企业前缀
    */
    @Column(name = "ent_prefix")
    private String entPrefix;

    /**
    *  通配符
    */
    @Column(name = "wildcard")
    private String wildcard;

    /**
    *  标识名称
    */
    @Column(name = "name")
    private String name;

    /**
    *  标识
    */
    @Column(name = "handle")
    private String handle;

    /**
    *  实体类型 1业务实体 2资源实体
    */
    @Column(name = "entity_type")
    private Integer entityType;

    /**
    *  所属应用名称
    */
    @Column(name = "app_name")
    private String appName;

    /**
    *  所属应用标识编码
    */
    @Column(name = "app_handle_code")
    private String appHandleCode;

    /**
    *  企业名称
    */
    @Column(name = "org_name")
    private String orgName;
    
    /**
     *  上报人
     */
    @Column(name = "report_name")
    private String reportName;
    
    /**
     * 创建时间
     */
    @Column(name = "created_time")
    private LocalDateTime createdTime;
    
    /**
     * 创建人
     */
    @Column(name = "created_by")
    private Long createdBy;
    
    /**
     * 更新时间
     */
    @LastModifiedDate
    @Column(name = "updated_time")
    private LocalDateTime updatedTime;
    
    /**
     * 更新人
     */
    @LastModifiedBy
    @Column(name = "updated_by")
    private Long updatedBy;
    
    /**
     * 逻辑删除: 0 未删除 null 已删除
     */
    @Column(name = "is_deleted")
    private Integer isDeleted = 0;
    
    /**
     * 省级租户id
     */
    @Column(name = "province_id")
    private Long provinceId;

    /**
     * 授权属性标识操作人
     */
    @Column(name = "auth_by")
    private Long authBy;

    /**
     * 授权属性标识操作时间
     */
    @Column(name = "auth_time")
    private LocalDateTime authTime;

    /**
     * 实体对象id
     */
    @Column(name = "entity_object_id")
    private String entityObjectId;
    /**
     * 实体对象类型id
     */
    @Column(name = "entity_object_type_id")
    private String entityObjectTypeId;
    /**
     * 标识类型
     */
    @Column(name = "handle_type")
    private Integer handleType;
    /**
     * 标识状态
     */
    @Column(name = "handle_status")
    private Integer handleStatus;
    /**
     * 注册状态
     */
    @Column(name = "register_status")
    private Integer registerStatus;

    /**
     * 主数据类型*
     */
    @Column(name = "master_data_type")
    private Integer masterDataType;

    /**
     * 父级id*
     */
    @Column(name = "parent_id")
    private Long parentId;

    @Column(name = "data_type")
    private Integer dataType;


}