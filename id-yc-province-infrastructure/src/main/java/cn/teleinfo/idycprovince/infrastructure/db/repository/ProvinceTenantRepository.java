package cn.teleinfo.idycprovince.infrastructure.db.repository;

import cn.teleinfo.idycprovince.infrastructure.db.entity.ProvinceTenantEntity;
import org.springframework.data.jpa.repository.Query;

import java.util.List;

/***
 * @title HandleMaintainRepository
 * @description <TODO description class purpose>
 * <AUTHOR>
 * @version 1.0.0
 * @create 2023/3/13 15:48
 **/
public interface ProvinceTenantRepository extends BaseRepository<ProvinceTenantEntity,Long> {


    List<ProvinceTenantEntity> findAllByIdNot(Long id);
    Boolean existsAllByPrefix(String prefix);
    Boolean existsAllByBizCode(String bizCode);

    ProvinceTenantEntity findByPrefix(String prefix);


    @Query(value = "SELECT * FROM yc_province_tenant",nativeQuery = true)
    List<ProvinceTenantEntity> findProvinceTenantEntities();
}
