package cn.teleinfo.idycprovince.infrastructure.db.repository;


import cn.teleinfo.idycprovince.infrastructure.db.entity.SysDictPO;
import org.springframework.data.jpa.repository.Query;
import org.springframework.data.repository.query.Param;

import java.util.List;

/**
 * @description: 字典
 * @author: liuyan
 * @create: 2022−11-05 9:51 PM
 */
public interface SysDictRepository extends BaseRepository<SysDictPO, Long> {


    /**
     * 根据KEY查询
     *
     * @param key
     * @return
     */
    List<SysDictPO> findByDictKey(String key);

    /**
     * 根据parentId查询
     *
     * @param parentId
     * @return
     */
    List<SysDictPO> findByParentId(Long parentId);

    /**
     * 根据key，code查询value
     * @param key
     * @param code
     * @return
     */
    @Query("SELECT dictValue FROM SysDictPO WHERE dictKey= :key and dictCode = :code")
    String getDictValue(@Param("key") String key, @Param("code") String code);

}
