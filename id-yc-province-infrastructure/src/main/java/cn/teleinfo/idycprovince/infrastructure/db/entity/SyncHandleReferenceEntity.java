package cn.teleinfo.idycprovince.infrastructure.db.entity;

import lombok.Getter;
import lombok.Setter;
import org.hibernate.annotations.BatchSize;
import org.hibernate.annotations.SQLDelete;
import org.hibernate.annotations.SQLDeleteAll;
import org.hibernate.annotations.Where;

import javax.persistence.Column;
import javax.persistence.Entity;
import javax.persistence.Id;
import javax.persistence.Table;
import java.time.LocalDateTime;


@Getter
@Setter
@Entity
@Table(name = "yc_integrated_handle_reference")

public class SyncHandleReferenceEntity {

    @Id
    @Column(name = "id")
    private Long id;

    @Column(name = "source_id")
    private Long sourceId;


    @Column(name = "created_time")
    private LocalDateTime createdTime;

    @Column(name = "updated_time")
    private LocalDateTime updatedTime;

    @Column(name = "reference_handle")
    private String referenceHandle;

    @Column(name = "reference_handle_prop")
    private String referenceHandleProp;

    @Column(name = "reference_handle_prop_index")
    private Integer referenceHandlePropIndex;

    @Column(name = "query_prop")
    private String queryProp;

    @Column(name = "query_prop_index")
    private Integer queryPropIndex;

    @Column(name = "param_prop")
    private String paramProp;

    @Column(name = "param_prop_index")
    private Integer paramPropIndex;

    @Column(name = "handle_item_id")
    private Long handleItemId;

    @Column(name = "province_prefix")
    private String provincePrefix;

    @Column(name = "ent_prefix")
    private String entPrefix;

    @Column(name = "app_handle_code")
    private String appHandleCode;

    @Column(name = "is_deleted")
    private Integer isDeleted;
}
