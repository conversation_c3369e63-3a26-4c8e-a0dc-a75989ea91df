package cn.teleinfo.idycprovince.infrastructure.db.entity;

import lombok.Data;
import org.hibernate.annotations.SQLDelete;
import org.hibernate.annotations.SQLDeleteAll;
import org.hibernate.annotations.Where;

import javax.persistence.Column;
import javax.persistence.Entity;
import javax.persistence.Table;
import java.io.Serializable;

/**
 * <AUTHOR>
 */
@Entity
@Data
@SQLDelete(sql = "update yc_handle_item set is_deleted = null, updated_time = NOW()  where id = ?")
@SQLDeleteAll(sql = "update yc_handle_item set is_deleted = null, updated_time = NOW()  where id = ?")
@Where(clause = "is_deleted = 0")
@Table(name = "yc_handle_item")
public class HandleItemEntity extends BaseEntity implements Serializable {

    /**
    *  索引
    */
    @Column(name = "field_index")
    private Integer fieldIndex;

    /**
    *  字段名称
    */
    @Column(name = "field")
    private String field;

    /**
    *  字段描述
    */
    @Column(name = "description")
    private String description;

    /**
    *  属性类型 1固定值 2标识解析数据源 3标识值 4标识-属性
    */
    @Column(name = "field_type")
    private Integer fieldType;

    /**
    *  管理员写 0 不可写 1可写
    */
    @Column(name = "admin_write")
    private Integer adminWrite;

    /**
    *  管理员读 0不可读 1可读
    */
    @Column(name = "admin_read")
    private Integer adminRead;

    /**
    *  公共读 0 不可读 1 可读
    */
    @Column(name = "public_read")
    private Integer publicRead;

    /**
    *  公共写 0 不可写 1可写
    */
    @Column(name = "public_write")
    private Integer publicWrite;

    /**
    *  标识ID
    */
    @Column(name = "handle_id")
    private Long handleId;

    /**
    *  属性值
    */
    @Column(name = "field_value")
    private String fieldValue;

    /**
    *  数据服务ID
    */
    @Column(name = "data_service_id")
    private Long dataServiceId;

    /**
    *  数据源ID
    */
    @Column(name = "data_source_id")
    private Long dataSourceId;

    /**
    *  数据源名称
    */
    @Column(name = "data_source_name")
    private String dataSourceName;

    /**
    *  企业ID
    */
    @Column(name = "ent_id")
    private Long entId;
    
    /**
     *  属性来源类型 0基础属性 1扩展属性
     */
    @Column(name = "field_source_type")
    private Integer fieldSourceType;
    
    /**
     *  应用标识编码
     */
    @Column(name = "app_handle_code")
    private String appHandleCode;
    
    /**
     *  备注
     */
    @Column(name = "remark")
    private String remark;

    /**
     * 数据通道类型：1基本类型2数组类型*
     */
    @Column(name = "data_channel_type")
    private Integer dataChannelType;

    @Column(name = "auth_user")
    private String authUser;

    /**
     *  属性编码
     */
    @Column(name = "entity_object_field_code")
    private String entityObjectFieldCode;

    @Column(name = "table_name")
    private String tableName;

    @Column(name = "column_name")
    private String columnName;

    @Column(name = "database_name")
    private String databaseName;

    @Column(name = "database_ip")
    private String databaseIp;


//    @Column(name = "auth_group_ids")
//    private String authGroupIds;


}