package cn.teleinfo.idycprovince.infrastructure.db.entity;

import lombok.Getter;
import lombok.Setter;
import lombok.experimental.Accessors;

import javax.persistence.*;

@Getter
@Setter
@Entity
@Accessors(chain = true)
@Table(name = "yc_province_manage")
public class ProvinceManageEntity extends BaseEntity{

    /**
    *  运营公司名称
    */
    @Column(name = "manage_org_name")
    private String manageOrgName;

    /**
    *  运营公司性质
    */
    @Column(name = "manage_org_nature")
    private String manageOrgNature;

    /**
    *  组织机构代码
    */
    @Column(name = "manage_org_crt_code")
    private String manageOrgCrtCode;

    /**
    *  服务行业
    */
    @Column(name = "manage_org_industry")
    private String manageOrgIndustry;

    /**
    *  官方网址
    */
    @Column(name = "manage_website")
    private String manageWebsite;

    /**
    *  单位简介
    */
    @Column(name = "manage_org_desc")
    private String manageOrgDesc;

    /**
    *  单位所在省
    */
    @Column(name = "manage_org_province")
    private String manageOrgProvince;

    /**
    *  单位所在市
    */
    @Column(name = "manage_org_city")
    private String manageOrgCity;

    /**
    *  单位地址
    */
    @Column(name = "manage_org_addr")
    private String manageOrgAddr;

    /**
     * 通讯地址*
     */
    @Column(name = "manage_contact_addr")
    private String manageContactAddr;


}