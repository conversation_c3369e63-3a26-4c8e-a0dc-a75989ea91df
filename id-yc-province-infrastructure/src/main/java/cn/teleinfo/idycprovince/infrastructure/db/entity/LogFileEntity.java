package cn.teleinfo.idycprovince.infrastructure.db.entity;

import java.io.Serializable;
import java.time.LocalDateTime;

import lombok.Getter;
import lombok.Setter;
import org.springframework.data.annotation.CreatedDate;
import org.springframework.data.annotation.LastModifiedDate;
import org.springframework.data.jpa.domain.support.AuditingEntityListener;

import javax.persistence.*;

@Getter
@Setter
@Entity
@Table(name = "yc_log_file")
@EntityListeners(AuditingEntityListener.class)
public class LogFileEntity implements Serializable {
    
    @Id
    @GeneratedValue(strategy = GenerationType.IDENTITY)
    private Long id;

    /**
    *  创建时间
    */
    @CreatedDate
    @Column(name = "created_time")
    private LocalDateTime createdTime;

    /**
    *  更新时间
    */
    @LastModifiedDate
    @Column(name = "updated_time")
    private LocalDateTime updatedTime;

    /**
    *  文件名
    */
    @Column(name = "file_name")
    private String fileName;

    /**
    *  文件状态(1.已上传，2:已读;3:已删除)
    */
    @Column(name = "file_state")
    private Integer fileState;


}