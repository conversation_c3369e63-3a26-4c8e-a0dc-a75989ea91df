package cn.teleinfo.idycprovince.infrastructure.db.entity;

import lombok.Getter;
import lombok.Setter;

import javax.persistence.Column;
import javax.persistence.Entity;
import javax.persistence.Table;

/**
 * 字典明细表
 *
 * <AUTHOR>
 */
@Getter
@Setter
@Entity
@Table(name = "sys_dict_item")
public class SysDictItemEntity extends BaseEntity{

    /**
    *  字典ID
    */
    @Column(name = "dict_id")
    private Long dictId;

    /**
    *  字典类型
    */
    @Column(name = "dict_type")
    private String dictType;

    /**
    *  字典值
    */
    @Column(name = "dict_key")
    private String dictKey;

    /**
    *  字典名称
    */
    @Column(name = "dict_value")
    private String dictValue;

    /**
    *  排序
    */
    @Column(name = "sort")
    private Integer sort;

}