package cn.teleinfo.idycprovince.infrastructure.db.entity;

import lombok.Data;
import org.hibernate.annotations.SQLDelete;
import org.hibernate.annotations.SQLDeleteAll;

import javax.persistence.Column;
import javax.persistence.Entity;
import javax.persistence.Id;
import javax.persistence.Table;
import java.time.LocalDateTime;

@Data
@Entity
@Table(name = "yc_integrated_ent_prefix")
@SQLDelete(sql = "update yc_integrated_ent_prefix set is_deleted = null, updated_time = NOW()  where id = ?")
@SQLDeleteAll(sql = "update yc_integrated_ent_prefix set is_deleted = null, updated_time = NOW()  where id = ?")
public class SyncEntPrefixEntity {

    @Id
    @Column(name = "id")
    private Long id;

    @Column(name = "source_id")
    private Long sourceId;

    @Column(name = "created_time")
    private LocalDateTime createdTime;

    @Column(name = "updated_time")
    private LocalDateTime updatedTime;

    @Column(name = "ent_prefix")
    private String entPrefix;

    @Column(name = "state")
    private Integer state;

    @Column(name = "ent_id")
    private Long entId;

    @Column(name = "is_deleted")
    private Integer isDeleted;

}
