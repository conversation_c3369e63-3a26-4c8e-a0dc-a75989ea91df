package cn.teleinfo.idycprovince.infrastructure.db.entity;


import lombok.Data;
import lombok.EqualsAndHashCode;
import lombok.ToString;
import lombok.experimental.Accessors;

import javax.persistence.*;
import java.time.LocalDateTime;

/**
 * 定时任务 DO
 *
 * <AUTHOR>
 */
@Table(name = "yc_job")
@Data
@Entity
@ToString(callSuper = true)
@Accessors(chain = true)
public class JobDO  {

	/**
	 * 任务编号
	 */
	@Id
	@GeneratedValue(strategy = GenerationType.IDENTITY)
	private Long id;
	/**
	 * 任务名称
	 */
	private String name;
	/**
	 * 任务状态
	 * <p>
	 */
	private Integer status;

	/**
	 * 任务唯一id
	 * 为空时则使用 handlerName 作为id。
	 */
	private String identity;

	/**
	 * 处理器的名字
	 */
	private String handlerName;
	/**
	 * 处理器的参数
	 */
	private String handlerParam;
	/**
	 * CRON 表达式
	 */
	private String cronExpression;

	/**
	 * 重试次数
	 * <p>
	 * 如果不重试，则设置为 0
	 */
	private Integer retryCount;
	/**
	 * 重试间隔，单位：毫秒
	 * <p>
	 * 如果没有间隔，则设置为 0
	 */
	private Integer retryInterval;
	/**
	 * 监控超时时间，单位：毫秒
	 * <p>
	 * 如果不监控超时，则设置为 0
	 */
	private Integer monitorTimeout;

	/**
	 * 创建时间
	 */
	private LocalDateTime createTime;
	/**
	 * 最后更新时间
	 */
	private LocalDateTime updateTime;
	/**
	 * 创建者，目前使用 SysUser 的 id 编号
	 */
	private String creator;
	/**
	 * 更新者，目前使用 SysUser 的 id 编号
	 */
	private String updater;
	/**
	 * 是否删除
	 */
	private Boolean deleted;

} 