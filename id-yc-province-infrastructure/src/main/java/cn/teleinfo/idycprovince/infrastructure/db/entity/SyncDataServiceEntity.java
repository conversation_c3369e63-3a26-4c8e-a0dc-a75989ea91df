package cn.teleinfo.idycprovince.infrastructure.db.entity;

import lombok.Data;

import javax.persistence.*;
import java.time.LocalDateTime;

@Entity
@Data
@Table(name = "yc_integrated_data_service")
public class SyncDataServiceEntity {

    @Id
    @Column(name = "id")
    private Long id;

    @Column(name = "source_id")
    private Long sourceId;

    @Column(name = "created_time")
    private LocalDateTime createdTime;

    @Column(name = "created_by")
    private Long createdBy;

    @Column(name = "updated_time")
    private LocalDateTime updatedTime;

    @Column(name = "updated_by")
    private Long updatedBy;

    @Column(name = "province_prefix")
    private String provincePrefix;

    @Column(name = "ent_prefix")
    private String entPrefix;

    @Column(name = "app_handle_code")
    private String appHandleCode;

    @Column(name = "data_service_name", length = 20)
    private String dataServiceName;

    @Column(name = "service_address", length = 255)
    private String serviceAddress;

    @Column(name = "service_token", length = 50)
    private String serviceToken;

    @Column(name = "is_deleted")
    private Integer isDeleted = 0;

    @Column(name = "data_service_uuid", length = 64)
    private String dataServiceUuid;

    @Column(name = "data_service_type")
    private Integer dataServiceType;

    @Column(name = "version", length = 32)
    private String version;

    @Column(name = "report_time")
    private LocalDateTime reportTime;

}