package cn.teleinfo.idycprovince.infrastructure.db.entity;

import lombok.Data;

import javax.persistence.*;
import java.io.Serializable;
import java.util.Date;

/**
 * @description: 日志对象
 * @author: liuyan
 * @create: 2022−11-07 9:39 PM
 */
@Entity
@Table(name = "yc_task")
@Data
public class TaskEntity implements Serializable {
    /**
     * 自增id
     */
    @Id
    @GeneratedValue(strategy = GenerationType.IDENTITY)
    private Long id;

    /**
     * 任务名称
     */
    @Column(name = "name")
    private String name;

//    private String className;
//
//    private String methodName;

    /**
     * 任务创建人员
     */
    @Column(name = "nickname")
    private String nickName;

    /**
     * 操作员id
     */
    @Column(name = "user_id")
    private Integer userId;

    /**
     * 任务创建时间
     */
//    @TableField(fill = FieldFill.INSERT)
//    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss", timezone = "GMT+8")
    @Column(name = "create_time")
    private Date createTime;

    /**
     * 时间类型1具体时间 2为cron表达式周期时间
     */
    @Column(name = "time_type")
    private Integer timeType;

    /**
     * 任务执行时间
     */
    @Column(name = "time")
    private String time;

    /**
     * jobGropName
     */
    @Column(name = "job_grop_name")
    private String jobGropName;

    /**
     * 参数
     */
    @Column(name = "parameter")
    private String parameter;

    /**
     * jobName
     */
    @Column(name = "job_name")
    private String jobName;

    /**
     * 备注
     */
    @Column(name = "remark")
    private String remark;

    /**
     * 命令类型
     */
    @Column(name = "type")
    private String type;

    /**
     * 是否删除
     */
    @Column(name = "isdelete")
    private Byte isDelete;

    /**
     * 1开始 2为暂停 3已执行
     */
    @Column(name = "tast_state")
    private Integer tastState;

    private static final long serialVersionUID = 1L;
}