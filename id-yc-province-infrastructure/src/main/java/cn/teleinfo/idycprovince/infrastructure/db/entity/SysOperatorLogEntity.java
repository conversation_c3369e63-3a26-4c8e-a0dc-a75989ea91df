package cn.teleinfo.idycprovince.infrastructure.db.entity;

import lombok.Getter;
import lombok.Setter;
import lombok.experimental.Accessors;

import javax.persistence.Column;
import javax.persistence.Entity;
import javax.persistence.Table;

/**
 * @description: 系统操作日志
 * @author: liuyan
 * @create: 2022−11-04 5:44 PM
 */
@Getter
@Setter
@Accessors(chain = true)
@Entity
@Table(name = "yc_sys_operator_log")
public class SysOperatorLogEntity extends BaseEntity {


    /**
     * 企业租户id
     */
    @Column(name = "ent_id")
    private Long entId;

    /**
     * 请求方式
     */
    @Column(name = "method")
    private String method;
    /**
     * 方法名
     */
    @Column(name = "path")
    private String path;


    /**
     * 请求ip
     */
    @Column(name = "host")
    private String host;
    /**
     * 请求参数
     */
    @Column(name = "param")
    private String param;
    /**
     * 响应码
     */
    @Column(name = "response_code")
    private String responseCode;

    /**
     * 描述
     */
    @Column(name = "description")
    private String description;

}
