package cn.teleinfo.idycprovince.infrastructure.db.view;

import java.time.LocalDateTime;

public interface JobView {
    String getId();
    String getName();
    Integer getStatus();
    String getIdentity();
    String getHandlerName();
    String getHandlerParam();
    String getCronExpression();
    Integer getRetryCount();
    Integer getRetryInterval();
    Integer getMonitorTimeout();
    LocalDateTime getCreateTime();
    LocalDateTime getUpdateTime();
    String getCreator();
    String getUpdater();
    Boolean getDeleted();
}