package cn.teleinfo.idycprovince.infrastructure.db.repository;

import cn.teleinfo.idycprovince.infrastructure.db.entity.ObjectHandleMaintainItemEntity;
import org.springframework.transaction.annotation.Transactional;

import java.util.List;

public interface ObjectHandleMaintainItemRepository extends BaseRepository<ObjectHandleMaintainItemEntity,Long> {


    List<ObjectHandleMaintainItemEntity> findByMaintainIdAndHandleAndCreatedBy(Long maintainId,String handle,Long createdBy);

    @Transactional
    void deleteByMaintainId(Long objectHandleId);


    List<ObjectHandleMaintainItemEntity>  findByHandleAndCreatedByNot(String handle,Long createdBy);
}
