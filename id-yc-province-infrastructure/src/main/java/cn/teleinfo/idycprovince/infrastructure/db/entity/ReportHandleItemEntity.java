package cn.teleinfo.idycprovince.infrastructure.db.entity;

import lombok.Data;
import org.hibernate.annotations.SQLDelete;
import org.hibernate.annotations.SQLDeleteAll;
import org.hibernate.annotations.Where;
import org.springframework.data.jpa.domain.support.AuditingEntityListener;

import javax.persistence.Column;
import javax.persistence.Entity;
import javax.persistence.EntityListeners;
import javax.persistence.Table;
import java.io.Serializable;

@Entity
@Data
@SQLDelete(sql = "update yc_report_handle_item set is_deleted = null, updated_time = NOW()  where id = ?")
@SQLDeleteAll(sql = "update yc_report_handle_item set is_deleted = null, updated_time = NOW()  where id = ?")
@Where(clause = "is_deleted = 0")
@Table(name = "yc_report_handle_item")
@EntityListeners(AuditingEntityListener.class)
public class ReportHandleItemEntity extends BaseEntity implements Serializable {

    /**
    *  索引
    */
    @Column(name = "field_index")
    private Integer fieldIndex;

    /**
    *  字段名称
    */
    @Column(name = "field")
    private String field;

    /**
    *  字段描述
    */
    @Column(name = "description")
    private String description;

    /**
    *  属性类型 1固定值 2标识解析数据源 3标识值 4标识-属性
    */
    @Column(name = "field_type")
    private Integer fieldType;

    /**
    *  管理员写 0 不可写 1可写
    */
    @Column(name = "admin_write")
    private Integer adminWrite;

    /**
    *  管理员读 0不可读 1可读
    */
    @Column(name = "admin_read")
    private Integer adminRead;

    /**
    *  公共读 0 不可读 1 可读
    */
    @Column(name = "public_read")
    private Integer publicRead;

    /**
    *  公共写 0 不可写 1可写
    */
    @Column(name = "public_write")
    private Integer publicWrite;

    /**
    *  标识ID
    */
    @Column(name = "handle_id")
    private Long handleId;

    /**
    *  属性值
    */
    @Column(name = "field_value")
    private String fieldValue;

    /**
    *  数据服务名称
    */
    @Column(name = "data_service_name")
    private String dataServiceName;

    /**
    *  数据源名称
    */
    @Column(name = "data_source_name")
    private String dataSourceName;
    
    /**
     *  属性来源类型 0基础属性 1扩展属性
     */
    @Column(name = "field_source_type")
    private Integer fieldSourceType;
    
    /**
     *  备注
     */
    @Column(name = "remark")
    private String remark;



    @Column(name = "auth_user")
    private String authUser;

    /**
     *  属性编码
     */
    @Column(name = "entity_object_field_code")
    private String entityObjectFieldCode;

    @Column(name = "table_name")
    private String tableName;

    @Column(name = "column_name")
    private String columnName;

    @Column(name = "database_name")
    private String databaseName;

    @Column(name = "database_ip")
    private String databaseIp;
}