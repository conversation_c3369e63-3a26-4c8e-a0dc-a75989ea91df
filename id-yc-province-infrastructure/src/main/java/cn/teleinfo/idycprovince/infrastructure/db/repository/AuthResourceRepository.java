package cn.teleinfo.idycprovince.infrastructure.db.repository;

import cn.teleinfo.idycprovince.infrastructure.db.entity.AuthResourceEntity;

import java.util.Collection;
import java.util.List;

public interface AuthResourceRepository extends BaseRepository<AuthResourceEntity, Long> {

    AuthResourceEntity findAuthResourceEntityByAuthId(Long authId);
    
    List<AuthResourceEntity> findAuthResourceEntitiesByAuthIdIn(Collection<Long> auths);
}
