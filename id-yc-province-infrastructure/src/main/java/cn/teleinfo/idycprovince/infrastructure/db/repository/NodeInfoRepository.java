package cn.teleinfo.idycprovince.infrastructure.db.repository;

import cn.teleinfo.idycprovince.infrastructure.db.entity.NodeInfoEntity;
import org.springframework.data.jpa.repository.Query;
import org.springframework.data.repository.query.Param;

/**
 * <AUTHOR>
 * @version 1.0
 * @date Created in 2022/11/5
 * @description 节点信息Repository
 */
public interface NodeInfoRepository extends BaseRepository<NodeInfoEntity,Long> {

    /**
     * 根据企业Id查询
     * @param entId
     * @return
     */
    NodeInfoEntity findByEntId(Long entId);

    /**
     * 校验是否是二级企业前缀
     * @param provinceId
     * @return
     */
    NodeInfoEntity findByProvinceIdAndEntId(Long provinceId ,Long entId);

    /**
     * 根据企业Id和省Id查询节点信息
     * @param provinceId
     * @param entId
     * @return
     */
    @Query(nativeQuery = true, value =
            "SELECT * " +
            "FROM " +
            "yc_node_info " +
            "WHERE " +
            "yc_node_info.province_id = :provinceId " +
            "AND if(:entId != '' and :entId is not null,yc_node_info.ent_id = :entId, 1=1 )")
    NodeInfoEntity findQueryByProvinceIdAndEntId(@Param("provinceId") Long provinceId, @Param("entId") Long entId);

}
