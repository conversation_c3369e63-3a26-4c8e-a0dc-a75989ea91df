package cn.teleinfo.idycprovince.infrastructure.db.entity;

import lombok.Getter;
import lombok.Setter;
import org.hibernate.annotations.SQLDelete;
import org.hibernate.annotations.SQLDeleteAll;
import org.hibernate.annotations.Where;

import javax.persistence.Column;
import javax.persistence.Entity;
import javax.persistence.Table;

@Getter
@Setter
@Entity
@Table(name = "yc_app_info")
@SQLDelete(sql = "update yc_app_info set is_deleted = null, updated_time = NOW() where id = ?")
@SQLDeleteAll(sql = "update yc_app_info set is_deleted = null, updated_time = NOW() where id = ?")
@Where(clause = "is_deleted = 0")
public class AppInfoEntity extends BaseEntity{

    private static final long serialVersionUID = 1L;

    /** 应用名称 */
    @Column(name = "app_name")
    private String appName ;
    /** 标识编码 */
    @Column(name = "handle_code")
    private String handleCode ;
    /** 部署地址 */
    @Column(name = "deploy_address")
    private String deployAddress ;
    /** 系统版本 */
    @Column(name = "sys_version")
    private String sysVersion ;
    /** 所属企业 */
    @Column(name = "ent_id")
    private Long entId ;
    /** 前缀id */
    @Column(name = "prefix_id")
    private Long prefixId;
    /**
     * 数据中台应用id
     */
    @Column(name = "mp_dmm_app_id")
    private String mpDmmAppId;
    /**
     * 应用类型:1-中台应用，2非中台应用
     */
    @Column(name = "app_type")
    private int appType;
    /**
     * 主数据范围:字典值:1-盒烟，2-条烟，3-件烟，4-原烟，5-成品烟，6-丝束，7-滤棒
     */
    @Column(name = "master_data_scope")
    private String masterDataScope;

    /**
     * 公钥
     */
    @Column(name = "public_key")
    private String publicKey;

    /**
     * 是否开启审核 0 未开启 1 开启
     */
    @Column(name = "is_audit")
    private Integer isAudit;

}
