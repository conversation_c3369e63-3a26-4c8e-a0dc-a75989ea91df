package cn.teleinfo.idycprovince.infrastructure.db.repository;

import cn.teleinfo.idycprovince.infrastructure.db.entity.HandleItemAuthGroupEntity;
import cn.teleinfo.idycprovince.infrastructure.db.view.AuthGroupView;
import org.springframework.data.jpa.repository.Query;
import org.springframework.data.repository.query.Param;

import java.util.List;

public interface HandleItemAuthGroupRepository extends BaseRepository<HandleItemAuthGroupEntity, Long>{
    void deleteAllByAuthGroupId(Long authGroupId);

    void deleteAllByHandleId(Long handleId);

    void deleteAllByAuthGroupIdAndHandleId(Long authGroupId, Long handleId);

    List<HandleItemAuthGroupEntity> findAllByAuthGroupIdAndHandleId(Long authGroupId, Long handleId);

    HandleItemAuthGroupEntity findByHandleIdAndAuthGroupIdAndItemId(Long handleId, Long authGroupId, Long itemId);

    List<HandleItemAuthGroupEntity> findAllByHandleIdAndItemId(Long handleId, Long id);

    void deleteAllByItemIdAndHandleId(Long itemId, Long handleId);

    @Query(nativeQuery = true,value = "SELECT " +
            " a.item_id AS itemId, " +
            " b.id AS groupId, " +
            " b.auth_group_name AS groupName, " +
            " b.type AS groupType  " +
            " FROM " +
            " yc_handle_item_auth_group a " +
            " LEFT JOIN yc_auth_group b ON a.auth_group_id = b.id  " +
            " WHERE " +
            " a.is_deleted = 0  " +
            " AND b.is_deleted = 0  " +
            " AND a.handle_id = :handleId  " +
            " AND a.item_id IN (:itemIds)")
    List<AuthGroupView> getAuthgroupList(@Param("handleId") Long handleId, @Param("itemIds") List<Long> itemIds);
}
