package cn.teleinfo.idycprovince.infrastructure.db.entity;

import lombok.Data;
import org.hibernate.annotations.SQLDelete;
import org.hibernate.annotations.SQLDeleteAll;
import org.hibernate.annotations.Where;

import javax.persistence.Column;
import javax.persistence.Entity;
import javax.persistence.Table;

/**
 *
 *
 * @Author: liusp
 * @Date: 2023/10/25/18:18
 * @Description:
 */
@Entity
@Data
@SQLDelete(sql = "update yc_metadata_column set is_deleted = null, updated_time = NOW()  where id = ?")
@SQLDeleteAll(sql = "update yc_metadata_column set is_deleted = null, updated_time = NOW()  where id = ?")
@Where(clause = "is_deleted = 0")
@Table(name = "yc_metadata_column")
public class MetaDataColumnEntity extends BaseEntity{
    /**
     * 企业ID
     */
    @Column(name = "ent_id")
    private Long entId;
    /**
     * 应用ID
     */
    @Column(name = "app_id")
    private Long appId;
    /** 数据库表ID */
    @Column(name = "table_id")
    private Long tableId ;
    /** 字段名 */
    @Column(name = "column_name")
    private String columnName ;
    /** 字段类型 */
    @Column(name = "column_type")
    private String columnType ;
    /** 描述 */
    @Column(name = "description")
    private String description ;
}
