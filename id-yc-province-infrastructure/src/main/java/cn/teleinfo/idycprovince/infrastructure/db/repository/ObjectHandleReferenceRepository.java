package cn.teleinfo.idycprovince.infrastructure.db.repository;

import cn.teleinfo.idycprovince.infrastructure.db.entity.ObjectHandleReferenceEntity;
import org.springframework.transaction.annotation.Transactional;

import java.util.List;

public interface ObjectHandleReferenceRepository extends BaseRepository<ObjectHandleReferenceEntity,Long> {

    List<ObjectHandleReferenceEntity> findByObjectHandleItemId(Long id);

    @Transactional
    void deleteByObjectHandleItemId(Long objectHandleItemId);

}