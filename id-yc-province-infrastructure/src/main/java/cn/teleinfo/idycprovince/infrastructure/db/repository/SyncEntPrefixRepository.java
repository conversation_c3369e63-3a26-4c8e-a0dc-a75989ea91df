package cn.teleinfo.idycprovince.infrastructure.db.repository;

import cn.teleinfo.idycprovince.infrastructure.db.entity.SyncEntPrefixEntity;
import cn.teleinfo.idycprovince.infrastructure.db.view.EntSyncView;
import org.springframework.data.jpa.repository.Query;
import org.springframework.data.repository.query.Param;

import java.util.List;

public interface SyncEntPrefixRepository extends BaseRepository<SyncEntPrefixEntity, Long>{

    @Query(
            value = "select * from yc_integrated_ent_prefix ",
            nativeQuery = true
    )
    List<SyncEntPrefixEntity> findSyncEntPrefix();

    @Query(value = "SELECT " +
            "a.ent_prefix AS entPrefix, " +
            "b.org_name AS orgName, " +
            "b.org_code AS orgCode, " +
            "b.parent_org_name AS parentOrgName, " +
            "b.org_addr_province AS orgAddrProvince, " +
            "b.org_addr_city AS orgAddrCity, " +
            "b.org_addr_district AS orgAddrDistrict, " +
            "b.org_address AS orgAddress, " +
            "b.province_id AS provinceId, " +
            "b.industry_ent_id AS industryEntId, " +
            "b.is_deleted as isDeleted, " +
            "b.updated_time AS updatedTime " +
            "FROM yc_ent_prefix a " +
            "LEFT JOIN yc_ent b ON a.ent_id = b.id " +
            "WHERE a.ent_prefix IN :entPrefixList AND b.updated_time IS NOT NULL " +
            "GROUP BY b.id ", nativeQuery = true)
    List<EntSyncView> findSyncEntByPrefix(@Param("entPrefixList") List<String> entPrefixList);
}
