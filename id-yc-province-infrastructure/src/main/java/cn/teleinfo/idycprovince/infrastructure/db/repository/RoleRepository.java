package cn.teleinfo.idycprovince.infrastructure.db.repository;

import cn.teleinfo.idycprovince.infrastructure.db.entity.RoleEntity;
import cn.teleinfo.idycprovince.infrastructure.db.to.EntRoleInfoTO;
import cn.teleinfo.idycprovince.infrastructure.db.to.ProvinceRoleInfoTO;
import org.springframework.data.domain.Page;
import org.springframework.data.domain.Pageable;
import org.springframework.data.jpa.repository.Query;
import org.springframework.data.repository.query.Param;

import java.util.List;

public interface RoleRepository extends BaseRepository<RoleEntity, Long> {

    List<RoleEntity> findAllByProvinceIdAndUndeleteAndRoleTypeIn(Long provinceId, Integer undelete, List<Integer> roleTypeList);
    List<RoleEntity> findAllByProvinceId(Long provinceId);
    RoleEntity findByProvinceIdAndUndeleteAndRoleType(Long provinceId, Integer undelete, Integer roleType);

    @Query(nativeQuery = true, value = "select " +
            " yr.role_code AS roleCode, " +
            " yr.role_name AS roleName, " +
            " yr.id AS id " +
            " from yc_role yr " +
            " WHERE " +
            " yr.is_deleted = 0 " +
            " AND yr.role_name = :roleName " +
            " AND yr.ent_id = :entId ")
    List<EntRoleInfoTO> findRoleEntType(@Param("roleName") String roleName,@Param("entId") Long entId);

    @Query(nativeQuery = true, value = "select " +
            " yr.role_code AS roleCode, " +
            " yr.role_name AS roleName, " +
            " yr.id AS id " +
            " from yc_role yr " +
            " WHERE " +
            " yr.is_deleted = 0 " +
            " AND (yr.ent_id is null or yr.ent_id = -1)" +
            " AND yr.role_name = :roleName " +
            " AND yr.province_id = :provinceId ")
    List<EntRoleInfoTO> findRoleProvinceType(@Param("roleName") String roleName,@Param("provinceId") Long provinceId);

    boolean existsByRoleCodeAndProvinceIdAndEntId(String roleCode, Long provinceId, Long entId);

    @Query(nativeQuery = true, value =
            "SELECT " +
                    " ye.id AS entId, " +
                    " ye.org_name AS entName, " +
                    " yr.role_name AS roleName, " +
                    " yr.role_desc AS roleDesc, " +
                    " yr.created_time AS createdTime, " +
                    " yr.updated_time AS updatedTime, " +
                    " yr.undelete AS undelete, " +
                    " yr.province_id AS provinceId, " +
                    " yr.role_code AS roleCode, " +
                    " yr.sort AS sort, " +
                    " yr.id AS id " +
                    " FROM " +
                    " yc_role yr LEFT JOIN yc_ent ye ON yr.ent_id = ye.id AND yr.is_deleted = 0 " +
                    " WHERE " +
                    " yr.is_deleted = 0 " +
                    " AND yr.province_id = :provinceId " +
                    " AND if(:entId != '' and :entId is not null, yr.ent_id = :entId or yr.ent_id = -1, 1=1 ) " +
                    " AND if(:entName !='', ye.org_name like CONCAT('%',:entName,'%') , 1=1) " +
                    " AND if(:roleName !='', yr.role_name like CONCAT('%',:roleName,'%') , 1=1) " +
                    " AND (yr.created_by = :currentUserId or yr.role_name in :userNameList) ",
            countQuery =
                    " SELECT " +
                            " count(*) " +
                            " FROM " +
                            " yc_role yr LEFT JOIN yc_ent ye ON yr.ent_id = ye.id AND yr.is_deleted = 0 " +
                            " WHERE " +
                            " yr.is_deleted = 0 " +
                            " AND yr.province_id = :provinceId " +
                            " AND if(:entId != '' and :entId is not null, yr.ent_id = :entId or yr.ent_id = -1, 1=1 ) " +
                            " AND if(:entName !='', ye.org_name like CONCAT('%',:entName,'%') , 1=1) " +
                            " AND if(:roleName !='', yr.role_name like CONCAT('%',:roleName,'%') , 1=1) " +
                            " AND (yr.created_by = :currentUserId or yr.role_name in :userNameList) ")
    Page<EntRoleInfoTO> getEntRoleInfo(@Param("entId") Long entId, @Param("provinceId") Long provinceId
            , @Param("roleName") String roleName, @Param("entName") String entName, @Param("currentUserId") Long currentUserId
            , @Param("userNameList") List<String> userNameList, @Param("pageable") Pageable pageable);


    @Query(nativeQuery = true, value =
            "SELECT " +
                    " yr.role_name AS roleName, " +
                    " yr.role_desc AS roleDesc, " +
                    " yr.created_time AS createdTime, " +
                    " yr.updated_time AS updatedTime, " +
                    " yr.undelete AS undelete, " +
                    " yr.province_id AS provinceId, " +
                    " yr.role_code AS roleCode, " +
                    " yr.sort AS sort, " +
                    " yr.id AS id " +
                    " FROM " +
                    " yc_role yr " +
                    " WHERE " +
                    " yr.is_deleted = 0 " +
                    " AND (yr.ent_id is null or yr.ent_id = -1) " +
                    " AND (yr.province_id = :provinceId)" +
                    " AND if(:roleName !='', yr.role_name like CONCAT('%',:roleName,'%') , 1=1) " +
                    " AND (yr.created_by = :currentUserId or yr.role_name in :userNameList) ",
            countQuery =
                    " SELECT " +
                            " count(*) " +
                            " FROM " +
                            " yc_role yr  " +
                            " WHERE " +
                            " yr.is_deleted = 0 " +
                            " AND (yr.ent_id is null or yr.ent_id = -1)" +
                            " AND (yr.province_id = :provinceId)" +
                            " AND if(:roleName !='', yr.role_name like CONCAT('%',:roleName,'%') , 1=1) " +
                            " AND (yr.created_by = :currentUserId or yr.role_name in :userNameList) ")
    Page<ProvinceRoleInfoTO> getProvinceRoleInfo(@Param("provinceId") Long provinceId
            , @Param("roleName") String roleName, @Param("currentUserId") Long currentUserId
            , @Param("userNameList") List<String> userNameList, @Param("pageable") Pageable pageable);


    @Query(nativeQuery = true, value =
            "SELECT " +
                    " yr.role_name AS roleName, " +
                    " yr.role_desc AS roleDesc, " +
                    " yr.created_time AS createdTime, " +
                    " yr.updated_time AS updatedTime, " +
                    " yr.undelete AS undelete, " +
                    " yr.province_id AS provinceId, " +
                    " yr.role_code AS roleCode, " +
                    " yr.sort AS sort, " +
                    " yr.role_type AS roleType, " +
                    " yr.id AS id " +
                    " FROM " +
                    " yc_role yr " +
                    " WHERE " +
                    " yr.is_deleted = 0 " +
                    " AND if(:entId is null, (yr.ent_id is null or yr.ent_id = -1), 1=1 )" +
                    " AND if(:entId is not null, (yr.ent_id = :entId or yr.ent_id = -1), 1=1)" +
                    " AND (yr.province_id = :provinceId) " +
                    " AND (yr.created_by = :currentUserId or yr.role_name in :userNameList) ")
    List<ProvinceRoleInfoTO> getProvinceRoleByRoleType(@Param("provinceId") Long provinceId
            , @Param("entId") Long entId, @Param("currentUserId") Long currentUserId
            , @Param("userNameList") List<String> userNameList);

    @Query(nativeQuery = true, value =
            "SELECT " +
                    " yr.role_name AS roleName, " +
                    " yr.role_desc AS roleDesc, " +
                    " yr.created_time AS createdTime, " +
                    " yr.updated_time AS updatedTime, " +
                    " yr.undelete AS undelete, " +
                    " yr.province_id AS provinceId, " +
                    " yr.role_code AS roleCode, " +
                    " yr.sort AS sort, " +
                    " yr.id AS id " +
                    " FROM " +
                    " yc_role yr " +
                    " WHERE " +
                    " yr.is_deleted = 0 " +
                    " AND if(:entId is null, (yr.ent_id is null), 1=1 )" +
                    " AND if(:entId is not null, (yr.ent_id = :entId or (yr.ent_id = -1 and yr.role_type = 2)), 1=1)" +
                    " AND (yr.province_id = :provinceId)")
    List<ProvinceRoleInfoTO> getRoleListWhenAddUserByProvinceIdAndEntId(@Param("provinceId") Long provinceId
            , @Param("entId") Long entId);

    void deleteAllByProvinceId(Long id);

    RoleEntity findRoleEntityByProvinceIdAndRoleTypeAndUndelete(Long provinceId, Integer roleType, Integer undelete);

    @Query(
            nativeQuery = true,
            value = "SELECT " +
                    "c.role_name " +
                    "from yc_user a " +
                    "left join yc_user_role b on a.id = b.user_id and b.is_deleted = 0 " +
                    "left join yc_role c on b.role_id = c.id and c.is_deleted = 0 " +
                    "where a.id = :currentUserId "
    )
    List<String> findRoleNameByUserId(@Param("currentUserId") Long currentUserId);
}
