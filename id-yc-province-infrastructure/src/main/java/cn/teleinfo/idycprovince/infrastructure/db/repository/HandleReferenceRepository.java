package cn.teleinfo.idycprovince.infrastructure.db.repository;

import cn.teleinfo.idycprovince.infrastructure.db.entity.HandleReferenceEntity;
import org.springframework.data.jpa.repository.Modifying;
import org.springframework.data.jpa.repository.Query;
import org.springframework.data.repository.query.Param;
import org.springframework.transaction.annotation.Transactional;

import java.util.List;

/**
 * <AUTHOR>
 */
public interface HandleReferenceRepository extends BaseRepository<HandleReferenceEntity, Long> {

    /**
     * 根据属性id查询关联信息
     *
     * @param id
     * @return 结果
     **/
    List<HandleReferenceEntity> findByHandleItemId(Long id);

    /**
     * 根据属性id删除关联信息
     *
     * @param
     * @param id
     * @return 结果
     **/
    void deleteByHandleItemId(Long id);

    /**
     * 物理删除对象标识parentId下所有的标识关联属性
     *
     * @param handleParentId
     */
    @Transactional
    @Modifying
    @Query(nativeQuery = true, value = "DELETE FROM yc_handle_reference WHERE handle_item_id " +
            "IN (SELECT b.id FROM yc_handle a INNER JOIN yc_handle_item b ON a.id = b.handle_id WHERE a.parent_id = :handleParentId)")
    void deleteByHandleParentIdPhysical(@Param("handleParentId") Long handleParentId);


    @Query(nativeQuery = true, value = "select * from  yc_handle_reference where handle_item_id = :id")
    List<HandleReferenceEntity> getByHandleItemId(@Param("id") Long id);
}