package cn.teleinfo.idycprovince.infrastructure.db.entity;

import lombok.Getter;
import lombok.Setter;

import javax.persistence.Column;
import javax.persistence.Entity;
import javax.persistence.Id;
import javax.persistence.Table;

/**
 * 卷烟规格基本信息
 */
@Getter
@Setter
@Entity
@Table(name = "TZ_AA1_RAW_TOBACCO_LEAF")
public class RawTobaccoLeafEntity {

    /**
     * 原烟业务主键
     */
    @Id
    @Column(name = "AA_LEAF_RT_ID")
    private String id;

    /**
     * 烟叶原烟物料代码
     */
    @Column(name = "AA_LEAF_RT_CODE")
    private String code;

    /**
     * 烟叶原烟物料名称;
     */
    @Column(name = "AA_LEAF_RT_NAME")
    private String name;

}
