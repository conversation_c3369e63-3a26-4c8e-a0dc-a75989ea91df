package cn.teleinfo.idycprovince.infrastructure.db.entity;

import lombok.Data;
import org.springframework.data.annotation.CreatedDate;
import org.springframework.data.annotation.LastModifiedDate;

import javax.persistence.*;
import java.time.LocalDateTime;

/**
 * <AUTHOR>
 * @version 1.0
 * @date Created in 2022/11/9
 * @description 统计文件记录
 */
@Data
@Entity
@Table(name = "yc_count_file_record")
public class CountFileRecordEntity {

    @Id
    @GeneratedValue(strategy = GenerationType.IDENTITY)
    private Long id;

    /**
     * 创建时间
     */
    @CreatedDate
    @Column(name = "created_time")
    private LocalDateTime createdTime;

    /**
     * 更新时间
     */
    @LastModifiedDate
    @Column(name = "updated_time")
    private LocalDateTime updatedTime;

    /**
     * 最后读取文件的名称
     */
    @Column(name = "last_read_file")
    private String lastReadFile;

}
