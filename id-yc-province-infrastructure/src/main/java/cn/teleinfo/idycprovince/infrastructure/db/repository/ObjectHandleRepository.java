package cn.teleinfo.idycprovince.infrastructure.db.repository;

import cn.teleinfo.idycprovince.infrastructure.db.entity.ObjectHandleEntity;
import org.springframework.data.jpa.repository.Query;

import java.util.List;

public interface ObjectHandleRepository extends BaseRepository<ObjectHandleEntity,Long> {

    ObjectHandleEntity findByIdAndProvinceId(Long id, Long provinceId);

    ObjectHandleEntity findByEntPrefixAndWildcard(String prefix, String Wildcard);

    List<ObjectHandleEntity> findByEntPrefixAndEntId(String prefix, Long entId);

    ObjectHandleEntity findByHandle(String handle);

    List<ObjectHandleEntity> findByAppId(Long appId);

    List<ObjectHandleEntity> findAllByEntPrefix(String entPrefix);

}