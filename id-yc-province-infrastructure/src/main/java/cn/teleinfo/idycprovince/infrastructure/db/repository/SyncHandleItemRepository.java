package cn.teleinfo.idycprovince.infrastructure.db.repository;


import cn.teleinfo.idycprovince.infrastructure.db.entity.SyncHandleItemEntity;
import cn.teleinfo.idycprovince.infrastructure.db.to.SyncHandleItemTO;
import org.springframework.data.jpa.repository.Query;
import org.springframework.data.repository.query.Param;

import java.util.List;

public interface SyncHandleItemRepository extends BaseRepository<SyncHandleItemEntity, Long> {

    @Query(nativeQuery = true, value = "select     id AS id,\n" +
            "    source_id AS sourceId,\n" +
            "    created_time AS createdTime,\n" +
            "    updated_time AS updatedTime,\n" +
            "    field AS field,\n" +
            "    description AS description,\n" +
            "    field_type AS fieldType,\n" +
            "    handle_id AS handleId,\n" +
            "    field_value AS fieldValue,\n" +
            "    data_channel_id AS dataChannelId,\n" +
            "    field_source_type AS fieldSourceType,\n" +
            "    app_handle_code AS appHandleCode,\n" +
            "    remark AS remark,\n" +
            "    province_prefix AS provincePrefix,\n" +
            "    ent_prefix AS entPrefix,\n" +
            "    is_deleted AS isDeleted,\n" +
            "    data_channel_type AS dataChannelType,\n" +
            "    database_name AS databaseName,\n" +
            "    database_ip AS databaseIp,\n" +
            "    table_name AS tableName,\n" +
            "    column_name AS columnName from yc_integrated_handle_item where handle_id IN :ids ")
    List<SyncHandleItemTO> findByHandleIdIn(@Param("ids") List<Long> ids);

    @Query(value = "select i from  SyncHandleItemEntity i where  i.handleId = :handleId ")
    List<SyncHandleItemEntity> findAllIByHandleId(@Param("handleId") Long handleId);
}