package cn.teleinfo.idycprovince.infrastructure.db.entity;


import lombok.Getter;
import lombok.NoArgsConstructor;
import lombok.Setter;
import org.hibernate.annotations.SQLDelete;
import org.hibernate.annotations.SQLDeleteAll;
import org.hibernate.annotations.Where;

import javax.persistence.*;
import java.io.Serializable;

/**
 * <p>
 * 权限菜单
 * </p>
 *
 * <AUTHOR>
 * @since 2022-11-04
 */

@Getter
@Setter
@Entity
@Table(name = "yc_auth_menu")
@SQLDelete(sql = "update yc_auth_menu set is_deleted = null where id = ?")
@SQLDeleteAll(sql = "update yc_auth_menu set is_deleted = null where id = ?")
@Where(clause = "is_deleted = 0")
@NoArgsConstructor
public class AuthMenuEntity implements Serializable {

    private static final long serialVersionUID = 1L;

    @Id
    @GeneratedValue(strategy = GenerationType.IDENTITY)
    @Column(name = "id", nullable = false)
    private Long id;

    @Column(name = "auth_id")
    private Long authId;

    @Column(name = "menu_id")
    private Long menuId;

    /**
     * 逻辑删除: 0 未删除 null 已删除
     */
    @Column(name = "is_deleted")
    private Integer isDeleted = 0;

    public AuthMenuEntity(Long authId, Long menuId) {
        this.authId = authId;
        this.menuId = menuId;
    }
}
