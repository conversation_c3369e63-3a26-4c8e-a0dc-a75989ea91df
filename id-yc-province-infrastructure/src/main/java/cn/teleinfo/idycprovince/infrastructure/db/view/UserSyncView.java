package cn.teleinfo.idycprovince.infrastructure.db.view;

import java.time.LocalDateTime;

public interface UserSyncView {
    Long getId();
    Long getSourceId();
    String getAppHandleCode();
    String getEntPrefix();
    String getRoleCode();
    String getProvincePrefix();
    LocalDateTime getCreatedTime();
    LocalDateTime getUpdatedTime();
    String getUsername();
    Integer getIsDeleted();
    Integer getSourceType();
    String getRemark();
    String getUcOpenId();
    String getHandleCodes();
    String getName();
}