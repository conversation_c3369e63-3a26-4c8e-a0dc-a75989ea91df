package cn.teleinfo.idycprovince.infrastructure.redis;

import cn.tobacco.tcaf.cache.Caching;
import com.fasterxml.jackson.core.type.TypeReference;
import org.springframework.boot.autoconfigure.condition.ConditionalOnProperty;
import org.springframework.stereotype.Component;

import java.util.*;

/**
 * @description: redis 多云适配
 * @author: liuyan
 * @create: 2022−11-16 10:55 AM
 */
@ConditionalOnProperty(prefix = "app", name = "cache", havingValue = "tbc")
@Component
public class RedisTobaccoCache implements RedisCache {

    private final Caching caching;

    public RedisTobaccoCache(Caching caching) {
        this.caching = caching;
    }

    @Override
    public Boolean hasKey(String key) {
        return caching.getCache().exit(key);
    }

    /**
     * 根据key获取缓存数据
     *
     * @param key
     * @return
     */
    @Override
    public String get(String key) {
        Optional<String> value = caching.getCache().get(key, String.class);
        return value.orElse(null);
    }

    /**
     * set str
     *
     * @param key
     * @param value
     */
    @Override
    public void set(String key, String value) {
        caching.getCache().put(key, value, 0);
    }

    /**
     * set str
     *
     * @param key
     * @param value
     * @param expiresInSeconds
     */
    @Override
    public void set(String key, String value, int expiresInSeconds) {
        caching.getCache().put(key, value, expiresInSeconds);
    }


    /**
     * 判断key是否存在
     *
     * @param key
     * @return
     */
    @Override
    public boolean exists(String key) {
        return caching.getCache().exit(key);
    }

    /**
     * 删除key
     *
     * @param key
     * @return
     */
    @Override
    public boolean delete(String key) {
        return caching.getCache().remove(key);
    }

    /**
     * set add
     *
     * @param key
     * @param value
     * @return
     */
    @Override
    public boolean sAdd(String key, String value) {
        return caching.getCache().sAdd(key, value);
    }


    /**
     * @param key
     * @param clz
     * @return
     */
    @Override
    public List<String> sMembers(String key, Class clz) {
        Optional<List<String>> optional = caching.getCache().sMembers(key, clz);
        return optional.orElseGet(ArrayList::new);
    }


    /**
     * @param key
     * @param val
     * @return
     */
    @Override
    public long increment(String key, int val) {
        return caching.getCache().increase(key, val);
    }

    /**
     * hash 增加
     *
     * @param key
     * @param field
     * @param step
     * @return
     */
    @Override
    public long hIncrement(String key, String field, long step) {
        Optional<Long> optional = caching.getCache().hIncrement(key, field, step);
        return optional.orElse(0L);
    }

    /**
     * hash 获取对象
     *
     * @param key
     * @return
     */
    @Override
    public Map<String, Object> hEntries(String key) {
        TypeReference<Object> typeReference = new TypeReference<Object>() {};
        Optional<Map<String, Object>> optional = caching.getCache().hEntries(key, typeReference);
        return optional.orElse(Collections.EMPTY_MAP);

    }

    @Override
    public void rename(String oldKey, String newKey) {
        caching.getCache().rename(oldKey, newKey);
    }

    @Override
    public void hPutAll(String key, Map<String, Object> map) {
        caching.getCache().hSetAll(key, map);
    }

    @Override
    public void expireAt(String key, Date from) {
        caching.getCache().expireAt(key, from);
    }

    @Override
    public Long getTtl(String key) {
        return caching.getCache().getTtl(key);
    }
}
