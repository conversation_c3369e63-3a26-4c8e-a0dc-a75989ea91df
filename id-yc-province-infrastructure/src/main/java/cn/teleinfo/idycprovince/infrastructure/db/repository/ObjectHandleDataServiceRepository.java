package cn.teleinfo.idycprovince.infrastructure.db.repository;

import cn.teleinfo.idycprovince.infrastructure.db.entity.ObjectHandleDataServiceEntity;

import java.util.List;

/**
 * <AUTHOR>
 */
public interface ObjectHandleDataServiceRepository extends BaseRepository<ObjectHandleDataServiceEntity, Long> {
    
    /**
     * 由对象标识id查询关联数据服务id
     *
     * @param objectHandleId
     * @return 结果
     **/
    List<ObjectHandleDataServiceEntity> findByObjectHandleId(Long objectHandleId);
    
    /**
     * 由对象标识id删除
     *
     * @param objectHandleId
     * @return 结果
     **/
    void deleteByObjectHandleId(Long objectHandleId);

}