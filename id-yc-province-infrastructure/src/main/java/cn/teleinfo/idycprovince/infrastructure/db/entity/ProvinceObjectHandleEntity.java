package cn.teleinfo.idycprovince.infrastructure.db.entity;

import lombok.Getter;
import lombok.Setter;
import org.hibernate.annotations.SQLDelete;
import org.hibernate.annotations.SQLDeleteAll;
import org.hibernate.annotations.Where;

import javax.persistence.*;

@Getter
@Setter
@Entity
@Table(name = "yc_province_object_handle")
@SQLDelete(sql = "update yc_province_object_handle set is_deleted = null, updated_time = NOW()  where id = ?")
@SQLDeleteAll(sql = "update yc_province_object_handle set is_deleted = null, updated_time = NOW()  where id = ?")
@Where(clause = "is_deleted = 0")
public class ProvinceObjectHandleEntity extends BaseEntity{

    /**
    *  企业前缀
    */
    @Column(name = "ent_prefix")
    private String entPrefix;

    /**
    *  通配符
    */
    @Column(name = "wildcard")
    private String wildcard;

    /**
    *  对象标识名称
    */
    @Column(name = "name")
    private String name;

    /**
    *  对象标识
    */
    @Column(name = "handle")
    private String handle;

    /**
    *  标识管理员
    */
    @Column(name = "handle_admin")
    private String handleAdmin;

    /**
    *  上报状态 0未上报,1已上报
    */
    @Column(name = "upload_state")
    private Integer uploadState;

    /**
    *  企业ID
    */
    @Column(name = "ent_id")
    private Long entId;

    /**
     *  实体类型 1：资源实体 2：业务实体
     */
    @Column(name = "entity_type")
    private Integer entityType;

    /**
     *  所属应用
     */
    @Column(name = "app_id")
    private Long appId;
    
    /**
     *  所属应用名称
     */
    @Column(name = "app_name")
    private String appName;
    
    /**
     *  所属应用标识编码
     */
    @Column(name = "app_handle_code")
    private String appHandleCode;

}