package cn.teleinfo.idycprovince.infrastructure.db.model;

import lombok.Getter;
import lombok.Setter;
import org.springframework.util.CollectionUtils;

import java.io.Serializable;
import java.util.ArrayList;
import java.util.Collection;
import java.util.Comparator;
import java.util.List;
import java.util.stream.Collectors;

/**
 * 树模型
 *
 * <AUTHOR>
 * @Date 2017年9月11日
 */
@Getter
@Setter
public class TreeModel implements Serializable {

	/**
	 *
	 */
	private static final long serialVersionUID = 1L;

	/**
	 * 主键
	 */
	private Serializable id;

	/**
	 * 父节点主键
	 */
	private Serializable parentId;

	/**
	 * 排序
	 */
	private Integer sort = 1;

	private List<? extends TreeModel> childNodes = new ArrayList<>();


	/**
	 * 构建树
	 *
	 * @param nodes
	 * @return
	 * @Autor TBC
	 * @Date 2017年9月11日
	 */
	public static List<? extends TreeModel> buildTree(List<? extends TreeModel> allNodes,
													  List<? extends TreeModel> nodes) {
		if (CollectionUtils.isEmpty(nodes) || CollectionUtils.isEmpty(allNodes)) {
			return null;
		}
		for (TreeModel treeModel : nodes) {
			setChildren(treeModel, allNodes);
		}
		return nodes;
	}

	/**
	 * 构建树
	 *
	 * @param nodes
	 * @param firstNodeId
	 * @return
	 * @Autor TBC
	 * @Date 2017年9月11日
	 */
	public static List<? extends TreeModel> buildTree(List<? extends TreeModel> nodes, Serializable firstNodeId) {
		if (CollectionUtils.isEmpty(nodes)) {
			return null;
		}
		// 获取level为1的，未被停用的菜单
		List<? extends TreeModel> firstLevels = nodes.stream().filter(node -> node.getParentId().equals(firstNodeId))
				.collect(Collectors.toList());
		// 并根据order排序
		sortByOrder(firstLevels);

		// 给一级菜单添加子菜单
		firstLevels.stream().forEach(node -> setChildren(node, nodes));
		return firstLevels;
	}

	/**
	 * 递归排序出当前节点下的子节点
	 *
	 * @param currentNode
	 * @param nodeList
	 * @Autor TBC
	 * @Date 2017年6月23日
	 */
	private static void setChildren(TreeModel currentNode, Collection<? extends TreeModel> nodeList) {
		// 查询出当前节点下的子节点(根据子节点的parentId是父节点id)
		List<? extends TreeModel> childrens = nodeList.stream()
				.filter(node -> (node.getParentId().equals(currentNode.getId()))).collect(Collectors.toList());
		currentNode.childNodes = childrens;

		if (CollectionUtils.isEmpty(childrens)) {
			return;
		}
		// 在进行排序
		sortByOrder(childrens);
		// 递归次方法
		childrens.stream().forEach(node -> setChildren(node, nodeList));
	}

	/**
	 * 根据order排序
	 *
	 * @param firstLevels
	 * @Autor TBC
	 * @Date 2017年6月23日
	 */
	private static void sortByOrder(List<? extends TreeModel> firstLevels) {
		firstLevels.sort(Comparator.comparing(node -> Integer.valueOf(node.getSort())));
	}

}
