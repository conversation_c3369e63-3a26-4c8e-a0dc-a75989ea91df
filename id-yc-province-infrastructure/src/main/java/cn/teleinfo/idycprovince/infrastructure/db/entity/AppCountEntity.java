package cn.teleinfo.idycprovince.infrastructure.db.entity;

import lombok.Getter;
import lombok.Setter;

import javax.persistence.Column;
import javax.persistence.Entity;
import javax.persistence.GeneratedValue;
import javax.persistence.GenerationType;
import javax.persistence.Id;
import javax.persistence.Table;
import java.io.Serializable;
import java.time.LocalDateTime;

/**
 * <AUTHOR>
 * @version 1.0
 * @date Created in 2022/11/29
 * @description 应用接入数量统计
 */
@Getter
@Setter
@Entity
@Table(name = "yc_app_count")
public class AppCountEntity implements Serializable {

    @Id
    @GeneratedValue(strategy = GenerationType.IDENTITY)
    private Long id;

    /**
     * 创建时间
     */
    @Column(name = "created_time")
    private LocalDateTime createdTime;

    /**
     * 更新时间
     */
    @Column(name = "updated_time")
    private LocalDateTime updatedTime;

    /**
     * 省级前缀
     */
    @Column(name = "province_prefix")
    private String provincePrefix;

    /**
     * 企业前缀
     */
    @Column(name = "ent_prefix")
    private String entPrefix;

    /**
     * 统计日期（月）
     */
    @Column(name = "statistics_mouth")
    private String statisticsMouth;

    /**
     * 统计日期
     */
    @Column(name = "statistics_date")
    private String statisticsDate;

    /**
     * 移除属性数量
     */
    @Column(name = "app_num")
    private Long appNum;

    /**
     * 数据来源
     */
    @Column(name = "source_type")
    private Integer sourceType;

    @Column(name = "version")
    private Long version;

}
