package cn.teleinfo.idycprovince.infrastructure.db.repository;

import cn.teleinfo.idycprovince.infrastructure.db.entity.ObjectHandleItemEntity;
import org.springframework.transaction.annotation.Transactional;

import java.util.List;

public interface ObjectHandleItemRepository extends BaseRepository<ObjectHandleItemEntity,Long> {

    List<ObjectHandleItemEntity> findByObjectHandleId(Long objectHandleId);

    List<ObjectHandleItemEntity> findByObjectHandleIdAndFieldType(Long objectHandleId, Integer fieldType);

    @Transactional
    void deleteByObjectHandleId(Long objectHandleId);

    List<ObjectHandleItemEntity> findByObjectHandleIdAndFieldTypeIn(Long id, List<Integer> types);
    
    /**
     * 查询是否有属性关联数据服务
     *
     * @param dataServiceId
     * @return 结果
     **/
    int countByDataServiceId(Long dataServiceId);

}