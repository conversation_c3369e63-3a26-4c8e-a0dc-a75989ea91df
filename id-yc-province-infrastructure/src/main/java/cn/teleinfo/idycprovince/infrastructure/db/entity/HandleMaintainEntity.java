package cn.teleinfo.idycprovince.infrastructure.db.entity;

import lombok.Getter;
import lombok.Setter;
import org.hibernate.annotations.SQLDelete;
import org.hibernate.annotations.SQLDeleteAll;
import org.hibernate.annotations.Where;

import javax.persistence.Column;
import javax.persistence.Entity;
import javax.persistence.Table;
import javax.persistence.Version;
import java.time.LocalDateTime;

/***
 * @title HandleMaintainEntity
 * @description 标识维护表
 * <AUTHOR>
 * @version 1.0.0
 * @create 2023/2/27 11:15
 **/
@Getter
@Setter
@Entity
@Table(name = "yc_handle_maintain")
@SQLDelete(sql = "update yc_handle_maintain set is_deleted = null, updated_time = NOW()  where id = ? and version = ?")
@SQLDeleteAll(sql = "update yc_handle_maintain set is_deleted = null, updated_time = NOW()  where id = ? and version = ?")
@Where(clause = "is_deleted = 0")
public class HandleMaintainEntity extends BaseEntity {

    /**
     *  标识名称
     */
    @Column(name = "name")
    private String name;

    /**
     *  标识
     */
    @Column(name = "handle")
    private String handle;

    /**
     *  标识所属企业名称
     */
    @Column(name = "org_name")
    private String orgName;

    /**
     *  所属应用名称
     */
    @Column(name = "app_name")
    private String appName;

    /**
     *  实体类型 1：资源实体 2：业务实体
     */
    @Column(name = "entity_type")
    private Integer entityType;

    /**
     *  企业ID
     */
    @Column(name = "ent_id")
    private Long entId;

    /**
     *  应用ID
     */
    @Column(name = "app_id")
    private Long appId;

    /**
     *  维护状态
     */
    @Column(name = "maintain_state")
    private Integer maintainState;

    /**
     *  应用编码
     */
    @Column(name = "app_code")
    private String appCode;

    /**
     *  乐观锁
     */
    @Column(name = "version")
    @Version
    private Long version;

    /**
     * 维护属性
     */
    @Column(name = "maintain_field")
    private String maintainField;

    /**
     * 维护属性值
     */
    @Column(name = "maintain_field_value")
    private String maintainFieldValue;
    /**
     * 最后一次维护时间
     */
    @Column(name = "last_maintain_time")
    private LocalDateTime lastMaintainTime;

}
