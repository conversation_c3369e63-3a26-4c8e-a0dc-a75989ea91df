package cn.teleinfo.idycprovince.infrastructure.db.entity;

import lombok.Getter;
import lombok.Setter;
import org.hibernate.annotations.SQLDelete;
import org.hibernate.annotations.SQLDeleteAll;
import org.hibernate.annotations.Where;

import javax.persistence.Column;
import javax.persistence.Entity;
import javax.persistence.Table;
import java.io.Serializable;

/**
 * 标识审核记录表
 */
@Getter
@Setter
@Entity
@Table(name = "yc_handle_audit_log")
@SQLDelete(sql = "update yc_handle_audit_log set is_deleted = null , updated_time = NOW() where id = ?")
@SQLDeleteAll(sql = "update yc_handle_audit_log set is_deleted = null , updated_time = NOW() where id = ?")
@Where(clause = "is_deleted = 0")
public class HandleAuditLogEntity extends BaseEntity implements Serializable {

    private static final long serialVersionUID = 1L;

    /**
     * 标识 ID
     */
    @Column(name = "handle_id")
    private Long handleId;

    /**
     * 审核类型 0 注册 1 编辑 2 删除
     */
    @Column(name = "audit_type")
    private Integer auditType;

    /**
     * 审核状态 0 审核中 1 审核通过 2 驳回
     */
    @Column(name = "audit_state")
    private Integer auditState;

    /**
     * 审核备注
     */
    @Column(name = "audit_remark")
    private String auditRemark;


    /**
     * 企业ID
     */
    @Column(name = "ent_id")
    private Long entId;


}
