package cn.teleinfo.idycprovince.infrastructure.db.repository;

import cn.teleinfo.idycprovince.infrastructure.db.entity.EntPrefixHostingEntity;
import cn.teleinfo.idycprovince.infrastructure.db.to.EntProfixHostingTO;
import org.springframework.data.domain.Page;
import org.springframework.data.domain.Pageable;
import org.springframework.data.jpa.repository.Query;
import org.springframework.data.repository.query.Param;

import java.util.List;

public interface EntPrefixHostingRepository extends BaseRepository<EntPrefixHostingEntity, Long> {

//    @Query(nativeQuery = true, value =
//            "SELECT " +
//                    " b.id, " +
//                    " b.srv_name AS srvName, " +
//                    " b.ip_type AS iptype, " +
//                    " b.ip, " +
//                    " b.tcp_port AS tcpPort, " +
//                    " b.udp_port AS udpPort, " +
//                    " b.http_port AS httpPort, " +
//                    " b.ip_type_reslove AS ipTypeReslove, " +
//                    " b.ip_reslove AS ipReslove, " +
//                    " b.tcp_port_reslove AS tcpPortReslove, " +
//                    " b.udp_port_reslove AS udpPortReslove, " +
//                    " b.http_port_reslove AS httpPortReslove " +
//                    " FROM " +
//                    " yc_hosting_server b " +
//                    " WHERE " +
//                    " b.is_deleted = 0 " +
//                    " AND b.province_id = :provinceId " +
//                    " AND (:srvName is null or b.srvName LIKE CONCAT('%',:srvName,'%'))",
//            countQuery =
//                    " SELECT " +
//                            "count(*) " +
//                            " FROM " +
//                            " yc_hosting_server b " +
//                            " WHERE " +
//                            " b.is_deleted = 0 " +
//                            " AND b.province_id = :provinceId " +
//                            " AND (:srvName is null or b.srvName LIKE CONCAT('%',:srvName,'%'))")
//    Page<EntProfixHostingTO> getEntProxypage(@Param("provinceId") Long provinceId, @Param("srvName") String srvName
//            , @Param("pageable") Pageable pageable);

    @Query(nativeQuery = true, value =
            "SELECT " +
                    " a.id, " +
                    " c.ent_prefix AS entPrefix, " +
                    " b.srv_name AS srvName, " +
                    " b.ip_type AS iptype, " +
                    " b.ip, " +
                    " b.tcp_port AS tcpPort, " +
                    " b.udp_port AS udpPort, " +
                    " b.http_port AS httpPort, " +
                    " b.ip_type_reslove AS ipTypeReslove, " +
                    " b.ip_reslove AS ipReslove, " +
                    " b.tcp_port_reslove AS tcpPortReslove, " +
                    " b.udp_port_reslove AS udpPortReslove, " +
                    " b.http_port_reslove AS httpPortReslove " +
                    " FROM " +
                    " yc_ent_prefix_hosting a " +
                    " LEFT JOIN yc_hosting_server b ON a.hosting_server_id = b.id " +
                    " LEFT JOIN yc_ent_prefix c ON a.ent_prefix_id = c.id " +
                    " WHERE " +
                    " a.is_deleted = 0 " +
                    " AND a.province_id = :provinceId " +
                    " AND c.state = 1 " +
                    " AND (:entPrefix is null or c.ent_prefix LIKE CONCAT('%',:entPrefix,'%'))",
            countQuery =
                    " SELECT " +
                            "count(*) " +
                            " FROM " +
                            " yc_ent_prefix_hosting a " +
                            " LEFT JOIN yc_hosting_server b ON a.hosting_server_id = b.id " +
                            " LEFT JOIN yc_ent_prefix c ON a.ent_prefix_id = c.id " +
                            " WHERE " +
                            " a.is_deleted = 0 " +
                            " AND a.province_id = :provinceId " +
                            " AND c.state = 1 " +
                            " AND (:entPrefix is null or c.ent_prefix LIKE CONCAT('%',:entPrefix,'%'))")
    Page<EntProfixHostingTO> getEntProxypage(@Param("provinceId") Long provinceId, @Param("entPrefix") String entPrefix
            , @Param("pageable") Pageable pageable);

    EntPrefixHostingEntity findByEntPrefix(String entPrefix);

    EntPrefixHostingEntity findByEntPrefixAndHostingState(String entPrefix, Integer hostingState);

    List<EntPrefixHostingEntity> findByEntId(Long entId);

    List<EntPrefixHostingEntity> findByEntIdAndHostingState(Long entId, Integer hostingState);

    List<EntPrefixHostingEntity> findByHostingServerIdAndHostingState(Long hostingServerId, Integer hostingState);

    EntPrefixHostingEntity findByEntPrefixId(Long entPrefixId);

    @Query(nativeQuery = true, value = " SELECT DISTINCT " +
            " ( b.ent_prefix )  " +
            " FROM " +
            " yc_ent_prefix_hosting a " +
            " LEFT JOIN yc_ent_prefix b ON a.ent_prefix_id = b.id  " +
            " LEFT JOIN yc_app_info c ON b.id = c.prefix_id " +
            " WHERE " +
            " b.state = 1  " +
            " AND b.is_deleted = 0  " +
            " AND a.is_deleted = 0  " +
            " AND a.hosting_state = 1  " +
            " AND a.ent_id = :entId " +
            " AND if(:appId != '' and :appId is not null, c.id = :appId, 1=1 ) ")
    List<String> queryPrefixAllByEntIdAndAppId(@Param("entId") Long entId,@Param("appId") Long appId);

    Integer countByHostingServerIdAndHostingState(Long hostingServerId, Integer hostingState);

}
