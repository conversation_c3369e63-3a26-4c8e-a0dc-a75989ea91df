package cn.teleinfo.idycprovince.infrastructure.db.repository;

import cn.teleinfo.idycprovince.infrastructure.db.entity.EntEntity;
import cn.teleinfo.idycprovince.infrastructure.db.to.EntPageTO;
import org.springframework.data.domain.Page;
import org.springframework.data.domain.Pageable;
import org.springframework.data.jpa.repository.Modifying;
import org.springframework.data.jpa.repository.Query;
import org.springframework.data.repository.query.Param;

import java.time.LocalDateTime;
import java.util.List;
import java.util.Map;

public interface EntRepository extends BaseRepository<EntEntity, Long> {

    @Query(nativeQuery = true, value = " SELECT id FROM yc_ent LIMIT 1 ")
    Long getOne();

    Boolean existsAllByProvinceId(Long provinceId);
    List<EntEntity> findAllByProvinceId(Long provinceId);

    @Query(nativeQuery = true,
            value = "SELECT " +
                    "a.ID, " +
                    "a.org_name AS orgName, " +
                    "a.org_code AS orgCode, " +
                    "a.parent_org_name AS parentOrgName, " +
                    "b.dict_value AS provinceName, " +
                    "c.dict_value AS cityName, " +
                    "d.dict_value AS districtName, " +
                    "a.org_address AS orgAddress, " +
                    "a.updated_time AS updatedTime, " +
                    "IFNULL(e.hosting_state, 0) as hostingState " +
                    "FROM yc_ent a " +
                    "LEFT JOIN yc_sys_dict b ON a.org_addr_province = b.dict_code AND b.dict_key = 'PROVINCE' " +
                    "LEFT JOIN yc_sys_dict c ON a.org_addr_city = c.dict_code AND c.dict_key = 'CITY' " +
                    "LEFT JOIN yc_sys_dict d ON a.org_addr_district = d.dict_code AND d.dict_key = 'DISTRICT' " +
                    "LEFT JOIN yc_ent_prefix_hosting e ON e.ent_id = a.id " +
                    "WHERE a.is_deleted = 0 " +
                    "AND IF(:entName != '' and :entName is not null,a.org_name LIKE CONCAT('%',:entName,'%'), 1=1 ) " +
                    "GROUP BY a.ID ",
            countQuery = "SELECT count(1) " +
                    "FROM yc_ent a " +
                    "LEFT JOIN yc_sys_dict b ON a.org_addr_province = b.dict_code AND b.dict_key = 'PROVINCE' " +
                    "LEFT JOIN yc_sys_dict c ON a.org_addr_city = c.dict_code AND c.dict_key = 'CITY' " +
                    "LEFT JOIN yc_sys_dict d ON a.org_addr_district = d.dict_code AND d.dict_key = 'DISTRICT' " +
                    "LEFT JOIN yc_ent_prefix_hosting e ON e.ent_id = a.id " +
                    "WHERE a.is_deleted = 0 " +
                    "AND IF(:entName != '' and :entName is not null,a.org_name LIKE CONCAT('%',:entName,'%'), 1=1 ) " +
                    "GROUP BY a.ID "
    )
    Page<EntPageTO> queryEnt(@Param("entName") String entName, Pageable pageable);

    @Query(nativeQuery = true,
            value = "SELECT " +
                    "a.ID, " +
                    "a.org_name AS orgName, " +
                    "a.org_code AS orgCode, " +
                    "a.parent_org_name AS parentOrgName, " +
                    "b.dict_value AS provinceName, " +
                    "c.dict_value AS cityName, " +
                    "d.dict_value AS districtName, " +
                    "a.org_addr_province AS province, " +
                    "a.org_addr_city AS city, " +
                    "a.org_addr_district AS district, " +
                    "a.org_address AS orgAddress, " +
                    "a.updated_time AS updatedTime " +
                    "FROM yc_ent a " +
                    "LEFT JOIN yc_sys_dict b ON a.org_addr_province = b.dict_code AND b.dict_key = 'PROVINCE' " +
                    "LEFT JOIN yc_sys_dict c ON a.org_addr_city = c.dict_code AND c.dict_key = 'CITY' " +
                    "LEFT JOIN yc_sys_dict d ON a.org_addr_district = d.dict_code AND d.dict_key = 'DISTRICT' " +
                    "WHERE a.is_deleted = 0 AND a.id = :id "
    )
    EntPageTO queryEntDetail(@Param("id") Long id);

    @Modifying(clearAutomatically = true)
    @Query(nativeQuery = true,
            value = "UPDATE yc_ent SET " +
                    "org_code = :orgCode, " +
                    "org_name = :orgName, " +
                    "parent_org_name = :parentOrgName, " +
                    "org_addr_province = :orgAddrProvince, " +
                    "org_addr_city = :orgAddrCity, " +
                    "org_addr_district = :orgAddrDistrict, " +
                    "org_address = :orgAddress, " +
                    "province_id = :provinceId, " +
                    "industry_ent_id = :industryEntId, " +
                    "is_deleted = :isDeleted, " +
                    "updated_time = :updatedTime " +
                    "WHERE id = :id")
    void saveBySql(@Param("id") Long id, @Param("orgCode") String orgCode, @Param("orgName") String orgName, @Param("parentOrgName") String parentOrgName, @Param("orgAddrProvince") String orgAddrProvince, @Param("orgAddrCity") String orgAddrCity, @Param("orgAddrDistrict") String orgAddrDistrict, @Param("orgAddress") String orgAddress, @Param("provinceId") Long provinceId, @Param("industryEntId") Long industryEntId, @Param("isDeleted") Integer isDeleted, @Param("updatedTime") LocalDateTime updatedTime);
}