package cn.teleinfo.idycprovince.infrastructure.db.repository;

import cn.teleinfo.idycprovince.infrastructure.db.entity.JobDO;
import cn.teleinfo.idycprovince.infrastructure.db.view.JobLogView;
import cn.teleinfo.idycprovince.infrastructure.db.view.JobView;
import org.springframework.data.domain.Page;
import org.springframework.data.domain.Pageable;
import org.springframework.data.jpa.repository.Query;
import org.springframework.data.repository.query.Param;

import java.time.LocalDateTime;

public interface JobRepository extends BaseRepository<JobDO, Long>{

    @Query(nativeQuery = true,
            value = "SELECT CONVERT(id, CHAR) AS id, " +
                    "name, " +
                    "status, " +
                    "identity, " +
                    "handler_name AS handlerName, " +
                    "handler_param AS handlerParam, " +
                    "cron_expression AS cronExpression, " +
                    "retry_count AS retryCount, " +
                    "retry_interval AS retryInterval, " +
                    "monitor_timeout AS monitorTimeout, " +
                    "create_time AS createTime, " +
                    "update_time AS updateTime, " +
                    "creator, " +
                    "updater, " +
                    "deleted " +
                    "FROM yc_job " +
                    "WHERE deleted = 0 " +
                    "AND if(:name IS NOT NULL AND :name != '', name LIKE CONCAT('%', :name, '%'), 1=1) " +
                    "AND if(:status IS NOT NULL, status = :status, 1=1) " +
                    "AND if(:handlerName IS NOT NULL AND :handlerName != '', handler_name LIKE CONCAT('%', :handlerName, '%'), 1=1)",
            countQuery = "SELECT COUNT(*) " +
                    "FROM yc_job " +
                    "WHERE deleted = 0 " +
                    "AND if(:name IS NOT NULL AND :name != '',  name LIKE CONCAT('%', :name, '%'), 1=1) " +
                    "AND if(:status IS NOT NULL, status = :status, 1=1) " +
                    "AND if(:handlerName IS NOT NULL AND :handlerName != '', handler_name LIKE CONCAT('%', :handlerName, '%'), 1=1)")
    Page<JobView> findJobByPage(@Param("name") String name,
                                @Param("status") Integer status,
                                @Param("handlerName") String handlerName,
                                Pageable pageable);

    @Query(nativeQuery = true,
            value = "SELECT CONVERT(id, CHAR) AS id, " +
                    "CONVERT(job_id, CHAR) AS jobId, " +
                    "handler_name AS handlerName, " +
                    "handler_param AS handlerParam, " +
                    "execute_index AS executeIndex, " +
                    "begin_time AS beginTime, " +
                    "end_time AS endTime, " +
                    "duration, " +
                    "status, " +
                    "result," +
                    "creator," +
                    "updater," +
                    "deleted," +
                    "create_time as createTime," +
                    "update_time as updateTime " +
                    "FROM yc_job_log " +
                    "WHERE deleted = 0 " +
                    "AND if(:jobId IS NOT NULL, job_id = :jobId, 1=1) " +
                    "AND if(:status IS NOT NULL, status = :status, 1=1) " +
                    "AND if(:handlerName IS NOT NULL AND :handlerName != '', handler_name LIKE CONCAT('%', :handlerName, '%'), 1=1) " +
                    "AND if(:handlerParam IS NOT NULL AND :handlerParam != '', handler_param LIKE CONCAT('%', :handlerParam, '%'), 1=1) " +
                    "AND if(:beginTime IS NOT NULL, begin_time >= :beginTime, 1=1) " +
                    "AND if(:endTime IS NOT NULL, end_time <= :endTime, 1=1)" +
                    "order by create_time desc",
            countQuery = "SELECT COUNT(*) " +
                    "FROM yc_job_log " +
                    "WHERE deleted = 0 " +
                    "AND if(:jobId IS NOT NULL, job_id = :jobId, 1=1) " +
                    "AND if(:status IS NOT NULL, status = :status, 1=1) " +
                    "AND if(:handlerName IS NOT NULL AND :handlerName != '', handler_name LIKE CONCAT('%', :handlerName, '%'), 1=1) " +
                    "AND if(:handlerParam IS NOT NULL AND :handlerParam != '', handler_param LIKE CONCAT('%', :handlerParam, '%'), 1=1) " +
                    "AND if(:beginTime IS NOT NULL, begin_time >= :beginTime, 1=1) " +
                    "AND if(:endTime IS NOT NULL, end_time <= :endTime, 1=1) " +
                    "order by create_time desc"
    )
    Page<JobLogView> findJobLogByPage(@Param("jobId") Long jobId,
                                     @Param("status") Integer status,
                                     @Param("handlerName") String handlerName,
                                     @Param("handlerParam") String handlerParam,
                                     @Param("beginTime") LocalDateTime beginTime,
                                     @Param("endTime") LocalDateTime endTime,
                                     Pageable pageable);
}
