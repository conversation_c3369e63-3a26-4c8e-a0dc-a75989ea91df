package cn.teleinfo.idycprovince.infrastructure.db.entity;

import lombok.Getter;
import lombok.Setter;
import org.hibernate.annotations.SQLDelete;
import org.hibernate.annotations.SQLDeleteAll;
import org.hibernate.annotations.Where;

import javax.persistence.Column;
import javax.persistence.Entity;
import javax.persistence.Table;
import javax.persistence.Transient;
import java.time.LocalDateTime;
import java.util.List;

/**
 *
 *
 * @Author: liusp
 * @Date: 2023/10/10/17:23
 * @Description:数据库实体类
 */
@Entity
@Getter
@Setter
@SQLDelete(sql = "update yc_metadata set is_deleted = null, updated_time = NOW()  where id = ?")
@SQLDeleteAll(sql = "update yc_metadata set is_deleted = null, updated_time = NOW()  where id = ?")
@Where(clause = "is_deleted = 0")
@Table(name = "yc_metadata")
public class MetaDataEntity extends BaseEntity{
    private static final long serialVersionUID = 1L;
    /**
     * 企业ID
     */
    @Column(name = "ent_id")
    private Long entId;
    /**
     * 应用ID
     */
    @Column(name = "app_id")
    private Long appId;
    /**
     * 数据服务数据库ID
     */
    @Column(name = "data_service_db_id")
    private Long databaseId;

    /** 描述 */
    @Column(name = "description")
    private String description ;

    @Transient
    List<MetaDataTableEntity> metaDataTableEntityList;
}
