package cn.teleinfo.idycprovince.infrastructure.db.entity;

import lombok.Getter;
import lombok.Setter;
import org.hibernate.annotations.BatchSize;
import org.hibernate.annotations.SQLDelete;
import org.hibernate.annotations.SQLDeleteAll;
import org.hibernate.annotations.Where;

import javax.persistence.Column;

import javax.persistence.Entity;
import javax.persistence.Id;
import javax.persistence.Table;
import java.time.LocalDateTime;


@Getter
@Setter
@Entity
@Table(name = "yc_integrated_handle")

public class SyncHandleEntity {

    @Id
    @Column(name = "id")
    private Long id;

    @Column(name = "source_id")
    private Long sourceId;

    @Column(name = "created_time")
    private LocalDateTime createdTime;

    @Column(name = "updated_time")
    private LocalDateTime updatedTime;

    @Column(name = "ent_prefix")
    private String entPrefix;

    @Column(name = "wildcard")
    private String wildcard;

    @Column(name = "name")
    private String name;

    @Column(name = "handle")
    private String handle;

    @Column(name = "entity_type")
    private Integer entityType;

    @Column(name = "app_handle_code")
    private String appHandleCode;

    @Column(name = "province_prefix")
    private String provincePrefix;

    @Column(name = "is_deleted")
    private Integer isDeleted;
}
