package cn.teleinfo.idycprovince.infrastructure.db.entity;

import lombok.Getter;
import lombok.Setter;

import javax.persistence.Column;
import javax.persistence.Entity;
import javax.persistence.Table;
import java.io.Serializable;

/**
 * <AUTHOR>
 * @version 1.0
 * @date Created in 2022/11/5
 * @description 节点信息
 */
@Getter
@Setter
@Entity
@Table(name = "yc_node_info")
public class NodeInfoEntity extends BaseEntity implements Serializable {
    /**
     * 企业id
     */
    @Column(name = "ent_id")
    private Long entId;

    /**
     * 节点名称
     */
    @Column(name = "node_name")
    private String nodeName ;

    /**
     * 企业名称
     */
    @Column(name = "ent_name")
    private String entName ;

    /**
     * 节点前缀
     */
    @Column(name = "node_prefix")
    private String nodePrefix ;

    /**
     * 节点地址
     */
    @Column(name = "node_address")
    private String nodeAddress ;

    /**
     * 过期时间
     */
    @Column(name = "expire_time")
    private String expireTime ;

    /**
     * 路径
     */
    @Column(name = "node_path")
    private String nodePath ;

    /**
     * 状态
     */
    @Column(name = "enabled")
    private String enabled ;

}
