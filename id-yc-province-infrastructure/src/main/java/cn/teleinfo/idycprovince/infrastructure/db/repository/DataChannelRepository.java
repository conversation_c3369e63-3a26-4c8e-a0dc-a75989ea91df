package cn.teleinfo.idycprovince.infrastructure.db.repository;

import cn.teleinfo.idycprovince.infrastructure.db.entity.DataChannelEntity;
import cn.teleinfo.idycprovince.infrastructure.db.to.ChannelTO;
import cn.teleinfo.idycprovince.infrastructure.db.view.DataChannelPageView;
import cn.teleinfo.idycprovince.infrastructure.db.view.HandlePageView;
import org.springframework.data.domain.Page;
import org.springframework.data.domain.Pageable;
import org.springframework.data.jpa.repository.Query;
import org.springframework.data.repository.query.Param;

import java.util.Date;
import java.util.List;

public interface DataChannelRepository extends BaseRepository<DataChannelEntity, Long> {

    DataChannelEntity findByDataChannelId(Long dataChannelId);

    @Query(nativeQuery = true, value = "SELECT" +
            " a.id AS id," +
            " a.data_channel_name AS dataChannelName," +
            " d.`name` AS objectHandleName," +
            " a.data_channel_id AS dataChannelId," +
            " a.data_type AS dataType," +
            " a.is_share AS isShare," +
            " b.database_name AS databaseName," +
            " b.database_ip AS databaseIp," +
            " a.send_status AS sendStatus," +
            " a.updated_time AS updatedTime, " +
            " c.data_service_type AS dataServiceType " +
            " FROM" +
            " yc_data_channel a" +
            " LEFT JOIN yc_data_service_db b ON a.database_id = b.id" +
            " LEFT JOIN yc_handle d ON a.object_handle_id = d.id  " +
            " LEFT JOIN yc_data_service c ON a.data_service_id = c.id  " +
            " WHERE" +
            " a.is_deleted = 0 " +
            " AND if(:dataChannelName is not null, a.data_channel_name like CONCAT('%',:dataChannelName,'%'),1=1) " +
            " AND if(:objectHandle is not null , d.`name` like CONCAT('%',:objectHandle,'%'), 1=1 ) " +
            " AND if(:sendStatus is not null , a.send_status = :sendStatus , 1=1 ) " +
            " AND if(:startTime is not null , a.updated_time >= :startTime , 1=1)  " +
            " AND if(:endTime is not null , a.updated_time <= :endTime , 1=1)  " +
            " AND a.province_id = :provinceId " +
            " AND a.ent_id = :entId " +
            " AND a.app_id = :appId ",
            countQuery = "SELECT" +
                    " count(*)" +
                    " FROM" +
                    " yc_data_channel a" +
                    " LEFT JOIN yc_data_service_db b ON a.database_id = b.id" +
                    " LEFT JOIN yc_handle d ON a.object_handle_id = d.id  " +
                    " LEFT JOIN yc_data_service c ON a.data_service_id = c.id  " +
                    " WHERE" +
                    " a.is_deleted = 0 " +
                    " AND if(:dataChannelName is not null, a.data_channel_name like CONCAT('%',:dataChannelName,'%'),1=1) " +
                    " AND if(:objectHandle is not null , d.`name` like CONCAT('%',:objectHandle,'%'), 1=1 ) " +
                    " AND if(:sendStatus is not null , a.send_status = :sendStatus , 1=1 ) " +
                    " AND if(:startTime is not null , a.updated_time >= :startTime , 1=1)  " +
                    " AND if(:endTime is not null , a.updated_time <= :endTime , 1=1)  " +
                    " AND a.province_id = :provinceId " +
                    " AND a.ent_id = :entId " +
                    " AND a.app_id = :appId ")
    Page<DataChannelPageView> page(@Param("dataChannelName") String dataChannelName,
                                   @Param("objectHandle") String objectHandle,
                                   @Param("sendStatus") Integer sendStatus,
                                   @Param("startTime") Date startTime,
                                   @Param("endTime") Date endTime,
                                   @Param("provinceId") Long provinceId,
                                   @Param("entId") Long entId,
                                   @Param("appId") Long appId,
                                   Pageable pageable);

    DataChannelEntity findFirstByDataChannelIdAndObjectHandleId(Long dataChannelId, Long objectHandleId);

    DataChannelEntity findByDataChannelNameAndAppId(String dataChannelName, Long appId);

    @Query(nativeQuery = true, value = " SELECT DISTINCT h.id,h.name,h.handle,h.handle_status as handleStatus FROM yc_handle_item i LEFT JOIN yc_handle h ON h.id=i.handle_id AND h.is_deleted=0 WHERE i.is_deleted=0 AND i.data_source_id = :dataChannelId " +
            " AND if(:name is not null, h.name like CONCAT('%',:name,'%'),1=1) " +
            " AND if(:handle is not null , h.handle like CONCAT('%',:handle,'%'), 1=1 ) "
            ,
            countQuery = " SELECT count(DISTINCT h.id)  FROM yc_handle_item i LEFT JOIN yc_handle h ON h.id=i.handle_id AND h.is_deleted=0 WHERE i.is_deleted=0 AND i.data_source_id = :dataChannelId  " +
                    " AND if(:name is not null, h.name like CONCAT('%',:name,'%'),1=1) " +
                    " AND if(:handle is not null , h.handle like CONCAT('%',:handle,'%'), 1=1 ) "

    )
    Page<HandlePageView> objectHandlePage(@Param("dataChannelId") Long dataChannelId,
                                          @Param("name") String name,
                                          @Param("handle") String handle,
                                          Pageable pageable);


    DataChannelEntity findFirstByDataChannelId(Long dataChannelId);

    DataChannelEntity findFirstByDataChannelIdAndAppId(Long dataChannelId, Long appId);

    DataChannelEntity findByDataChannelIdAndAppId(Long dataChannelId, Long appId);

    List<DataChannelEntity> findByObjectHandleId(Long HandleId);


    /**
     * 查询所有数据通道
     *
     * @return 数据通道列表
     */
    @Query(value = "SELECT " +
            "c.id as sourceId, " +
            "c.data_channel_name as dataChannelName, " +
            "h.handle as objectHandle, " +
            "c.object_handle_type as objectHandleType, " +
            "c.data_channel_id as dataChannelId, " +
            "c.data_type as dataType, " +
            "c.resolve_sql as resolveSql, " +
            "c.query_sql as querySql, " +
            "c.created_time as createdTime, " +
            "c.updated_time as updatedTime, " +
            "c.is_deleted as isDeleted, " +
            " SUBSTRING_INDEX(e.ent_prefix, '.', LENGTH(e.ent_prefix) - LENGTH(REPLACE(e.ent_prefix, '.', ''))) AS provincePrefix, " +
            "e.ent_prefix as entPrefix, " +
            "a.handle_code as appHandleCode, " +
            "c.is_share as isShare " +
            "FROM yc_data_channel c " +
            "LEFT JOIN yc_handle h ON c.object_handle_id = h.id AND h.is_deleted = 0 " +
            "LEFT JOIN yc_app_info a ON c.app_id = a.id AND a.is_deleted = 0 " +
            "LEFT JOIN yc_ent_prefix e ON a.prefix_id = e.id AND e.is_deleted = 0 ",
            nativeQuery = true)
    List<ChannelTO> findAllChannels();

    void deleteByEntId(Long entId);
}
