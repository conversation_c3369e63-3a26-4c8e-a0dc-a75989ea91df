package cn.teleinfo.idycprovince.infrastructure.db.repository;

import cn.teleinfo.idycprovince.infrastructure.db.entity.AppHandleAuthGroupEntity;
import cn.teleinfo.idycprovince.infrastructure.db.view.AppAuthGroupPageView;
import org.springframework.data.domain.Page;
import org.springframework.data.domain.Pageable;
import org.springframework.data.jpa.repository.Query;
import org.springframework.data.repository.query.Param;

import java.util.List;

public interface AppHandleAuthGroupRepository extends BaseRepository<AppHandleAuthGroupEntity, Long>{
    List<AppHandleAuthGroupEntity> findAllByAppIdAndAppHandleAuthId(Long currentAppId, Long appHandleAuthId);

    void deleteAllByAppIdAndAppHandleAuthId(Long appId, Long appHandleAuthId);

    @Query(nativeQuery = true,value = "SELECT DISTINCT a.id,a.created_time as createdTime,a.handle_code as handleCode,a.app_name as appName,a.remark,GROUP_CONCAT( ag.auth_group_name ) AS authGroupName  FROM yc_app_handle_auth a LEFT JOIN yc_app_handle_auth_group g ON a.id=g.app_handle_auth_id AND g.is_deleted=0 AND g.app_id= :appId LEFT JOIN yc_auth_group ag ON ag.id=g.auth_group_id AND ag.is_deleted=0 WHERE a.is_deleted=0 " +
            " And a.ent_id = :entId " +
            " AND if(:groupName !='', auth_group_name like CONCAT('%',:groupName,'%') , 1=1) " +
            " AND if(:appName !='', app_name like CONCAT('%',:appName,'%') , 1=1) " +
            " GROUP BY a.id "
            ,
            countQuery = "SELECT count(distinct a.id) FROM yc_app_handle_auth a LEFT JOIN yc_app_handle_auth_group g ON a.id=g.app_handle_auth_id AND g.is_deleted=0 AND g.app_id= :appId LEFT JOIN yc_auth_group ag ON ag.id=g.auth_group_id AND ag.is_deleted=0 WHERE a.is_deleted=0 And a.ent_id = :entId " +
                    " AND if(:groupName !='', auth_group_name like CONCAT('%',:groupName,'%') , 1=1) " +
                    " AND if(:appName !='', app_name like CONCAT('%',:appName,'%') , 1=1) " +
                    "GROUP BY a.id" )
    Page<AppAuthGroupPageView> authPage(@Param("appName") String appName,
                                        @Param("groupName") String groupName,
                                        @Param("appId") Long appId,
                                        @Param("entId") Long entId, Pageable pageable);

    void deleteByAppHandleAuthId(Long appHandleAuthId);

    List<AppHandleAuthGroupEntity> findByAppHandleAuthId(Long appHandleAuthId);

    List<AppHandleAuthGroupEntity> findAllByAuthGroupId(Long authGroupId);


}
