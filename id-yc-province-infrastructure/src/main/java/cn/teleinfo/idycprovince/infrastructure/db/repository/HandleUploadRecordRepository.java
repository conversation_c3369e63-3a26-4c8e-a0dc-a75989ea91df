package cn.teleinfo.idycprovince.infrastructure.db.repository;

import cn.teleinfo.idycprovince.infrastructure.db.entity.HandleUploadRecordEntity;

/***
 * @title HandleUploadRecordRepository
 * @description <TODO description class purpose>
 * <AUTHOR>
 * @version 1.0.0
 * @create 2023/5/15 10:21
 **/
public interface HandleUploadRecordRepository extends BaseRepository<HandleUploadRecordEntity,Long> {


     HandleUploadRecordEntity findByFileAddress(String fileAddress);
}
