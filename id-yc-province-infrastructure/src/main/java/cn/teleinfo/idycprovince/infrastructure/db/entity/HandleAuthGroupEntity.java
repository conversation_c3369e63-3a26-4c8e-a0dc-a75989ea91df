package cn.teleinfo.idycprovince.infrastructure.db.entity;


import lombok.Getter;
import lombok.Setter;
import org.hibernate.annotations.SQLDelete;
import org.hibernate.annotations.SQLDeleteAll;
import org.hibernate.annotations.Where;
import org.springframework.data.annotation.CreatedDate;
import org.springframework.data.annotation.LastModifiedDate;

import javax.persistence.*;
import java.io.Serializable;
import java.time.LocalDateTime;

@Getter
@Setter
@Entity
@Table(name = "yc_handle_auth_group")
@SQLDelete(sql = "update yc_handle_auth_group set is_deleted = null, updated_time = NOW() where id = ?")
@SQLDeleteAll(sql = "update yc_handle_auth_group set is_deleted = null, updated_time = NOW() where id = ?")
@Where(clause = "is_deleted = 0")
public class HandleAuthGroupEntity implements Serializable {


    @Column(name = "app_id")
    private Long appId;

    @Column(name = "handle_id")
    private Long handleId;

    @Column(name = "auth_group_id")
    private Long authGroupId;

    @Id
    @GeneratedValue(strategy = GenerationType.IDENTITY)
    private Long id;

    /**
     * 创建时间
     */
    @CreatedDate
    @Column(name = "created_time")
    private LocalDateTime createdTime;

    /**
     * 更新时间
     */
    @LastModifiedDate
    @Column(name = "updated_time")
    private LocalDateTime updatedTime;

    /**
     * 逻辑删除: 0 未删除 null 已删除
     */
    @Column(name = "is_deleted")
    private Integer isDeleted = 0;


}
