package cn.teleinfo.idycprovince.infrastructure.db.to;

import java.time.LocalDateTime;

public interface SyncHandleReferenceTO {
    /**
     * 获取主键ID
     *
     * @return 主键ID值
     */
    Long getId();

    /**
     * 获取源关联ID
     *
     * @return 源系统关联ID
     */
    Long getSourceId();

    /**
     * 获取记录创建时间
     *
     * @return 创建时间
     */
    LocalDateTime getCreatedTime();

    /**
     * 获取记录更新时间
     *
     * @return 最后更新时间
     */
    LocalDateTime getUpdatedTime();

    /**
     * 获取被关联的标识值
     *
     * @return 被关联的标识字符串
     */
    String getReferenceHandle();

    /**
     * 获取被关联的属性名
     *
     * @return 被关联的属性名称
     */
    String getReferenceHandleProp();

    /**
     * 获取被关联属性索引
     *
     * @return 被关联属性的索引位置
     */
    Integer getReferenceHandlePropIndex();

    /**
     * 获取查询属性名
     *
     * @return 用于查询的属性名称
     */
    String getQueryProp();

    /**
     * 获取查询属性索引
     *
     * @return 查询属性的索引位置
     */
    Integer getQueryPropIndex();

    /**
     * 获取参数属性名
     *
     * @return 参数传递的属性名称
     */
    String getParamProp();

    /**
     * 获取参数属性索引
     *
     * @return 参数属性的索引位置
     */
    Integer getParamPropIndex();

    /**
     * 获取关联的标识属性ID
     *
     * @return 标识属性表主键ID
     */
    Long getHandleItemId();

    /**
     * 获取省级前缀
     *
     * @return 省级标识前缀
     */
    String getProvincePrefix();

    /**
     * 获取企业前缀
     *
     * @return 企业标识前缀
     */
    String getEntPrefix();

    /**
     * 获取应用标识编码
     *
     * @return 应用系统唯一编码
     */
    String getAppHandleCode();

    /**
     * 获取删除状态
     *
     * @return 0-未删除 null-已删除
     */
    Integer getIsDeleted();
}
