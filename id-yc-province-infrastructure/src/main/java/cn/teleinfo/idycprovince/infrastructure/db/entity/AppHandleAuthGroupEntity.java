package cn.teleinfo.idycprovince.infrastructure.db.entity;

import lombok.Getter;
import lombok.Setter;
import org.hibernate.annotations.SQLDelete;
import org.hibernate.annotations.SQLDeleteAll;
import org.hibernate.annotations.Where;

import javax.persistence.*;
import java.io.Serializable;

@Getter
@Setter
@Entity
@Table(name = "yc_app_handle_auth_group")
@SQLDelete(sql = "update yc_app_handle_auth_group set is_deleted = null where id = ?")
@SQLDeleteAll(sql = "update yc_app_handle_auth_group set is_deleted = null where id = ?")
@Where(clause = "is_deleted = 0")
public class AppHandleAuthGroupEntity implements Serializable {

    @Column(name = "app_id")
    private Long appId;

    @Column(name = "app_handle_auth_id")
    private Long appHandleAuthId;

    @Column(name = "auth_group_id")
    private Long authGroupId;

    @Id
    @GeneratedValue(strategy = GenerationType.IDENTITY)
    private Long id;

    /**
     * 逻辑删除: 0 未删除 null 已删除
     */
    @Column(name = "is_deleted")
    private Integer isDeleted = 0;
}
