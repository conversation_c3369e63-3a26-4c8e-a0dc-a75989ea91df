package cn.teleinfo.idycprovince.infrastructure.db.repository;

import cn.teleinfo.idycprovince.infrastructure.db.entity.AuthGroupEntity;
import org.springframework.data.jpa.repository.Query;
import org.springframework.data.repository.query.Param;

import java.util.Collection;
import java.util.List;

public interface AuthGroupRepository extends BaseRepository<AuthGroupEntity, Long>{

    @Query(nativeQuery = true, value = " SELECT " +
            " a.id, " +
            " a.app_id , " +
            " a.ent_id , " +
            " a.auth_group_name , " +
            " a.created_by, "+
            " a.created_time, "+
            " a.updated_by, "+
            " a.updated_time, "+
            " a.is_deleted, "+
            " a.province_id, "+
            " a.type "+
            " FROM " +
            " yc_auth_group a " +
            " WHERE " +
            " a.is_deleted = 0 " +
            " AND if(:authGroupName !='', a.auth_group_name like CONCAT('%',:authGroupName,'%') , 1=1) " +
            " AND if( :types is not null, a.type = :types, 1=1 ) " +
            " AND if(:appId != '' and :appId is not null,a.app_id = :appId, 1=1 )" +
            " order by a.created_time desc " )

    List<AuthGroupEntity> findAuthGroupList(@Param("authGroupName") String authGroupName, @Param("appId") Long appId,@Param("types") Integer types);

    List<AuthGroupEntity> findByAuthGroupNameAndAppId(String authGroupName, Long currentAppId);
}
