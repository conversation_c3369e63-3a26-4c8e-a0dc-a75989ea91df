package cn.teleinfo.idycprovince.infrastructure.db.entity;


import lombok.Getter;
import lombok.Setter;
import org.hibernate.annotations.SQLDelete;
import org.hibernate.annotations.SQLDeleteAll;
import org.hibernate.annotations.Where;

import javax.persistence.Column;
import javax.persistence.Entity;
import javax.persistence.Table;
import java.io.Serializable;

/**
 * <p>
 * 角色表
 * </p>
 *
 * <AUTHOR>
 * @since 2022-11-05
 */

@Getter
@Setter
@Entity
@Table(name = "yc_role")
@SQLDelete(sql = "update yc_role set is_deleted = null, updated_time = NOW()  where id = ?")
@SQLDeleteAll(sql = "update yc_role set is_deleted = null, updated_time = NOW()  where id = ?")
@Where(clause = "is_deleted = 0")
public class RoleEntity extends BaseEntity implements Serializable {

    private static final long serialVersionUID = 1L;


    /**
     * 角色名称
     */
    @Column(name = "role_name")
    private String roleName;

    /**
     * 角色码;后台生成
     */
    @Column(name = "role_code")
    private String roleCode;

    /**
     * 角色描述
     */
    @Column(name = "role_desc")
    private String roleDesc;

    /**
     * 是否启用;0：启用；1：停用
     */
    @Column(name = "enabled")
    private Integer enabled = 0;

    @Column(name = "ent_id")
    private Long entId;

    @Column(name = "sort")
    private Integer sort;

    @Column(name = "undelete")
    private Integer undelete = 0;
    @Column(name = "role_type")
    private Integer roleType;

}
