package cn.teleinfo.idycprovince.infrastructure.db.entity;

import lombok.Data;
import org.hibernate.annotations.SQLDelete;
import org.hibernate.annotations.SQLDeleteAll;
import org.hibernate.annotations.Where;
import org.springframework.data.annotation.LastModifiedBy;

import javax.persistence.Column;
import javax.persistence.Entity;
import javax.persistence.Table;
import javax.persistence.Version;
import java.io.Serializable;
import java.time.LocalDateTime;

/**
 * <AUTHOR>
 */
@Entity
@Data
@SQLDelete(sql = "update yc_handle set is_deleted = null, updated_time = NOW() where id = ? and version = ?")
@SQLDeleteAll(sql = "update yc_handle set is_deleted = null, updated_time = NOW() where id = ? and version = ?")
@Where(clause = "is_deleted = 0")
@Table(name = "yc_handle")
public class HandleEntity extends BaseEntity implements Serializable {

    /**
    *  企业前缀
    */
    @Column(name = "ent_prefix")
    private String entPrefix;

    /**
    *  通配符
    */
    @Column(name = "wildcard")
    private String wildcard;

    /**
    *  标识名称
    */
    @Column(name = "name")
    private String name;

    /**
    *  标识
    */
    @Column(name = "handle")
    private String handle;

    /**
    *  实体类型 1业务实体 2资源实体
    */
    @Column(name = "entity_type")
    private Integer entityType;

    /**
    *  应用id
    */
    @Column(name = "app_id")
    private Long appId;

    /**
    *  企业ID
    */
    @Column(name = "ent_id")
    private Long entId;
    
    /**
     *  最大索引
     */
    @Column(name = "max_field_index")
    private Integer maxFieldIndex;
    
    /**
     *  版本号
     */
    @Version
    @Column(name = "version")
    private Integer version;

    /**
     * 授权属性标识操作人
     */
    @Column(name = "auth_by")
    private Long authBy;

    /**
     * 授权属性标识操作时间
     */
    @Column(name = "auth_time")
    private LocalDateTime authTime;

    /**
     * 实体对象id
     */
    @Column(name = "entity_object_id")
    private String entityObjectId;
    /**
     * 实体对象类型id
     */
    @Column(name = "entity_object_type_id")
    private String entityObjectTypeId;
    /**
     * 标识类型
     */
    @Column(name = "handle_type")
    private Integer handleType;
    /**
     * 标识状态
     */
    @Column(name = "handle_status")
    private Integer handleStatus;
    /**
     * 注册状态
     */
    @Column(name = "register_status")
    private Integer registerStatus;

    /**
     * 主数据类型*
     */
    @Column(name = "master_data_type")
    private Integer masterDataType;

    /**
     * 父级id*
     */
    @Column(name = "parent_id")
    private Long parentId;

    @Column(name = "data_type")
    private Integer dataType;
}