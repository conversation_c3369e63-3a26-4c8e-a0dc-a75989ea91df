package cn.teleinfo.idycprovince.infrastructure.db.repository;

import cn.teleinfo.idycprovince.infrastructure.db.entity.DataServiceEntity;
import cn.teleinfo.idycprovince.infrastructure.db.view.DataServiceView;
import org.springframework.data.jpa.repository.Query;

import java.util.List;
import java.util.Optional;

/**
 * <AUTHOR>
 */
public interface DataServiceRepository extends BaseRepository<DataServiceEntity, Long> {
    
    /**
     * 查询数据服务
     *
     * @param provinceId
     * @param entId
     * @param appId
     * @return 结果
     **/
    List<DataServiceEntity> findByProvinceIdAndEntIdAndAppId(Long provinceId, Long entId, Long appId);
    
    /**
     * 由数据服务名称查询
     *
     * @param dataServiceName
     * @param provinceId
     * @param entId
     * @param appId
     * @return 结果
     **/
    DataServiceEntity findByDataServiceNameAndProvinceIdAndEntIdAndAppId(String dataServiceName, Long provinceId, Long entId, Long appId);
    
    /**
     * 由数据服务地址查询
     *
     * @param serviceAddress
     * @param provinceId
     * @param entId
     * @param appId
     * @return 结果
     **/
    DataServiceEntity findByServiceAddressAndProvinceIdAndEntIdAndAppId(String serviceAddress, Long provinceId, Long entId, Long appId);

    /**
     * 由数据服务UUID查询
     * @param dataServiceUuid
     * @return
     */
    Optional<DataServiceEntity> findByDataServiceUuid(String dataServiceUuid);

    DataServiceEntity findByDataServiceUuidAndAppId(String dataServiceUuid, Long appId);

    void deleteByEntId(Long entId);

    @Query(value = "SELECT DISTINCT " +
            "ep.ent_prefix as entPrefix,ds.id," +
            "ds.version," +
            "ai.id as appId," +
            "ds.data_service_name as dataServiceName," +
            "ds.data_service_type as dataServiceType," +
            "ds.data_service_uuid as dataServiceUuid," +
            "ds.is_deleted as isDeleted," +
            "ds.service_token as serviceToken," +
            "ds.service_address as serviceAddress," +
            "ds.province_id as provinceId," +
            "ds.updated_by as updatedBy," +
            "ds.updated_time as updatedTime," +
            "ds.created_by as createdBy," +
            "pt.prefix as provincePrefix," +
            "ds.created_time as createdTime FROM \n" +
            "yc_data_service ds \n" +
            "left join yc_app_info ai on ds.app_id = ai.id \n" +
            "LEFT join yc_ent_prefix ep on  ep.id = ai.prefix_id " +
            "LEFT join id_yc_ent.yc_province_tenant pt ON ds.province_id = pt.id  ", nativeQuery = true)
    List<DataServiceView> findDataServiceEntity();
}