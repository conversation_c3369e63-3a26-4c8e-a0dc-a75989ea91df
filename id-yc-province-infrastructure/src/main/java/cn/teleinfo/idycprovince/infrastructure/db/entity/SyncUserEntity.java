package cn.teleinfo.idycprovince.infrastructure.db.entity;

import lombok.Data;
import lombok.Getter;
import lombok.Setter;
import org.hibernate.annotations.SQLDelete;
import org.hibernate.annotations.SQLDeleteAll;
import org.hibernate.annotations.Where;
import org.springframework.data.annotation.CreatedDate;
import org.springframework.data.annotation.LastModifiedDate;

import javax.persistence.*;
import java.io.Serializable;
import java.time.LocalDateTime;
import java.util.Date;

@Getter
@Setter
@Entity
@Table(name = "yc_integrated_user")
@SQLDelete(sql = "update yc_integrated_user set is_deleted = null, updated_time = NOW()  where id = ?")
@SQLDeleteAll(sql = "update yc_integrated_user set is_deleted = null, updated_time = NOW()  where id = ?")
@Where(clause = "is_deleted = 0")
public class SyncUserEntity implements Serializable {

    private static final long serialVersionUID = 1L;

    @Id
    @Column(name = "id")
    private Long id;

    @Column(name = "source_id")
    private Long sourceId;


    @Column(name = "ent_prefix")
    private String entPrefix;

    @Column(name = "role_code")
    private String roleCode;

    @Column(name = "province_prefix")
    private String provincePrefix;

    @CreatedDate
    @Column(name = "created_time")
    private LocalDateTime createdTime;

    @Column(name = "uc_open_id")
    private String ucOpenId;


    @Column(name = "updated_time")
    private LocalDateTime updatedTime;

    @Column(name = "username")
    private String username;


    @Column(name = "is_deleted")
    private Integer isDeleted = 0;

    @Column(name = "app_handle_codes")
    private String appHandleCodes;

    @Column(name = "name")
    private String name;
}