package cn.teleinfo.idycprovince.infrastructure.db.entity;

import lombok.Getter;
import lombok.Setter;
import org.hibernate.annotations.SQLDelete;
import org.hibernate.annotations.SQLDeleteAll;
import org.hibernate.annotations.Where;

import javax.persistence.Column;
import javax.persistence.Entity;
import javax.persistence.Table;

/**
 * <AUTHOR>
 */
@Getter
@Setter
@Entity
@Table(name = "yc_object_handle_data_service")
@SQLDelete(sql = "update yc_object_handle_data_service set is_deleted = null, updated_time = NOW()  where id = ?")
@SQLDeleteAll(sql = "update yc_object_handle_data_service set is_deleted = null, updated_time = NOW()  where id = ?")
@Where(clause = "is_deleted = 0")
public class ObjectHandleDataServiceEntity extends BaseEntity {

    /**
     * 数据服务id
     */
    @Column(name = "data_service_id")
    private Long dataServiceId;

    /**
     * 对象标识id
     */
    @Column(name = "object_handle_id")
    private Long objectHandleId;
    
    /**
     * 企业id
     */
    @Column(name = "ent_id")
    private Long entId;

}
