package cn.teleinfo.idycprovince.infrastructure.db.entity;


import javax.persistence.*;

import lombok.Getter;
import lombok.Setter;
import org.hibernate.annotations.SQLDelete;
import org.hibernate.annotations.SQLDeleteAll;
import org.hibernate.annotations.Where;
import org.springframework.data.annotation.CreatedDate;
import org.springframework.data.annotation.LastModifiedDate;

import java.io.Serializable;
import java.time.LocalDateTime;

/**
 * 集成数据通道实体
 */
@Getter
@Setter
@Entity
@Table(name = "yc_integrated_data_channel")
@SQLDelete(sql = "update yc_integrated_data_channel set is_deleted = null, updated_time = NOW()  where id = ?")
@SQLDeleteAll(sql = "update yc_integrated_data_channel set is_deleted = null, updated_time = NOW()  where id = ?")
@Where(clause = "is_deleted = 0")
public class SyncChannelEntity implements Serializable {

    /**
     * 主键ID
     */
    @Id
    @Column(name = "id")
    private Long id;

    /**
     * 通道原始ID
     */
    @Column(name = "source_id")
    private Long sourceId;

    /**
     * 数据通道名称
     */
    @Column(name = "data_channel_name")
    private String dataChannelName;

    /**
     * 所属对象标识编码
     */
    @Column(name = "object_handle")
    private String objectHandle;

    /**
     * 所属对象标识类型
     */
    @Column(name = "object_handle_type")
    private Integer objectHandleType;

    /**
     * 数据通道ID
     */
    @Column(name = "data_channel_id")
    private String dataChannelId;

    /**
     * 实例数据类型
     */
    @Column(name = "data_type")
    private Integer dataType;

    /**
     * 解析SQL
     */
    @Column(name = "resolve_sql")
    private String resolveSql;

    /**
     * 查询SQL
     */
    @Column(name = "query_sql")
    private String querySql;

    /**
     * 创建时间
     */
    @Column(name = "created_time")
    @CreatedDate
    private LocalDateTime createdTime;

    /**
     * 修改时间
     */
    @Column(name = "updated_time")
    @LastModifiedDate
    private LocalDateTime updatedTime;

    /**
     * 是否已删除
     */
    @Column(name = "is_deleted")
    private Integer isDeleted;

    /**
     * 省级前缀
     */
    @Column(name = "province_prefix")
    private String provincePrefix;

    /**
     * 企业前缀
     */
    @Column(name = "ent_prefix")
    private String entPrefix;

    /**
     * 应用身份编码
     */
    @Column(name = "app_handle_code")
    private String appHandleCode;

    /**
     * 是否共享：1-共享 2-未共享
     */
    @Column(name = "is_share")
    private Integer isShare;

    /**
     * 源类型：1-省；2-企业
     */
    @Column(name = "source_type")
    private Integer sourceType;
}
