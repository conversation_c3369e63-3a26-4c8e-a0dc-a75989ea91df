package cn.teleinfo.idycprovince.infrastructure.db.repository;

import cn.teleinfo.idycprovince.infrastructure.db.entity.ObjectHandleEntity;
import cn.teleinfo.idycprovince.infrastructure.db.entity.ReportHandleEntity;

public interface ReportHandleRepository extends BaseRepository<ReportHandleEntity,Long> {

    ReportHandleEntity findByIdAndProvinceId(Long id, Long provinceId);

    ReportHandleEntity findByHandle(String handle);
}