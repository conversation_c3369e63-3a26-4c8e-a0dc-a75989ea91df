package cn.teleinfo.idycprovince.infrastructure.db.entity;

import lombok.Data;
import lombok.Getter;
import lombok.Setter;
import org.hibernate.annotations.SQLDelete;
import org.hibernate.annotations.SQLDeleteAll;
import org.hibernate.annotations.Where;

import javax.persistence.*;
import java.time.LocalDateTime;

/**
 *
 *
 * @Author: liusp
 * @Date: 2023/10/10/17:23
 * @Description:数据库实体类
 */
@Entity
@Getter
@Setter
@SQLDelete(sql = "update yc_data_service_db set is_deleted = null where id = ?")
@SQLDeleteAll(sql = "update yc_data_service_db set is_deleted = null where id = ?")
@Where(clause = "is_deleted = 0")
@Table(name = "yc_data_service_db")
public class DatabaseEntity extends BaseEntity{
    private static final long serialVersionUID = 1L;
    /**
     * 企业ID
     */
    @Column(name = "ent_id")
    private Long entId;
    /**
     * 应用ID
     */
    @Column(name = "app_id")
    private Long appId;
    /**
     * 数据服务ID
     */
    @Column(name = "data_service_id")
    private Long dataServiceId;
    /**
     * 数据源ID
     */
    @Column(name = "data_source_id")
    private Long dataSourceId;
    /** 数据库名称 */
    @Column(name = "database_name")
    private String databaseName ;
    /** 数据库IP */
    @Column(name = "database_ip")
    private String databaseIp ;
    /** 描述 */
    @Column(name = "description")
    private String description ;
//    @Transient
//    List<MetaDataTableEntity> dataBaseTableEntityList;
}
