package cn.teleinfo.idycprovince.infrastructure.db.repository;

import cn.teleinfo.idycprovince.infrastructure.db.entity.HandleMaintainReferenceEntity;

import java.util.List;

/***
 * @title HandleMaintainRepository
 * @description <TODO description class purpose>
 * <AUTHOR>
 * @version 1.0.0
 * @create 2023/3/13 15:48
 **/
public interface HandleMaintainReferenceRepository extends BaseRepository<HandleMaintainReferenceEntity,Long> {

    void deleteByMaintainItemIdIn(List<Long> longs);
    void deleteByMaintainItemId(Long maintainItemId);

    List<HandleMaintainReferenceEntity> findByMaintainItemId(Long maintainItemId);

}
