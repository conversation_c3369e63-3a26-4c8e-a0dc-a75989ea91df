package cn.teleinfo.idycprovince.infrastructure.db.repository;


import cn.teleinfo.idycprovince.infrastructure.db.entity.SyncHandleEntity;
import cn.teleinfo.idycprovince.infrastructure.db.to.SyncHandlePageTO;
import com.abluepoint.summer.mvc.domain.PageResult;
import org.springframework.data.domain.Page;
import org.springframework.data.domain.Pageable;
import org.springframework.data.jpa.repository.Query;
import org.springframework.data.repository.query.Param;

import java.time.LocalDateTime;
import java.util.List;

public interface SyncHandleRepository extends BaseRepository<SyncHandleEntity, Long> {

    @Query(nativeQuery = true, value = "select    id AS id," +
            "    source_id AS sourceId," +
            "    created_time AS createdTime," +
            "    updated_time AS updatedTime," +
            "    ent_prefix AS entPrefix," +
            "    wildcard AS wildcard," +
            "    name AS name," +
            "    handle AS handle," +
            "    entity_type AS entityType," +
            "    app_handle_code AS appHandleCode," +
            "    province_prefix AS provincePrefix," +
            "    is_deleted AS isDeleted from " +
            " yc_integrated_handle h " +
            "WHERE if(:updatedTime !='' AND :updatedTime IS NOT NULL,updated_time > :updatedTime,1=1)",
            countQuery = "select count(*) from " +
                    " yc_integrated_handle h " +
                    "WHERE if(:updatedTime !='' AND :updatedTime IS NOT NULL, updated_time > :updatedTime, 1=1)")
    Page<SyncHandlePageTO> handlePage(@Param("updatedTime") LocalDateTime updatedTime, Pageable pageable);

    @Query( value = "select h from SyncHandleEntity h")
    List<SyncHandleEntity> findSyncHandleEntity();
}