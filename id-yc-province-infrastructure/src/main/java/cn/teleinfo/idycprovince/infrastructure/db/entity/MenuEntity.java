package cn.teleinfo.idycprovince.infrastructure.db.entity;


import lombok.Getter;
import lombok.Setter;
import org.hibernate.annotations.SQLDelete;
import org.hibernate.annotations.SQLDeleteAll;
import org.hibernate.annotations.Where;

import javax.persistence.Column;
import javax.persistence.Entity;
import javax.persistence.Table;
import java.io.Serializable;

/**
 * <p>
 * 菜单表
 * </p>
 *
 * <AUTHOR>
 * @since 2022-11-05
 */

@Getter
@Setter
@Entity
@Table(name = "yc_menu")
@SQLDelete(sql = "update yc_menu set is_deleted = null, updated_time = NOW()  where id = ?")
@SQLDeleteAll(sql = "update yc_menu set is_deleted = null, updated_time = NOW()  where id = ?")
@Where(clause = "is_deleted = 0")
public class MenuEntity extends BaseEntity implements Serializable {

    private static final long serialVersionUID = 1L;


    /**
     * 菜单名称
     */
    @Column(name = "menu_name")
    private String menuName;

    /**
     * 菜单编码
     */
    @Column(name = "menu_code")
    private String menuCode;

    /**
     * 菜单类型;1：页面，2：按钮
     */
    @Column(name = "menu_type")
    private Integer menuType;

    /**
     * 父级id
     */
    @Column(name = "parent_id")
    private Long parentId;

    /**
     * 菜单排序
     */
    @Column(name = "sort")
    private Integer sort;

    /**
     * 菜单图标
     */
    @Column(name = "icon")
    private String icon;

    /**
     * 菜单描述
     */
    @Column(name = "menu_desc")
    private String menuDesc;
}
