package cn.teleinfo.idycprovince.infrastructure.db.entity;

import lombok.Getter;
import lombok.Setter;
import org.hibernate.annotations.SQLDelete;
import org.hibernate.annotations.SQLDeleteAll;
import org.hibernate.annotations.Where;

import javax.persistence.Column;
import javax.persistence.Entity;
import javax.persistence.Table;

/***
 * @title HandleMaintainReference
 * @description 标识维护关联属性表
 * <AUTHOR>
 * @version 1.0.0
 * @create 2023/2/27 11:26
 **/
@Getter
@Setter
@Entity
@Table(name = "yc_handle_maintain_reference")
@SQLDelete(sql = "update yc_handle_maintain_reference set is_deleted = null, updated_time = NOW()  where id = ?")
@SQLDeleteAll(sql = "update yc_handle_maintain_reference set is_deleted = null, updated_time = NOW()  where id = ?")
@Where(clause = "is_deleted = 0")
public class HandleMaintainReferenceEntity extends BaseEntity {

    /**
     *  标识维护属性表ID
     */
    @Column(name = "maintain_item_id")
    private Long maintainItemId;

    /**
     *  关联标识
     */
    @Column(name = "reference_handle")
    private String referenceHandle;

    /**
     *  关联标识属性
     */
    @Column(name = "reference_handle_prop")
    private String referenceHandleProp;

    /**
     *  查询属性
     */
    @Column(name = "query_prop")
    private String queryProp;

    /**
     *  参数属性
     */
    @Column(name = "param_prop")
    private String paramProp;

    /**
     *  企业ID
     */
    @Column(name = "ent_id")
    private Long entId;

}
