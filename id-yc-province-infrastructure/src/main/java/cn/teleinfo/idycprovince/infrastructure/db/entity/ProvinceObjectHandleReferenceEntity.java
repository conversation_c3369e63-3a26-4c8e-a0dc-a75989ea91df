package cn.teleinfo.idycprovince.infrastructure.db.entity;

import java.time.LocalDateTime;

import lombok.Getter;
import lombok.Setter;
import org.hibernate.annotations.SQLDelete;
import org.hibernate.annotations.SQLDeleteAll;
import org.hibernate.annotations.Where;

import javax.persistence.*;

@Getter
@Setter
@Entity
@Table(name = "yc_province_object_handle_reference")
@SQLDelete(sql = "update yc_province_object_handle_reference set is_deleted = null, updated_time = NOW()  where id = ?")
@SQLDeleteAll(sql = "update yc_province_object_handle_reference set is_deleted = null, updated_time = NOW()  where id = ?")
@Where(clause = "is_deleted = 0")
public class ProvinceObjectHandleReferenceEntity extends BaseEntity{

    /**
    *  关联对象
    */
    @Column(name = "reference_object_handle")
    private String referenceObjectHandle;

    /**
    *  查询属性
    */
    @Column(name = "query_prop")
    private String queryProp;

    /**
    *  参数属性
    */
    @Column(name = "param_prop")
    private String paramProp;

    /**
    *  对象标识项ID
    */
    @Column(name = "object_handle_item_id")
    private Long objectHandleItemId;

    /**
    *  企业ID
    */
    @Column(name = "ent_id")
    private Long entId;


}