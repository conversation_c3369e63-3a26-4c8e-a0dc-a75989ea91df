package cn.teleinfo.idycprovince.infrastructure.db.repository;


import cn.teleinfo.idycprovince.infrastructure.db.entity.SyncHandleReferenceEntity;
import cn.teleinfo.idycprovince.infrastructure.db.to.SyncHandleReferenceTO;
import org.springframework.data.jpa.repository.Query;
import org.springframework.data.repository.query.Param;

import java.util.Arrays;
import java.util.List;

public interface SyncHandleReferenceRepository extends BaseRepository<SyncHandleReferenceEntity, Long> {

    @Query(nativeQuery = true, value = "select   id AS id,\n" +
            "    source_id AS sourceId,\n" +
            "    created_time AS createdTime,\n" +
            "    updated_time AS updatedTime,\n" +
            "    reference_handle AS referenceHandle,\n" +
            "    reference_handle_prop AS referenceHandleProp,\n" +
            "    reference_handle_prop_index AS referenceHandlePropIndex,\n" +
            "    query_prop AS queryProp,\n" +
            "    query_prop_index AS queryPropIndex,\n" +
            "    param_prop AS paramProp,\n" +
            "    param_prop_index AS paramPropIndex,\n" +
            "    handle_item_id AS handleItemId,\n" +
            "    province_prefix AS provincePrefix,\n" +
            "    ent_prefix AS entPrefix,\n" +
            "    app_handle_code AS appHandleCode,\n" +
            "    is_deleted AS isDeleted from yc_integrated_handle_reference where handle_item_id IN :itemIds  ")
    List<SyncHandleReferenceTO> findByHandleItemIdIn(@Param("itemIds") List<Long> itemIds);

    @Query(value = "select r from SyncHandleReferenceEntity r  ")
    List<SyncHandleReferenceEntity> findAllReference();
}