package cn.teleinfo.idycprovince.infrastructure.db.repository;

import cn.teleinfo.idycprovince.infrastructure.db.entity.EntPrefixEntity;
import cn.teleinfo.idycprovince.infrastructure.db.view.EntDeploymentVersionView;
import cn.teleinfo.idycprovince.infrastructure.db.view.PrefixManagePageView;
import org.springframework.data.domain.Page;
import org.springframework.data.domain.Pageable;
import org.springframework.data.jpa.repository.Query;
import org.springframework.data.repository.query.Param;

import java.util.Collection;
import java.util.List;

public interface EntPrefixRepository extends BaseRepository<EntPrefixEntity, Long> {

    @Query(nativeQuery = true, value =
            "SELECT * " +
            "FROM " +
            "yc_ent_prefix " +
            "WHERE " +
            "yc_ent_prefix.province_id = :provinceId " +
            "AND if(:entId != '' and :entId is not null,yc_ent_prefix.ent_id = :entId, 1=1 ) " +
            "AND if(:entPrefix != '' and :entPrefix is not null,yc_ent_prefix.ent_prefix = :entPrefix, 1=1 )")
    List<EntPrefixEntity> findByProvinceIdAndEntIdAndEntPrefix(@Param("provinceId") Long provinceId, @Param("entId") Long entId, @Param("entPrefix") String entPrefix);

    EntPrefixEntity findByEntPrefix(String prefix);

    @Query(nativeQuery = true, value =
            "SELECT " +
            " p.id AS id, " +
            " p.ent_prefix AS entPrefix, " +
            " p.state AS state, " +
            " p.created_time AS createdTime, " +
            " p.updated_time AS updatedTime, " +
            " e.org_name AS orgName, " +
            " e.org_code AS orgCode, " +
            " e.parent_org_name AS parentOrgName, " +
            " e.org_addr_province AS orgAddrProvince, " +
            " e.org_addr_city AS orgAddrCity, " +
            " e.org_addr_district AS orgAddrDistrict, " +
            " e.org_address AS orgAddress " +
            "FROM " +
            " yc_ent_prefix p " +
            " LEFT JOIN yc_ent e ON p.ent_id = e.id  " +
            " AND e.is_deleted = 0  " +
            "WHERE " +
            " p.is_deleted = 0 " +
            " AND p.province_id = :provinceId " +
            " AND (:entId IS NULL OR p.ent_id = :entId) " +
            " AND (:entPrefix IS NULL OR p.ent_prefix = :entPrefix)", countQuery =
            "SELECT " +
            " count(*) " +
            "FROM " +
            " yc_ent_prefix p " +
            " LEFT JOIN yc_ent e ON p.ent_id = e.id  " +
            " AND e.is_deleted = 0  " +
            "WHERE " +
            " p.is_deleted = 0 " +
            " AND p.province_id = :provinceId " +
            " AND (:entId IS NULL OR p.ent_id = :entId) " +
            " AND (:entPrefix IS NULL OR p.ent_prefix = :entPrefix)"
    )
    Page<PrefixManagePageView> page(@Param("entPrefix") String entPrefix, @Param("provinceId") Long provinceId, @Param("entId") Long entId, Pageable pageable);


    @Query(nativeQuery = true, value =
            "SELECT " +
                    " p.id AS id, " +
                    " p.ent_prefix AS entPrefix, " +
                    " p.state AS state, " +
                    " p.created_time AS createdTime, " +
                    " p.updated_time AS updatedTime, " +
                    " e.org_name AS orgName, " +
                    " e.org_code AS orgCode, " +
                    " e.parent_org_name AS parentOrgName, " +
                    " e.org_addr_province AS orgAddrProvince, " +
                    " e.org_addr_city AS orgAddrCity, " +
                    " e.org_addr_district AS orgAddrDistrict, " +
                    "edv.version AS version, " +
                    "edv.run_environment AS runEnvironment, " +
                    "edv.deploy_time AS deployTime, " +
                    "edv.report_time AS reportTime, " +
                    " e.org_address AS orgAddress " +
                    "FROM " +
                    " yc_ent_prefix p " +
                    " LEFT JOIN yc_ent e ON p.ent_id = e.id  " +
                    " AND e.is_deleted = 0  " +
                    "left join yc_ent_deployment_version edv on p.ent_prefix = edv.ent_prefix " +
                    "AND edv.is_deleted = 0 " +
                    "WHERE " +
                    " p.is_deleted = 0 " +
                    " AND if(:entPrefix != '' and :entPrefix is not null,p.ent_prefix like CONCAT('%',:entPrefix,'%'), 1=1 )",countQuery =
            "SELECT count(*) " +
                    "FROM yc_ent_prefix p " +
                    "LEFT JOIN yc_ent e ON p.ent_id = e.id AND e.is_deleted = 0 " +
                    "LEFT JOIN yc_ent_deployment_version edv ON p.ent_prefix = edv.ent_prefix AND edv.is_deleted = 0 " +
                    "WHERE p.is_deleted = 0 " +
                    "AND if(:entPrefix != '' and :entPrefix is not null, p.ent_prefix like CONCAT('%',:entPrefix,'%'), 1=1)"
    )
    Page<EntDeploymentVersionView> findEntPrefixEntitiesByPage(@Param("entPrefix") String entPrefix, Pageable pageable);

    /**
     * 查询企业前缀下拉框
     * @param prefix
     * @return
     */
    @Query(nativeQuery = true, value =
            " SELECT " +
                    " a.id, " +
                    " a.created_by, " +
                    " a.created_time, " +
                    " a.updated_by, " +
                    " a.updated_time, " +
                    " a.ent_prefix, " +
                    " a.state, " +
                    " a.ent_id, " +
                    " a.province_id, " +
                    " a.is_deleted " +
                    " FROM " +
                    " yc_ent_prefix AS a " +
                    " WHERE " +
                    " (:prefix IS NULL OR a.ent_prefix LIKE CONCAT('%',:prefix,'%')) AND " +
                    " a.state = 1 AND " +
                    " a.is_deleted = 0 AND " +
                    " a.province_id = :provinceId " +
                    " and a.id not in( SELECT ent_prefix_id from yc_ent_prefix_hosting where is_deleted = 0) " +
                    " and a.id not in( SELECT ent_prefix_id from yc_ent_prefix_config where is_deleted = 0) "
    )
    List<EntPrefixEntity> findByEntPrefixAndState(@Param("prefix") String prefix, @Param("provinceId") Long provinceId);

    List<EntPrefixEntity> findByIdIn(List<Long> ids);

    List<EntPrefixEntity> findByEntId(Long entId);

    boolean existsByEntPrefix(String entPrefix);

    boolean existsByEntPrefixInAndState(Collection<String> entPrefixes, Integer state);

    List<EntPrefixEntity> findByProvinceId(Long provinceId);

    void deleteByEntId(Long entId);
}
