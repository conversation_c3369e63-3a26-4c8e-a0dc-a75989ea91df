package cn.teleinfo.idycprovince.infrastructure.db.repository;

import cn.teleinfo.idycprovince.infrastructure.db.entity.HandleMaintainEntity;
import cn.teleinfo.idycprovince.infrastructure.db.to.HandleMaintainTO;
import org.springframework.data.domain.Page;
import org.springframework.data.domain.Pageable;
import org.springframework.data.jpa.repository.Query;
import org.springframework.data.repository.query.Param;

import java.util.List;

/***
 * @title HandleMaintainRepository
 * @description <TODO description class purpose>
 * <AUTHOR>
 * @version 1.0.0
 * @create 2023/3/13 15:48
 **/
public interface HandleMaintainRepository extends BaseRepository<HandleMaintainEntity, Long> {

    // TODO:状态的取值
    @Query(nativeQuery = true, value =
            " SELECT " +
                    " MIN( hm.org_name ) AS orgName, " +
                    " MIN( hm.`name` ) AS name, " +
                    " MIN( hm.handle ) AS handle, " +
                    " MIN( hm.app_id ) AS appId, " +
                    " MIN( hm.app_name ) AS appName, " +
                    " MIN( hm.updated_by ) AS updatedBy, " +
                    " MAX( hm.last_maintain_time ) AS updatedTime, " +
                    " MIN( hm.maintain_state ) AS maintainState, " +
                    " GROUP_CONCAT( hm.maintain_field ) AS maintainField " +
                    " FROM " +
                    " yc_handle_maintain hm " +
                    " WHERE" +
                    " hm.is_deleted = 0 " +
                    " AND not exists(select 1 " +
                    " from yc_handle_maintain where handle = hm.handle and last_maintain_time > hm.last_maintain_time) " +
                    " AND if(:handle != '' and :handle is not null, hm.handle = :handle , 1=1) " +
                    " AND if(:maintainState != '' and :maintainState is not null, hm.maintain_state = :maintainState , 1=1) " +
                    " AND if(:appName != '' and :appName is not null, hm.app_name like CONCAT('%',:appName,'%') , 1=1)" +
                    " AND if(:description != '' and :description is not null, FIND_IN_SET(:description,hm.maintain_field) > 0, 1=1) " +
                    " AND if(:referenceHandle != '' and :referenceHandle is not null, FIND_IN_SET(:referenceHandle,hm.maintain_field_value) > 0, 1=1) " +
                    " AND if(:appId is not null, hm.app_id = :appId, 1=1  ) " +
                    " AND if(:entId is not null, hm.ent_id = :entId, 1=1  ) " +
                    " GROUP BY hm.handle ",
            countQuery =
                    " SELECT" +
                            " COUNT(*)" +
                            " FROM" +
                            " yc_handle_maintain hm " +
                            " WHERE" +
                            " hm.is_deleted = 0 " +
                            " AND not exists(select 1 " +
                            " from yc_handle_maintain where handle = hm.handle and last_maintain_time > hm.last_maintain_time) " +
                            " AND if(:handle != '' and :handle is not null, hm.handle = :handle , 1=1) " +
                            " AND if(:maintainState != '' and :maintainState is not null, hm.maintain_state = :maintainState , 1=1) " +
                            " AND if(:appName != '' and :appName is not null, hm.app_name like CONCAT('%',:appName,'%') , 1=1) " +
                            " AND if(:description != '' and :description is not null, FIND_IN_SET(:description,hm.maintain_field) > 0, 1=1) " +
                            " AND if(:referenceHandle != '' and :referenceHandle is not null, FIND_IN_SET(:referenceHandle,hm.maintain_field_value) > 0, 1=1) " +
                            " AND if(:appId is not null, hm.app_id = :appId, 1=1  ) " +
                            " AND if(:entId is not null, hm.ent_id = :entId, 1=1  ) " +
                            " GROUP BY hm.handle " )
    Page<HandleMaintainTO> getMaintainPage(@Param("handle") String handle,
                                           @Param("appName") String appName,
                                           @Param("description") String description,
                                           @Param("referenceHandle") String referenceHandle,
                                           @Param("maintainState") String maintainState,
                                           @Param("appId") Long appId,
                                           @Param("entId") Long entId,
                                           @Param("pageable") Pageable pageable);

    HandleMaintainEntity findByHandleAndAppId(String handle, Long appId);
    Boolean existsAllByProvinceId(Long provinceId);

    void deleteAllByHandle(String handle);

    List<HandleMaintainEntity> findByHandleAndEntId(String handle, Long entId);

    List<HandleMaintainEntity> findAllByHandle(String handle);

    HandleMaintainEntity findByHandle(String handle);

}
