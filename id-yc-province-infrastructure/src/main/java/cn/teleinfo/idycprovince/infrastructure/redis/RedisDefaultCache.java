package cn.teleinfo.idycprovince.infrastructure.redis;

import org.springframework.boot.autoconfigure.condition.ConditionalOnProperty;
import org.springframework.data.redis.core.HashOperations;
import org.springframework.data.redis.core.StringRedisTemplate;
import org.springframework.stereotype.Component;

import java.util.*;
import java.util.concurrent.TimeUnit;

/**
 * 基于RedisTemplate写
 * todo: impl
 */
@ConditionalOnProperty(prefix = "app", name = "cache", havingValue = "redis")
@Component
public class RedisDefaultCache implements RedisCache {

    private final StringRedisTemplate stringRedisTemplate;

    public RedisDefaultCache(StringRedisTemplate stringRedisTemplate) {
        this.stringRedisTemplate = stringRedisTemplate;
    }

    @Override
    public Boolean hasKey(String key) {
        return stringRedisTemplate.hasKey(key);
    }

    @Override
    public String get(String key) {
        return stringRedisTemplate.opsForValue().get(key);
    }

    @Override
    public void set(String key, String value) {
        stringRedisTemplate.opsForValue().set(key, value);
    }

    @Override
    public void set(String key, String value, int expiresInSeconds) {
        stringRedisTemplate.opsForValue().set(key, value, expiresInSeconds, TimeUnit.SECONDS);
    }

    /**
     * todo: 更好的方法
     *
     * @param key
     * @return
     */
    @Override
    public boolean exists(String key) {
        return stringRedisTemplate.opsForValue().get(key) != null;
    }

    @Override
    public boolean delete(String key) {
        return stringRedisTemplate.delete(key);
    }

    @Override
    public boolean sAdd(String key, String value) {
        return stringRedisTemplate.opsForSet().add(key, value) == 1L;
    }

    @Override
    public List<String> sMembers(String key, Class clz) {
        Set<String> members = stringRedisTemplate.opsForSet().members(key);
        return new ArrayList<>(members);
    }

    @Override
    public long increment(String key, int val) {
        return stringRedisTemplate.opsForValue().increment(key,val);
    }

    @Override
    public long hIncrement(String key, String field, long step) {
        return stringRedisTemplate.opsForHash().increment(key,field,step);
    }

    @Override
    public Map<String, Object> hEntries(String key) {
        HashOperations<String, String, Object> hashOperations = stringRedisTemplate.opsForHash();
        return hashOperations.entries(key);
    }

    @Override
    public void rename(String oldKey, String newKey) {
        stringRedisTemplate.rename(oldKey,newKey);
    }

    @Override
    public void hPutAll(String key, Map<String, Object> map) {
        stringRedisTemplate.opsForHash().putAll(key, map);
    }

    @Override
    public void expireAt(String key, Date date) {
        stringRedisTemplate.expireAt(key, date);
    }

    @Override
    public Long getTtl(String key) {
        return stringRedisTemplate.getExpire(key);
    }
}
