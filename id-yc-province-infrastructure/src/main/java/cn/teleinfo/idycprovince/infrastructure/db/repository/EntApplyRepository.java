package cn.teleinfo.idycprovince.infrastructure.db.repository;

import cn.teleinfo.idycprovince.infrastructure.db.entity.EntApplyEntity;

import java.util.Collection;

public interface EntApplyRepository extends BaseRepository<EntApplyEntity, Long> {

    EntApplyEntity findTopByEntIdOrderByIdDesc(Long entId);

    boolean existsByEntPrefixInAndAuditStatus(Collection<String> entPrefixes, Integer auditStatus);

    void deleteByEntId(Long entId);
}