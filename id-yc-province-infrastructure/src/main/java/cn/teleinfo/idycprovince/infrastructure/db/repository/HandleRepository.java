package cn.teleinfo.idycprovince.infrastructure.db.repository;

import cn.teleinfo.idycprovince.infrastructure.db.entity.HandleEntity;
import cn.teleinfo.idycprovince.infrastructure.db.to.HandleTO;
import cn.teleinfo.idycprovince.infrastructure.db.to.HandlesTO;
import cn.teleinfo.idycprovince.infrastructure.db.view.*;
import org.springframework.data.domain.Page;
import org.springframework.data.domain.Pageable;
import org.springframework.data.jpa.repository.Modifying;
import org.springframework.data.jpa.repository.Query;
import org.springframework.data.repository.query.Param;
import org.springframework.transaction.annotation.Transactional;

import java.util.Collection;
import java.util.Date;
import java.util.List;

/**
 * <AUTHOR>
 */
public interface HandleRepository extends BaseRepository<HandleEntity, Long> {

    /**
     * 根据标识查询
     *
     * @param handle
     * @return 结果
     **/
    HandleEntity findByHandle(String handle);

    HandleEntity findByHandleAndAppId(String handle, Long appId);

    List<HandleEntity> findByAppId(Long appId);

    @Modifying
    @Query(nativeQuery = true, value =
            "update yc_handle handle set  " +
                    "handle.max_field_index = :maxFieldIndex " +
                    "WHERE " +
                    "handle.id = :id ")
    void updateMaxFieldIndexMaintain(@Param("id") Long id, @Param("maxFieldIndex") Integer maxFieldIndex);

    List<HandleEntity> findAllByEntPrefix(String entPrefix);

    Boolean existsAllByProvinceId(Long provinceId);

    @Query(nativeQuery = true, value = "SELECT " +
            " count( * ) " +
            " FROM\n" +
            " yc_handle_item a" +
            " LEFT JOIN yc_handle_reference b ON a.id = b.handle_item_id" +
            " LEFT JOIN yc_handle c ON c.id = a.handle_id " +
            " WHERE" +
            " a.is_deleted = 0 " +
            " AND b.is_deleted = 0 " +
            " AND if( :fieldIndex != '' and :fieldIndex is not null, a.field_index != :fieldIndex, 1=1) " +
            " AND b.reference_handle = :referenceHandle " +
            " AND c.handle = :handle")
    Integer findCountItemReferenceHandle(@Param("handle") String handle
            , @Param("fieldIndex") Integer fieldIndex
            , @Param("referenceHandle") String referenceHandle);

    @Query(nativeQuery = true, value = "SELECT" +
            " count( * ) " +
            " FROM" +
            " yc_handle a" +
            " LEFT JOIN yc_handle_item b ON a.id = b.handle_id" +
            " LEFT JOIN yc_handle_reference c ON b.id = c.handle_item_id " +
            " WHERE" +
            " a.is_deleted = 0 " +
            " AND b.is_deleted = 0 " +
            " AND c.is_deleted = 0 " +
            " AND c.reference_handle = :referenceHandle " +
            " AND a.id = :handleId")
    Integer checkReferenceHandleIsExist(@Param("referenceHandle") String referenceHandle, @Param("handleId") Long handleId);


    @Query(nativeQuery = true, value = "SELECT DISTINCT " +
            " a.data_channel_name AS dataChannelName," +
            " a.data_channel_id AS dataChannelId," +
            " b.database_name AS databaseName," +
            " b.database_ip AS databaseIp " +
            " FROM " +
            " yc_data_channel a" +
            " LEFT JOIN yc_data_service_db b ON a.database_id = b.id " +
            " WHERE" +
            " a.is_deleted = 0 " +
            " AND b.is_deleted = 0" +
            " AND ( a.object_handle_id = :objectHandleId or (a.is_share = 1 and a.app_id = :appId )) ")
    List<DataChannelListView> dataChannelList(@Param("objectHandleId") Long objectHandleId, @Param("appId") Long appId);


//    @Query(value = "SELECT h " +
//            "FROM HandleEntity h " +
//            "WHERE h.parentId is null and h.dataType in(1,2) and" +
//            "(:name IS NULL OR h.name LIKE %:name%) AND " +
//            "(:handle IS NULL OR h.handle = :handle) AND " +
//            "(:startTime IS NULL OR h.updatedTime >= :startTime) AND " +
//            "(:endTime IS NULL OR h.updatedTime <= :endTime) AND " +
//            "(:provinceId IS NULL OR h.provinceId = :provinceId) AND " +
//            "(:entId IS NULL OR h.entId = :entId) AND " +
//            "(:currentAppId IS NULL OR h.appId = :currentAppId)")


    @Query(nativeQuery = true, value = "SELECT" +
            " a.id as id, " +
            " a.app_id as appId, " +
            " a.handle as handle, " +
            " a.`name` as name, " +
            " a.updated_time as updatedTime, " +
            " a.handle_status as handleStatus, " +
            " a.register_status as registerStatus, " +
            " a.handle_type as handleType " +
            " FROM" +
            " yc_handle a " +
            " WHERE" +
            " a.parent_id IS NULL " +
            " AND a.is_deleted = 0 " +
            " AND a.data_type IN ( 1, 2 ) " +
            " AND if(:name !='' , a.`name` like CONCAT('%',:name,'%'), 1=1 )  " +
            " AND if(:handle !='' , a.`handle` like CONCAT('%',:handle,'%') , 1=1 ) " +
            " AND if(:startTime is not null , a.updated_time >= :startTime , 1=1)  " +
            " AND if(:endTime is not null , a.updated_time <= :endTime , 1=1)  " +
            " AND a.province_id = :provinceId " +
            " AND a.ent_id = :entId " +
            " AND a.app_id = :currentAppId ", countQuery = "SELECT" +
            " count(*) " +
            " FROM" +
            " yc_handle a " +
            " WHERE" +
            " a.parent_id IS NULL " +
            " AND a.is_deleted = 0 " +
            " AND a.data_type IN ( 1, 2 ) " +
            " AND if(:name !='' , a.`name` like CONCAT('%',:name,'%'), 1=1 )  " +
            " AND if(:handle !='' , a.`handle` like CONCAT('%',:handle,'%') , 1=1 ) " +
            " AND if(:startTime is not null , a.updated_time >= :startTime , 1=1)  " +
            " AND if(:endTime is not null , a.updated_time <= :endTime , 1=1)  " +
            " AND a.province_id = :provinceId " +
            " AND a.ent_id = :entId " +
            " AND a.app_id = :currentAppId ")
    Page<HandlePageView> page(@Param("name") String name,
                              @Param("handle") String handle,
                              @Param("startTime") Date startTime,
                              @Param("endTime") Date endTime,
                              @Param("provinceId") Long provinceId,
                              @Param("entId") Long entId,
                              @Param("currentAppId") Long currentAppId,
                              Pageable pageable);

    @Query(nativeQuery = true, value = "SELECT" +
            " a.id AS id," +
            " a.`name` AS `name`," +
            " a.handle AS handle," +
            " a.app_id AS appId," +
            " a.updated_time AS updatedTime " +
            " FROM" +
            " yc_handle a " +
            " WHERE" +
            " a.parent_id = :parentId " +
            " AND a.is_deleted = 0 " +
            " AND a.province_id = :provinceId " +
            " AND a.ent_id = :entId " +
            " AND a.app_id = :currentAppId", countQuery = "SELECT" +
            " count(*)" +
            " FROM" +
            " yc_handle a " +
            " WHERE" +
            " a.parent_id = :parentId " +
            " AND a.is_deleted = 0 " +
            " AND a.province_id = :provinceId " +
            " AND a.ent_id = :entId " +
            " AND a.app_id = :currentAppId")
    Page<HandlePageView> masterPage(@Param("parentId") Long parentId,
                                    @Param("provinceId") Long provinceId,
                                    @Param("entId") Long entId,
                                    @Param("currentAppId") Long currentAppId,
                                    Pageable pageable);


    @Query(nativeQuery = true, value = "SELECT" +
            " a.AC_CGT_PACK_CODE as handle," +
            " b.AC_CGT_NAME as `name`" +
            " FROM" +
            " TZ_AC_CIGARETTE_SPECS_PACK a" +
            " INNER JOIN TZ_AC_CIGARETTE b ON a.AC_CGT_CODE = b.AC_CGT_CODE " +
            " WHERE" +
            " a.AC_CGT_PACK_CODE LIKE 'AC1%' ", countQuery = "SELECT\n" +
            " count(*)" +
            " FROM" +
            " TZ_AC_CIGARETTE_SPECS_PACK a" +
            " INNER JOIN TZ_AC_CIGARETTE b ON a.AC_CGT_CODE = b.AC_CGT_CODE " +
            " WHERE" +
            " a.AC_CGT_PACK_CODE LIKE 'AC1%'")
    Page<MasterDataHandleView> getMasterDataListH(Pageable pageable);

    @Query(nativeQuery = true, value = "SELECT" +
            " a.AC_CGT_PACK_CODE as handle," +
            " b.AC_CGT_NAME as `name`" +
            " FROM" +
            " TZ_AC_CIGARETTE_SPECS_PACK a" +
            " INNER JOIN TZ_AC_CIGARETTE b ON a.AC_CGT_CODE = b.AC_CGT_CODE " +
            " WHERE" +
            " a.AC_CGT_PACK_CODE LIKE 'AC2%'", countQuery = "SELECT" +
            " count(*)" +
            " FROM" +
            " TZ_AC_CIGARETTE_SPECS_PACK a" +
            " INNER JOIN TZ_AC_CIGARETTE b ON a.AC_CGT_CODE = b.AC_CGT_CODE " +
            " WHERE" +
            " a.AC_CGT_PACK_CODE LIKE 'AC2%'")
    Page<MasterDataHandleView> getMasterDataListT(Pageable pageable);

    @Query(nativeQuery = true, value = "SELECT" +
            " a.AC_CGT_PACK_CODE as handle," +
            " b.AC_CGT_NAME as `name`" +
            " FROM" +
            " TZ_AC_CIGARETTE_SPECS_PACK a" +
            " INNER JOIN TZ_AC_CIGARETTE b ON a.AC_CGT_CODE = b.AC_CGT_CODE " +
            " WHERE" +
            " a.AC_CGT_PACK_CODE LIKE 'AC3%'", countQuery = "SELECT" +
            " count(*)" +
            " FROM" +
            " TZ_AC_CIGARETTE_SPECS_PACK a" +
            " INNER JOIN TZ_AC_CIGARETTE b ON a.AC_CGT_CODE = b.AC_CGT_CODE " +
            " WHERE" +
            " a.AC_CGT_PACK_CODE LIKE 'AC3%'")
    Page<MasterDataHandleView> getMasterDataListJ(Pageable pageable);

    @Query(nativeQuery = true, value = "SELECT" +
            " AA_LEAF_RT_NAME as `name`," +
            " AA_LEAF_RT_CODE as handle" +
            " FROM" +
            " TZ_AA1_RAW_TOBACCO_LEAF", countQuery = "SELECT\n" +
            " count(*)" +
            " FROM" +
            " TZ_AA1_RAW_TOBACCO_LEAF")
    Page<MasterDataHandleView> getMasterDataListY(Pageable pageable);

    @Query(nativeQuery = true, value = "SELECT" +
            " AA_LEAF_FT_CODE as handle," +
            " AA_LEAF_FT_NAME as `name`" +
            " FROM" +
            " TZ_AA2_TOBACCO_LEAF", countQuery = "SELECT" +
            " count(*) " +
            " FROM" +
            " TZ_AA2_TOBACCO_LEAF")
    Page<MasterDataHandleView> getMasterDataListCP(Pageable pageable);

    List<HandleEntity> findByHandleTypeAndAppIdAndDataType(Integer handleType, Long appId, Integer dataType);

    List<HandleEntity> findByHandleTypeAndAppIdAndParentIdAndDataType(Integer handleType, Long appId, Long parentId, Integer dataType);

    /**
     * 物理删除ParentId下所有的对象标识
     *
     * @param handleParentId
     */
    @Transactional
    @Modifying
    @Query(nativeQuery = true, value = "DELETE FROM yc_handle WHERE parent_id = :handleParentId")
    void deleteByHandleParentIdPhysical(@Param("handleParentId") Long handleParentId);

    HandleEntity findByMasterDataType(Integer masterDataType);

    boolean existsByMasterDataType(Integer masterDataType);

    List<HandleEntity> findByAppIdAndDataType(Long appId, Integer dataType);

    List<HandleEntity> findByParentId(Long parentId);

    @Query(nativeQuery = true, value = "SELECT " +
            "    h.ent_prefix AS entPrefix, " +
            "    COUNT(IF(h.is_deleted = 0 AND h.handle_status = 2 AND ( p.is_deleted = 0), 1, NULL)) AS handleRegisterNumCount, "
            +
            "    DATE_FORMAT(h.created_time, '%Y%m') AS currentMonth, " +
            "    DATE_FORMAT(h.created_time, '%Y%m%d') AS currentDay " +
            "FROM yc_handle h " +
            "LEFT JOIN yc_ent_prefix p ON h.ent_prefix = p.ent_prefix " +
            "WHERE h.ent_prefix IS NOT NULL " +
            "GROUP BY " +
            "    h.ent_prefix, " +
            "    currentMonth, " +
            "    currentDay")
    List<HandleRegisterNumListView> countHandleTotalRegisterNum();

    @Query(nativeQuery = true, value = "SELECT " +
            "COUNT(*) AS handleRegisterNumCount " +
            "FROM " +
            "yc_handle yh " +
            "LEFT JOIN yc_ent_prefix yep ON yh.ent_prefix = yep.ent_prefix " +
            "WHERE " +
            "yh.ent_prefix IS NOT NULL " +
            "AND yh.ent_prefix != '' " +
            "AND yep.is_deleted = 0 " +
            "AND yh.is_deleted = 0 " +
            "AND yh.handle_status = 2 " +
            "AND yh.created_time < :dateTimeStr " +
            "AND yep.province_id = :provinceId "
    )
    Long getProvinceHostingRegCount(@Param("provinceId") Long provinceId, @Param("dateTimeStr") String dateTimeStr);

    @Query(nativeQuery = true, value = "SELECT " +
            " COUNT(*) AS handleRegisterNumCount " +
            " FROM " +
            " yc_handle yh " +
            " LEFT JOIN yc_ent_prefix yep ON yh.ent_prefix = yep.ent_prefix  " +
            " WHERE " +
            " yh.ent_prefix IS NOT NULL  " +
            " AND yh.ent_prefix != ''  " +
            " AND yep.is_deleted = 0  " +
            " AND yh.is_deleted = 0  " +
            " AND yh.handle_status = 2  " +
            " AND yh.created_time < :dateTimeStr  " +
            " AND yep.province_id = :provinceId " +
            " AND yep.ent_id = :entId ")
    Long getEntRegCount(@Param("provinceId") Long provinceId, @Param("entId") Long entId, @Param("dateTimeStr") String dateTimeStr);

    @Query(nativeQuery = true, value = "SELECT " +
            "COUNT(*) AS handleRegisterNumCount " +
            "FROM " +
            "yc_handle yh " +
            "LEFT JOIN yc_ent_prefix yep ON yh.ent_prefix = yep.ent_prefix " +
            "WHERE " +
            "yh.ent_prefix IS NOT NULL " +
            "AND yh.ent_prefix != '' " +
            "AND yh.is_deleted = 0 " +
            "AND yh.handle_status = 2 " +
            "AND yep.province_id = :provinceId " +
            "AND if(:mouth !='' AND :mouth IS NOT NULL,DATE_FORMAT(yh.created_time, '%Y%m') = :mouth,1=1) " +
            "AND yep.is_deleted = 0 ")
    HandleRegisterNumListView countProvinceHandleNumCountAll(@Param("provinceId") Long provinceId, @Param("mouth") String mouth);

    @Query(nativeQuery = true, value = "SELECT " +
            "yh.ent_prefix AS entPrefix, " +
            "COUNT(*) AS handleRegisterNumCount " +
            "FROM " +
            "yc_handle yh " +
            "LEFT JOIN yc_ent_prefix yep ON yh.ent_prefix = yep.ent_prefix " +
            "WHERE " +
            "yep.is_deleted = 0 " +
            "AND yh.is_deleted = 0 " +
            "AND yh.handle_status = 2 " +
            "AND yh.created_time < :dateTimeStr " +
            "AND yep.province_id = :provinceId " +
            "GROUP BY entPrefix")
    List<HandleRegisterNumListView> countProvinceEntPrefixHandleNumCount(@Param("provinceId") Long provinceId, @Param("dateTimeStr") String dateTimeStr);


    @Query(nativeQuery = true, value = "SELECT" +
            " a.id as id, " +
            " a.app_id as appId, " +
            " a.handle as handle, " +
            " a.`name` as name, " +
            " a.updated_time as updatedTime, " +
            " a.handle_status as handleStatus, " +
            " a.register_status as registerStatus, " +
            " a.handle_type as handleType " +
            " FROM" +
            " yc_handle a " +
            " WHERE" +
            " a.parent_id IS NULL " +
            " AND a.is_deleted = 0 " +
            " AND a.data_type IN ( 1, 2 ) " +
            " AND a.handle_status = 2 " +
            " AND a.id not in (:handleIds)  " +
            " AND if(:name !='' , a.`name` like CONCAT('%',:name,'%'), 1=1 )  " +
            " AND if(:handle !='' , a.`handle` like CONCAT('%',:handle,'%') , 1=1 ) " +
            " AND a.province_id = :provinceId " +
            " AND a.ent_id = :entId " +
            " AND a.app_id = :currentAppId ", countQuery = "SELECT" +
            " count(*) " +
            " FROM" +
            " yc_handle a " +
            " WHERE" +
            " a.parent_id IS NULL " +
            " AND a.is_deleted = 0 " +
            " AND a.data_type IN ( 1, 2 ) " +
            " AND a.handle_status = 2 " +
            " AND a.id not in (:handleIds)  " +
//            " AND if(:handleIds !='' , a.handle = :handle , 1=1 ) " +
            " AND if(:name !='' , a.`name` like CONCAT('%',:name,'%'), 1=1 )  " +
            " AND if(:handle !='' , a.`handle` like CONCAT('%',:handle,'%') , 1=1 ) " +
            " AND a.province_id = :provinceId " +
            " AND a.ent_id = :entId " +
            " AND a.app_id = :currentAppId ")
    Page<HandlePageView> handleGroupPage(@Param("name") String name,
                                         @Param("handle") String handle,
                                         @Param("provinceId") Long provinceId,
                                         @Param("entId") Long entId,
                                         @Param("currentAppId") Long currentAppId,
                                         @Param("handleIds") List<Long> handleIds,
                                         Pageable pageable);


    @Query(nativeQuery = true,
            value = "select h.id AS id, " +
                    "    h.created_time AS createdTime, " +
                    "    h.updated_time AS updatedTime, " +
                    "    h.ent_prefix AS entPrefix, " +
                    "    h.wildcard AS wildcard, " +
                    "    h.name AS name, " +
                    "    h.handle AS handle, " +
                    "    h.entity_type AS entityType, " +
                    "    a.handle_code AS appHandleCode, " +
                    "    SUBSTRING_INDEX(h.ent_prefix, '.', LENGTH(h.ent_prefix) - LENGTH(REPLACE(h.ent_prefix, '.', ''))) AS provincePrefix, " +
                    "    h.is_deleted AS isDeleted " +
                    "from yc_handle h " +
                    "left join yc_app_info a on a.id = h.app_id and a.is_deleted = 0 ",
            countQuery = "SELECT COUNT(*) FROM yc_handle h LEFT JOIN yc_app_info a ON a.id = h.app_id and a.is_deleted = 0")
    Page<HandleTO> findSyncHandle(Pageable pageable);

    @Query(nativeQuery = true,
            value = "select h.id AS id, " +
                    "    h.created_time AS createdTime, " +
                    "    h.updated_time AS updatedTime, " +
                    "    h.ent_prefix AS entPrefix, " +
                    "    h.wildcard AS wildcard, " +
                    "    h.name AS name, " +
                    "    h.handle AS handle, " +
                    "    h.entity_type AS entityType, " +
                    "    a.handle_code AS appHandleCode, " +
                    "    SUBSTRING_INDEX(h.ent_prefix, '.', LENGTH(h.ent_prefix) - LENGTH(REPLACE(h.ent_prefix, '.', ''))) AS provincePrefix, " +
                    "    h.is_deleted AS isDeleted " +
                    "from yc_handle h " +
                    "left join yc_app_info a on a.id = h.app_id and a.is_deleted = 0 ")
    List<HandleTO> findSyncHandles();

    void deleteByEntIdAndEntPrefixIn(Long entId, Collection<String> entPrefixes);

    @Query(nativeQuery = true,
            value = "SELECT " +
                    "h.id AS id, " +
                    "h.name AS handleName, " +
                    "h.handle AS handle, " +
                    "h.entity_type as entityType, " +
                    "h.updated_time as updateTime, " +
                    "a.app_name as appName " +
                    "from yc_handle h " +
                    "left join yc_app_info a on h.app_id = a.id and a.is_deleted = 0 " +
                    "where IF(:handle != '' and :handle IS NOT NULL, h.handle = :handle, 1=1 ) " +
                    "and IF(:name != '' AND :name is not null, h.name like CONCAT('%',:name,'%'), 1=1 ) " +
                    "and IF(:appHandleCode != '' AND :appHandleCode is not null, a.handle_code = :appHandleCode, 1=1 ) " +
                    "AND h.is_deleted = 0",
            countQuery = "SELECT count(*) " +
                    "from yc_handle h " +
                    "left join yc_app_info a on h.app_id = a.id and a.is_deleted = 0 " +
                    "where IF(:handle != '' and :handle IS NOT NULL, h.handle = :handle, 1=1 ) " +
                    "and IF(:name != '' AND :name is not null, h.name like CONCAT('%',:name,'%'), 1=1 ) " +
                    "and IF(:appHandleCode != '' AND :appHandleCode is not null, a.handle_code = :appHandleCode, 1=1 ) " +
                    "AND h.is_deleted = 0"
    )
    Page<HandlesPageView> findListHandles(@Param("name") String name, @Param("handle")String handle, @Param("appHandleCode")String appHandleCode, Pageable pageable);

    @Query(nativeQuery = true,
            value = "select " +
                    "a.id as id, " +
                    "a.name as name, " +
                    "a.handle as handle, " +
                    "a.entity_type as entityType, " +
                    "b.node_name as provinceName, " +
                    "c.org_name as entName, " +
                    "d.app_name as appName " +
                    "from yc_handle a " +
                    "left join yc_province_tenant b on a.province_id = b.id " +
                    "left join yc_ent c on a.ent_id = c.id and c.is_deleted = 0 " +
                    "left join yc_app_info d on a.app_id = d.id and d.is_deleted = 0 " +
                    "where a.handle = :handle and a.is_deleted = 0 "
    )
    HandlesTO findHandlesDetailByHandle(@Param("handle") String handle);

    @Query(nativeQuery = true,
            value = "select " +
                    "a.id as id, " +
                    "a.name as name, " +
                    "a.handle as handle, " +
                    "a.entity_type as entityType, " +
                    "b.node_name as provinceName, " +
                    "c.org_name as entName, " +
                    "d.app_name as appName " +
                    "from yc_handle a " +
                    "left join yc_province_tenant b on a.province_id = b.id " +
                    "left join yc_ent c on a.ent_id = c.id and c.is_deleted = 0 " +
                    "left join yc_app_info d on a.app_id = d.id and d.is_deleted = 0 " +
                    "where IF(:handle != '' and :handle is not null, " +
                    "a.name like CONCAT('%',:handle,'%') or a.handle like CONCAT('%',:handle,'%'), 1=1) " +
                    "and a.is_deleted = 0 "
    )
    List<HandlesTO> getGraphHandlesByHandle(@Param("handle") String handle);


    // 查询前缀标识是否存在
    @Query(nativeQuery = true, value = "select count(1) from yc_handle where handle like concat(:prefix,'%') and is_deleted = 0")
    Long findCountByHandlePrefix(@Param("prefix") String prefix);
}