package cn.teleinfo.idycprovince.infrastructure.db.view;

import java.time.LocalDateTime;

public interface DataServiceView {

    Long getId();

    String getDataServiceName();

    LocalDateTime getCreatedTime();

    Long getCreatedBy();

    LocalDateTime getUpdatedTime();

    Long getUpdatedBy();

    Long getProvinceId();

    String getEntPrefix();

    String getAppHandleCode();

    String getServiceAddress();

    String getServiceToken();

    String getDataServiceUuid();

    Integer getDataServiceType();

    String getVersion();

    Integer getIsDeleted();

    Long getAppId();

    String getProvincePrefix();
    
}
