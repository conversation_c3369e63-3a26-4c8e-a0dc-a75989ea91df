package cn.teleinfo.idycprovince.infrastructure.db.to;

import java.time.LocalDateTime;

/**
 * 数据通道传输对象接口
 */
public interface ChannelTO {

    /**
     * 获取通道原始ID
     */
    Long getSourceId();

    /**
     * 获取数据通道名称
     */
    String getDataChannelName();

    /**
     * 获取所属对象标识编码
     */
    String getObjectHandle();

    /**
     * 获取所属对象标识类型
     */
    Integer getObjectHandleType();

    /**
     * 获取数据通道ID
     */
    String getDataChannelId();

    /**
     * 获取实例数据类型
     */
    Integer getDataType();

    /**
     * 获取解析SQL
     */
    String getResolveSql();

    /**
     * 获取查询SQL
     */
    String getQuerySql();

    /**
     * 获取创建时间
     */
    LocalDateTime getCreatedTime();

    /**
     * 获取更新时间
     */
    LocalDateTime getUpdatedTime();

    /**
     * 获取是否删除
     */
    Integer getIsDeleted();

    /**
     * 获取省级前缀
     */
    String getProvincePrefix();

    /**
     * 获取企业前缀
     */
    String getEntPrefix();

    /**
     * 获取应用身份编码
     */
    String getAppHandleCode();

    /**
     * 获取是否共享
     */
    Integer getIsShare();

    /**
     * 获取源类型
     */
    Integer getSourceType();
}
