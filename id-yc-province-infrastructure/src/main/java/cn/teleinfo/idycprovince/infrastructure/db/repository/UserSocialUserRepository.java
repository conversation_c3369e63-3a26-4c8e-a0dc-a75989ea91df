package cn.teleinfo.idycprovince.infrastructure.db.repository;

import cn.teleinfo.idycprovince.infrastructure.db.entity.UserSocialUserEntity;

/**
 * <AUTHOR>
 */
public interface UserSocialUserRepository extends BaseRepository<UserSocialUserEntity, Long> {
    
    /**
     * 根据用户id查询关联
     *
     * @param userId
     * @return 结果
     **/
    UserSocialUserEntity findByUserId(Long userId);
    
    /**
     * 由用户中台用户id删除
     *
     * @param socialUserId
     * @return 结果
     **/
    void deleteBySocialUserId(Long socialUserId);
    
    /**
     * 根据用户中心id查询关联
     *
     * @param socialUserId
     * @return 结果
     **/
    UserSocialUserEntity findBySocialUserId(Long socialUserId);

    UserSocialUserEntity findBySocialUserId(String socialUserId);
}
