package cn.teleinfo.idycprovince.infrastructure.db.entity;

import lombok.Data;

import javax.persistence.*;
import java.time.LocalDateTime;

@Entity
@Data
@Table(name = "yc_ent_deployment_version")
public class EntDeploymentVersionEntity {

    @Id
    @Column(name = "id")
    private Long id;

    @Column(name = "version", nullable = false, length = 50)
    private String version;

    @Column(name = "deploy_time", nullable = false)
    private String deployTime;

    @Column(name = "created_time", nullable = false, updatable = false)
    private LocalDateTime createdTime;

    @Column(name = "updated_time", nullable = false)
    private LocalDateTime updatedTime;

    @Column(name = "ent_prefix", nullable = false, length = 100)
    private String entPrefix;

    @Column(name = "is_deleted", nullable = false)
    private Integer isDeleted;

    @Column(name = "source_id")
    private Long sourceId;

    @Column(name = "run_environment")
    private String runEnvironment;

    @Column(name = "report_time")
    private LocalDateTime reportTime;
}