package cn.teleinfo.idycprovince.infrastructure.db.repository;

import cn.teleinfo.idycprovince.infrastructure.db.entity.HandleEntity;
import cn.teleinfo.idycprovince.infrastructure.db.entity.MetaDataEntity;
import cn.teleinfo.idycprovince.infrastructure.db.view.MetaDataPageView;
import com.abluepoint.summer.mvc.domain.PageResult;
import org.springframework.data.domain.Page;
import org.springframework.data.domain.Pageable;
import org.springframework.data.jpa.repository.Query;
import org.springframework.data.repository.query.Param;

/**
 *
 *
 * @Author: liusp
 * @Date: 2023/10/16/15:58
 * @Description:
 */
public interface MetaDataRepository extends BaseRepository<MetaDataEntity,Long> {
    @Query(nativeQuery = true,value = "SELECT " +
            "m.id AS id, " +
            "d.database_name AS databaseName, " +
            "s.data_service_name AS dataServiceName, " +
            "d.database_ip AS databaseIp, " +
            "m.description AS description, " +
            "m.updated_time AS updatedTime " +
            "FROM " +
            "yc_metadata m " +
            "LEFT JOIN yc_data_service_db d ON m.data_service_db_id = d.id " +
            "LEFT JOIN yc_data_service s ON d.data_service_id = s.id " +
            "WHERE " +
            "(:databaseName IS NULL OR d.database_name LIKE CONCAT('%',:databaseName,'%')) " +
            "AND (:startTime IS NULL OR DATE_FORMAT(m.updated_time,'%Y-%m-%d %H:%i:%s') >= :startTime)  " +
            "AND (:endTime IS NULL OR DATE_FORMAT(m.updated_time,'%Y-%m-%d %H:%i:%s') <= :endTime)  " +
            "AND (:provinceId IS NULL OR m.province_id = :provinceId) " +
            "AND (:entId IS NULL OR m.ent_id = :entId) " +
            "AND (:appId IS NULL OR m.app_id = :appId) " +
            "AND m.is_deleted = 0",countQuery = "SELECT count(*)" +
            "FROM " +
            "yc_metadata m " +
            "LEFT JOIN yc_data_service_db d ON m.data_service_db_id = d.id " +
            "LEFT JOIN yc_data_service s ON d.data_service_id = s.id " +
            "WHERE " +
            "(:databaseName IS NULL OR d.database_name LIKE CONCAT('%',:databaseName,'%')) " +
            "AND (:startTime IS NULL OR DATE_FORMAT(m.updated_time,'%Y-%m-%d %H:%i:%s') >= :startTime)  " +
            "AND (:endTime IS NULL OR DATE_FORMAT(m.updated_time,'%Y-%m-%d %H:%i:%s') <= :endTime)  " +
            "AND (:provinceId IS NULL OR m.province_id = :provinceId) " +
            "AND (:entId IS NULL OR m.ent_id = :entId) " +
            "AND (:appId IS NULL OR m.app_id = :appId) " +
            "AND m.is_deleted = 0")
    Page<MetaDataPageView> pageQuery(@Param("databaseName")String databaseName,
                                           @Param("startTime") String startTime,
                                           @Param("endTime") String endTime,
                                           @Param("provinceId") Long provinceId,
                                           @Param("entId") Long entId,
                                           @Param("appId") Long appId,
                                           Pageable pageable);

    boolean existsByDatabaseId(Long databaseId);

}
