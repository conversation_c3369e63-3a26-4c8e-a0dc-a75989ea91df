package cn.teleinfo.idycprovince.infrastructure.db.repository;

import cn.teleinfo.idycprovince.infrastructure.db.entity.HandleAutoMaintainStateEntity;

import java.util.List;


public interface HandleAutoMaintainStateRepository extends BaseRepository<HandleAutoMaintainStateEntity, Long> {

    HandleAutoMaintainStateEntity findByHandleAndFieldAndReferenceHandle(
            String handle, String field, String referenceHandle
    );

    List<HandleAutoMaintainStateEntity> findByState(Integer state);
}
