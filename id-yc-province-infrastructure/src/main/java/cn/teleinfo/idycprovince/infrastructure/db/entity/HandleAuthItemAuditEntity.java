package cn.teleinfo.idycprovince.infrastructure.db.entity;

import lombok.Data;
import org.hibernate.annotations.SQLDelete;
import org.hibernate.annotations.SQLDeleteAll;
import org.hibernate.annotations.Where;

import javax.persistence.Column;
import javax.persistence.Entity;
import javax.persistence.Table;
import java.time.LocalDateTime;

@Entity
@Data
@SQLDelete(sql = "update yc_handle_auth_item_audit set is_deleted = null, updated_time = NOW() where id = ? ")
@SQLDeleteAll(sql = "update yc_handle_auth_item_audit set is_deleted = null, updated_time = NOW() where id = ? ")
@Where(clause = "is_deleted = 0")
@Table(name = "yc_handle_auth_item_audit")
public class HandleAuthItemAuditEntity extends BaseEntity{

    /**
     * 英文名称*
     */
    @Column(name = "field")
    private String field;

    /**
     * 中文名称*
     */
    @Column(name = "description")
    private String description;

    /**
     * 属性类型：1固定值，2标识解析数据源，3关联标识，4关联属性*
     */
    @Column(name = "field_type")
    private Integer fieldType;

    /**
     * 管理员写：0不可写，1可写*
     */
    @Column(name = "admin_write")
    private Integer adminWrite;

    /**
     * 管理员读：0不可读，1可读*
     */
    @Column(name = "admin_read")
    private Integer adminRead;

    /**
     * 公共读：0不可读，1可读*
     */
    @Column(name = "public_read")
    private Integer publicRead;

    /**
     * 公共写：0不可写，1可写*
     */
    @Column(name = "public_write")
    private Integer publicWrite;

    /**
     * 审核记录id*
     */
    @Column(name = "handle_audit_id")
    private Long handleAuditId;

    /**
     * 属性值*
     */
    @Column(name = "field_value")
    private String fieldValue;

    /**
     * 数据来源类型：0基础属性，1扩展属性*
     */
    @Column(name = "field_source_type")
    private Integer fieldSourceType;

    /**
     * 备注*
     */
    @Column(name = "remark")
    private String remark;

    /**
     * 企业id*
     */
    @Column(name = "ent_id")
    private Long entId;
}
