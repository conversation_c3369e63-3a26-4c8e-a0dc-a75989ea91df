package cn.teleinfo.idycprovince.infrastructure.db.repository;

import cn.teleinfo.idycprovince.infrastructure.db.entity.HandleAuditEntity;
import cn.teleinfo.idycprovince.infrastructure.db.view.HandleAuditView;
import org.springframework.data.domain.Pageable;
import org.springframework.data.domain.Page;
import org.springframework.data.jpa.repository.Query;
import org.springframework.data.repository.query.Param;

import java.util.Date;

public interface HandleAuditRepository extends BaseRepository<HandleAuditEntity, Long> {

    HandleAuditEntity findByHandleId(Long handleId);

    @Query(nativeQuery = true,
            value = "SELECT t.id,\n" +
                    "       t.created_by                                                                       AS createdBy,\n" +
                    "       (SELECT u.nick_name FROM yc_user u WHERE u.id = t.created_by AND u.is_deleted = 0) AS createdByName,\n" +
                    "       t.created_time                                                                     AS createdTime,\n" +
                    "       t.updated_by                                                                       AS updatedBy,\n" +
                    "       (SELECT u.nick_name FROM yc_user u WHERE u.id = t.updated_by AND u.is_deleted = 0) AS updatedByName,\n" +
                    "       t.updated_time                                                                     AS updatedTime,\n" +
                    "       t.handle_id                                                                        AS handleId,\n" +
                    "       t.name,\n" +
                    "       t.handle,\n" +
                    "       t.app_id                                                                           AS appId,\n" +
                    "       a.app_name                                                                         AS appName,\n" +
                    "       t.apply_type                                                                       AS applyType,\n" +
                    "       t.audit_state                                                                      AS auditState,\n" +
                    "       t.handle_content                                                                   AS handleContent,\n" +
                    "       t.province_id                                                                      AS provinceId,\n" +
                    "       p.province_name                                                                    AS provinceName,\n" +
                    "       t.ent_id                                                                           AS entId,\n" +
                    "       e.org_name                                                                         AS entName\n" +
                    "FROM yc_handle_audit t\n" +
                    "LEFT JOIN yc_province p ON p.id = t.province_id\n" +
                    "LEFT JOIN yc_app_info a ON a.id = t.app_id\n" +
                    "LEFT JOIN yc_ent e ON e.id = t.ent_id\n" +
                    "WHERE t.is_deleted = 0\n" +
                    "  AND t.province_id = :provinceId \n" +
                    "  AND t.ent_id = :entId \n" +
                    "  AND if(:appId !='-1' , t.`app_id` = :appId, 1=1 ) " +
                    "  AND if(:auditState !='-1' , t.`audit_state` = :auditState, 1=1 ) " +
                    "  AND if(:name !='' , t.`name` like CONCAT('%',:name,'%'), 1=1 ) " +
                    "  AND if(:handle !='' , t.`handle` like CONCAT('%',:handle,'%'), 1=1 ) " +
                    "  AND if(:startTime is not null , t.updated_time >= :startTime , 1=1)  " +
                    "  AND if(:endTime is not null , t.updated_time <= :endTime , 1=1)  ",
            countQuery = "SELECT COUNT(*) \n" +
                    "FROM yc_handle_audit t\n" +
                    "WHERE t.is_deleted = 0\n" +
                    "  AND t.province_id = :provinceId \n" +
                    "  AND t.ent_id = :entId \n" +
                    "  AND if(:appId !='-1' , t.`app_id` = :appId, 1=1 ) " +
                    "  AND if(:auditState !='-1' , t.`audit_state` = :auditState, 1=1 ) " +
                    "  AND if(:name !='' , t.`name` like CONCAT('%',:name,'%'), 1=1 ) " +
                    "  AND if(:handle !='' , t.`handle` like CONCAT('%',:handle,'%'), 1=1 ) " +
                    "  AND if(:startTime is not null , t.updated_time >= :startTime , 1=1)  " +
                    "  AND if(:endTime is not null , t.updated_time <= :endTime , 1=1)  "
    )
    Page<HandleAuditView> page(@Param("name") String name, @Param("handle") String handle, @Param("startTime") Date startTime,
                               @Param("endTime") Date endTime, @Param("provinceId") Long provinceId, @Param("entId") Long entId,
                               @Param("appId") Long appId, @Param("auditState") Integer auditState,
                               Pageable pageable);


}
