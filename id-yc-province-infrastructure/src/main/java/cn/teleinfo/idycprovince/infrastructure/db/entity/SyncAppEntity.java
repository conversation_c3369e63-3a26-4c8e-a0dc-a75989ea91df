package cn.teleinfo.idycprovince.infrastructure.db.entity;

import lombok.Getter;
import lombok.Setter;
import org.hibernate.annotations.SQLDelete;
import org.hibernate.annotations.SQLDeleteAll;
import org.hibernate.annotations.Where;
import org.springframework.data.annotation.CreatedDate;
import org.springframework.data.annotation.LastModifiedDate;

import javax.persistence.Column;
import javax.persistence.Entity;
import javax.persistence.Id;
import javax.persistence.Table;
import java.time.LocalDateTime;

@Getter
@Setter
@Entity
@Table(name = "yc_integrated_app_info")
@SQLDelete(sql = "update yc_integrated_app_info set is_deleted = null, updated_time = NOW()  where id = ?")
@SQLDeleteAll(sql = "update yc_integrated_app_info set is_deleted = null, updated_time = NOW()  where id = ?")
@Where(clause = "is_deleted = 0")
public class SyncAppEntity {

    /**
     * 主键ID
     */
    @Id
    @Column(name = "id")
    private Long id;

    /**
     * 应用原始ID
     */
    @Column(name = "source_id")
    private Long sourceId;

    /**
     * 创建时间
     */
    @Column(name = "created_time")
    @CreatedDate
    private LocalDateTime createdTime;

    /**
     * 更新时间
     */
    @Column(name = "updated_time")
    @LastModifiedDate
    private LocalDateTime updatedTime;

    /**
     * 应用名称
     */
    @Column(name = "app_name")
    private String appName;

    /**
     * 标识编码
     */
    @Column(name = "handle_code")
    private String handleCode;

    /**
     * 部署地址
     */
    @Column(name = "deploy_address")
    private String deployAddress;

    /**
     * 系统版本
     */
    @Column(name = "sys_version")
    private String sysVersion;

    /**
     * 所属企业前缀
     */
    @Column(name = "ent_prefix")
    private String entPrefix;

    /**
     * 所属省级前缀
     */
    @Column(name = "province_prefix")
    private String provincePrefix;

    /**
     * 是否已删除：0否，null是
     */
    @Column(name = "is_deleted")
    private Integer isDeleted = 0 ;

    /**
     * 来源 1省级 2 企业
     */
    @Column(name = "source_type")
    private Integer sourceType;

    /**
     * 类型：1-中台应用,2-非中台应用
     */
    @Column(name = "app_type")
    private Integer appType;
}
