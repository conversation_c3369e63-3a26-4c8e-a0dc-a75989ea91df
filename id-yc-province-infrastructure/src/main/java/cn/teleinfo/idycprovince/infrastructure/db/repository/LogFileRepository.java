package cn.teleinfo.idycprovince.infrastructure.db.repository;

import cn.teleinfo.idycprovince.infrastructure.db.entity.LogFileEntity;
import org.springframework.data.jpa.repository.Query;

import java.util.List;

public interface LogFileRepository extends BaseRepository<LogFileEntity,Long> {

    @Query(nativeQuery = true, value =
            " SELECT *" +
            " FROM " +
            " yc_log_file a " +
            " WHERE " +
            " a.file_state = 2 AND " +
            " DATE_SUB(CURDATE(), INTERVAL 30 DAY) > DATE_FORMAT(created_time,'%Y-%m-%d') " )
    List<LogFileEntity> findOverDaysAndReaded();

    LogFileEntity findByFileName(String fileName);

    List<LogFileEntity> findByFileStateOrderByCreatedTimeAsc(Integer fileState);
}
