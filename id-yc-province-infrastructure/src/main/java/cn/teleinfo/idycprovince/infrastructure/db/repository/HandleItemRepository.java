package cn.teleinfo.idycprovince.infrastructure.db.repository;

import cn.teleinfo.idycprovince.infrastructure.db.entity.HandleItemEntity;
import org.springframework.data.jpa.repository.Modifying;
import org.springframework.data.jpa.repository.Query;
import org.springframework.data.repository.query.Param;
import org.springframework.transaction.annotation.Transactional;

import java.util.Collection;
import java.util.List;

/**
 * <AUTHOR>
 */
public interface HandleItemRepository extends BaseRepository<HandleItemEntity,Long> {
    
    /**
     * 根据标识id查询属性
     *
     * @param id
     * @return 结果
     **/
    List<HandleItemEntity> findByHandleId(Long id);
    
    /**
     * 根据标识id删除属性信息
     *
     * @param id
     * @return 结果
     **/
    void deleteByHandleId(Long id);
    
    /**
     * 查询是否有属性关联数据服务
     *
     * @param dataServiceId
     * @return 结果
     **/
    int countByDataServiceId(Long dataServiceId);
    
    /**
     * 根据索引查询属性
     *
     * @param handleId
     * @param fieldIndex
     * @return 结果
     **/
    HandleItemEntity findByHandleIdAndFieldIndex(Long handleId, Integer fieldIndex);

    /**
     * 根据属性名称查询属性
     *
     * @param handleId
     * @param field
     * @return 结果
     **/
    HandleItemEntity findByHandleIdAndField(Long handleId, String field);

    List<HandleItemEntity> findAllByHandleId(Long handleId);

//    HandleItemEntity findAllByHAndleId()

    /**
     * 根据属性中文名称查询属性*
     * @param handleId
     * @param description
     * @return
     */
    HandleItemEntity findByHandleIdAndDescription(Long handleId, String description);
    
    /**
     * 根据标识id和属性来源类型查询属性
     *
     * @param id
     * @return 结果
     **/
    List<HandleItemEntity> findByHandleIdAndFieldSourceType(Long id, Integer fieldSourceType);
    
    /**
     * 根据标识id和属性来源类型、应用编码查询属性
     *
     * @param id
     * @param fieldSourceType
     * @param appHandleCode
     * @return 结果
     **/
    List<HandleItemEntity> findByHandleIdAndFieldSourceTypeAndAppHandleCode(Long id, Integer fieldSourceType, String appHandleCode);
    
    
    /**
     * 根据标识id和索引列表查询
     *
     * @param handleId
     * @param fieldIndexList
     * @return 结果
     **/
    List<HandleItemEntity> findByHandleIdAndFieldIndexIn(Long handleId, Collection<Integer> fieldIndexList);

    List<HandleItemEntity> findByHandleIdAndFieldIn(Long handleId, Collection<String> fieldList);

    /**
     * 查询英文名称是否存在*
     * @param field
     * @param handleId
     * @return
     */
    HandleItemEntity findByFieldAndHandleId(String field,Long handleId);

    /**
     * 查询英文名称是否存在*
     * @param description
     * @param handleId
     * @return
     */
    HandleItemEntity findByDescriptionAndHandleId(String description,Long handleId);

    /**
     * 查询英文别名
     * 
     * @param handleId
     * @param tableName
     * @param columnName
     * @return
     */
    List<HandleItemEntity> findByHandleIdAndTableNameAndColumnName(Long handleId, String tableName, String columnName);

    @Query(nativeQuery = true,value = "SELECT " +
            " count( * ) " +
            " FROM" +
            " yc_handle_item a " +
            " WHERE" +
            " a.field = :field " +
            " AND a.field_index != :fieldIndex" +
            " AND a.handle_id = :handleId" +
            " AND a.is_deleted = 0")
    Integer checkFieldIsExist(@Param("fieldIndex") Integer fieldIndex,@Param("field") String field
                             ,@Param("handleId") Long handleId);

    @Query(nativeQuery = true,value = "SELECT " +
            " count( * )  " +
            " FROM " +
            " yc_handle_item a  " +
            " WHERE " +
            " a.description = :description  " +
            " AND a.field_index != :fieldIndex  " +
            " AND a.handle_id = :handleId  " +
            " AND a.is_deleted = 0")
    Integer checkDescriptionIsExist(@Param("fieldIndex") Integer fieldIndex,@Param("description") String description
            ,@Param("handleId") Long handleId);

    List<HandleItemEntity> findByHandleIdAndFieldType(Long id,Integer fieldType);

    /**
     * 物理删除对象标识parentId下所有的标识属性
     *
     * @param handleParentId
     */
    @Transactional
    @Modifying
    @Query(nativeQuery = true ,value = "DELETE FROM yc_handle_item WHERE handle_id IN (SELECT id FROM yc_handle WHERE parent_id = :handleParentId)")
    void deleteByHandleParentIdPhysical(@Param("handleParentId") Long handleParentId);

    boolean existsAllByTableNameIn(List<String> tableNames);

    void deleteByField(String field);

    HandleItemEntity findByFieldAndFieldSourceTypeAndHandleId(String field,Integer fieldSourceType,Long handleId);

    List<HandleItemEntity> findAllByDataSourceId(Long dataSourceId);

    @Query(nativeQuery = true,value = "select * from  yc_handle_item where handle_id = :id")
    List<HandleItemEntity> getAllByHandleId(@Param("id") Long id);
}