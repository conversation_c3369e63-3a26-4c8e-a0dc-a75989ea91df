package cn.teleinfo.idycprovince.infrastructure.db.entity;

import lombok.Getter;
import lombok.Setter;
import org.hibernate.annotations.SQLDelete;
import org.hibernate.annotations.SQLDeleteAll;
import org.hibernate.annotations.Where;

import javax.persistence.*;
import java.sql.Timestamp;
import java.util.Objects;

@Getter
@Setter
@Entity
@Table(name = "yc_ent_prefix")
@SQLDelete(sql = "update yc_ent_prefix set is_deleted = null , updated_time = NOW() where id = ?")
@SQLDeleteAll(sql = "update yc_ent_prefix set is_deleted = null , updated_time = NOW() where id = ?")
public class EntPrefixEntity extends BaseEntity {

    /**
     * 企业前缀
     */
    @Column(name = "ent_prefix")
    private String entPrefix;

    /**
     * 前缀状态
     */
    @Column(name = "state")
    private Integer state;

    /**
     * 企业ID
     */
    @Column(name = "ent_id")
    private Long entId;

}
