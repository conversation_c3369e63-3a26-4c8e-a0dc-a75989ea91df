package cn.teleinfo.idycprovince.infrastructure.db.entity;

import java.time.LocalDateTime;

import lombok.Getter;
import lombok.Setter;

import javax.persistence.*;

@Getter
@Setter
@Entity
@Table(name = "yc_province_tech")
public class ProvinceTechEntity extends BaseEntity{

    /**
    *  编码类型
    */
    @Column(name = "code_type")
    private String codeType;

    /**
    *  技术提供方
    */
    @Column(name = "tech_supporter")
    private String techSupporter;

    /**
    *  服务ip
    */
    @Column(name = "service_ip")
    private String serviceIp;

    /**
    *  服务端口
    */
    @Column(name = "service_port")
    private Integer servicePort;

    /**
    *  网络带宽
    */
    @Column(name = "network_band_width")
    private String networkBandWidth;

    /**
    *  运营商线路
    */
    @Column(name = "ope_line")
    private String opeLine;


}