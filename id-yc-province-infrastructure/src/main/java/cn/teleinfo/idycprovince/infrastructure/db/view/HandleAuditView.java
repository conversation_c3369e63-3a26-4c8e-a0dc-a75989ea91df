package cn.teleinfo.idycprovince.infrastructure.db.view;


import java.time.LocalDateTime;

public interface HandleAuditView {

    Long getId();

    /**
     * 创建时间
     */
    LocalDateTime getCreatedTime();

    /**
     * 创建人
     */
    Long getCreatedBy();

    String getCreatedByName();


    /**
     * 更新时间
     */
    LocalDateTime getUpdatedTime();

    /**
     * 更新人
     */
    Long getUpdatedBy();

    String getUpdatedByName();

    /**
     * 省级租户id
     */
    Long getProvinceId();

    String getProvinceName();

    /**
     * 标识 ID
     */
    Long getHandleId();

    /**
     * 标识名称
     */
    String getName();

    /**
     * 标识
     */
    String getHandle();

    /**
     * 应用ID
     */
    Long getAppId();

    String getAppName();

    /**
     * 申请类型 0 注册 1 编辑 2 删除
     */
    Integer getApplyType();

    /**
     * 审核状态 0 审核中 1 审核通过 2 驳回
     */
    Integer getAuditState();

    /**
     * 标识内容
     */
    String getHandleContent();

    /**
     * 企业ID
     */
    Long getEntId();

    String getEntName();
}
