package cn.teleinfo.idycprovince.infrastructure.db.entity;

import lombok.Getter;
import lombok.Setter;
import org.hibernate.annotations.SQLDelete;
import org.hibernate.annotations.SQLDeleteAll;
import org.hibernate.annotations.Where;

import javax.persistence.*;
import java.io.Serializable;
import java.time.LocalDateTime;

/**
 *
 *
 * @Author: liusp
 * @Date: 2023/07/19/14:47
 * @Description:
 */
@Getter
@Setter
@Entity
@Table(name = "yc_handle_user")
@SQLDelete(sql = "update yc_handle_user set is_deleted = null, updated_time = NOW()  where id = ?")
@SQLDeleteAll(sql = "update yc_handle_user set is_deleted = null, updated_time = NOW()  where id = ?")
@Where(clause = "is_deleted = 0")
public class HandleUserEntity extends BaseEntity implements Serializable {

    private static final long serialVersionUID = 1L;
    /**
     * 标识身份ID
     */
    @Id
    @GeneratedValue(strategy = GenerationType.IDENTITY)
    private Long id;
    /**
     * 省级ID
     */
    @Column(name = "province_id")
    private Long provinceId;
    /**
     * 企业ID
     */
    @Column(name = "ent_id")
    private Long entId;
    /**
     * 企业前缀ID
     */
    @Column(name = "ent_prefix_id")
    private Long entPrefixId;

    /**
     * 应用ID
     */
    @Column(name = "app_id")
    private Long appId;
    /**
     * 身份类型（应用:1/前缀:2/省级:3）
     */
    @Column(name = "user_type")
    private Integer userType;
    /**
     * 身份
     */
    @Column(name = "handle_name")
    private String handleName;
    /**
     * 标识身份
     */
    @Column(name = "handle_user")
    private String handleUser;
    /**
     * 身份私钥
     */
    @Column(name = "handle_private_key")
    private String handlePrivateKey;
    /**
     * 身份公钥
     */
    @Column(name = "handle_pubkey")
    private String handlePubkey;

    /**
     * 身份索引
     */
    @Column(name = "handle_index")
    private Integer handleIndex;

    /**
     * 创建人
     */
    @Column(name = "created_by")
    private Long createdBy;
    /**
     * 创建时间
     */
    @Column(name = "created_time")
    private LocalDateTime createdTime;
    /**
     * 更新人
     */
    @Column(name = "updated_by")
    private Long updatedBy;
    /**
     * 更新时间
     */
    @Column(name = "updated_time")
    private LocalDateTime updatedTime;

    /**
     * 是否删除
     */
    @Column(name = "is_deleted")
    private Integer isDeleted;

}
