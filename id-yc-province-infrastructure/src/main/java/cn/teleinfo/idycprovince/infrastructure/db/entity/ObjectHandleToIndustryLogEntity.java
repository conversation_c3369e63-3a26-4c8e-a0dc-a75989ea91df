package cn.teleinfo.idycprovince.infrastructure.db.entity;

import java.io.Serializable;
import java.time.LocalDateTime;

import lombok.Getter;
import lombok.Setter;
import org.springframework.data.annotation.CreatedBy;
import org.springframework.data.annotation.CreatedDate;
import org.springframework.data.annotation.LastModifiedBy;
import org.springframework.data.annotation.LastModifiedDate;
import org.springframework.data.jpa.domain.support.AuditingEntityListener;

import javax.persistence.*;

/**
 * <AUTHOR>
 */
@Getter
@Setter
@Entity
@Table(name = "yc_object_handle_to_industry_log")
@EntityListeners(AuditingEntityListener.class)
public class ObjectHandleToIndustryLogEntity implements Serializable {
    
    @Id
    @GeneratedValue(strategy = GenerationType.IDENTITY)
    private Long id;
    
    /**
     * 创建时间
     */
    @CreatedDate
    @Column(name = "created_time")
    private LocalDateTime createdTime;
    
    /**
     * 更新时间
     */
    @LastModifiedDate
    @Column(name = "updated_time")
    private LocalDateTime updatedTime;

    /**
    *  接口名称
    */
    @Column(name = "api_name")
    private String apiName;

    /**
    *  接口请求地址
    */
    @Column(name = "api_url")
    private String apiUrl;

    /**
    *  接口请求参数
    */
    @Column(name = "api_param")
    private String apiParam;

    /**
    *  接口失败重调次数
    */
    @Column(name = "fail_reset_count")
    private Integer failResetCount;

    /**
    *  响应码
    */
    @Column(name = "response_code")
    private String responseCode;

    /**
    *  响应描述
    */
    @Column(name = "response_desc")
    private String responseDesc;

    @Column(name = "province_id")
    private Long provinceId;


}