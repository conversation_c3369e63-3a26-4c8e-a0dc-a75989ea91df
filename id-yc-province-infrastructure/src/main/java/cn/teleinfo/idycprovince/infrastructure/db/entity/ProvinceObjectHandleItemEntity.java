package cn.teleinfo.idycprovince.infrastructure.db.entity;

import lombok.Getter;
import lombok.Setter;
import org.hibernate.annotations.SQLDelete;
import org.hibernate.annotations.SQLDeleteAll;
import org.hibernate.annotations.Where;

import javax.persistence.Column;
import javax.persistence.Entity;
import javax.persistence.Table;

@Getter
@Setter
@Entity
@Table(name = "yc_province_object_handle_item")
@SQLDelete(sql = "update yc_province_object_handle_item set is_deleted = null, updated_time = NOW()  where id = ?")
@SQLDeleteAll(sql = "update yc_province_object_handle_item set is_deleted = null, updated_time = NOW()  where id = ?")
@Where(clause = "is_deleted = 0")
public class ProvinceObjectHandleItemEntity extends BaseEntity{

    /**
    *  索引
    */
    @Column(name = "filed_index")
    private Integer filedIndex;

    /**
    *  字段名称
    */
    @Column(name = "filed")
    private String filed;

    /**
    *  字段描述
    */
    @Column(name = "description")
    private String description;

    /**
    *  字段类型 1基础 2标识 3关联
    */
    @Column(name = "field_type")
    private Integer fieldType;

    /**
    *  对象标识ID
    */
    @Column(name = "object_handle_id")
    private Long objectHandleId;

    /**
    *  企业ID
    */
    @Column(name = "ent_id")
    private Long entId;

    /**
     * 对象标识
     */
    @Column(name = "item_handle")
    private String itemHandle;

    /**
     * 选择字段
     */
    @Column(name = "optional_field")
    private String optionalField;
    
    /**
     *  数据服务ID
     */
    @Column(name = "data_service_id")
    private Long dataServiceId;
    
    /**
     *  数据服务名称
     */
    @Column(name = "data_service_name")
    private String dataServiceName;
    
    /**
     * 数据源ID
     */
    @Column(name = "data_source_id")
    private Long dataSourceId;
    
    /**
     * 数据源名称
     */
    @Column(name = "data_source_name")
    private String dataSourceName;

}