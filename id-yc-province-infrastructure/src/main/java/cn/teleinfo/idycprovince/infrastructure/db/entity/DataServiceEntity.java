package cn.teleinfo.idycprovince.infrastructure.db.entity;

import lombok.Getter;
import lombok.Setter;
import org.hibernate.annotations.SQLDelete;
import org.hibernate.annotations.SQLDeleteAll;
import org.hibernate.annotations.Where;

import javax.persistence.*;

/**
 * <AUTHOR>
 */
@Getter
@Setter
@Entity
@Table(name = "yc_data_service")
@SQLDelete(sql = "update yc_data_service set is_deleted = null, updated_time = NOW() where id = ?")
@SQLDeleteAll(sql = "update yc_data_service set is_deleted = null, updated_time = NOW() where id = ?")
@Where(clause = "is_deleted = 0")
public class DataServiceEntity extends BaseEntity {

    /**
     * 数据服务名称
     */
    @Column(name = "data_service_name")
    private String dataServiceName;

    /**
     * 地址
     */
    @Column(name = "service_address")
    private String serviceAddress;

    /**
     * Token
     */
    @Column(name = "service_token")
    private String serviceToken;
    
    /**
     * 企业id
     */
    @Column(name = "ent_id")
    private Long entId;
    
    /**
     * 应用id
     */
    @Column(name = "app_id")
    private Long appId;

    @Column(name = "data_service_uuid")
    private String dataServiceUuid;
    @Column(name = "data_service_type")
    private Integer dataServiceType;

    @Column(name = "version")
    private String version;

}
