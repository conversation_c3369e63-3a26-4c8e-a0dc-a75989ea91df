package cn.teleinfo.idycprovince.infrastructure.db.entity;

import lombok.Getter;
import lombok.Setter;
import org.hibernate.annotations.SQLDelete;
import org.hibernate.annotations.SQLDeleteAll;
import org.hibernate.annotations.Where;

import javax.persistence.Column;
import javax.persistence.Entity;
import javax.persistence.Table;

@Getter
@Setter
@Entity
@Table(name = "yc_ent")
@SQLDelete(sql = "update yc_ent set is_deleted = null , updated_time = NOW() where id = ?")
@SQLDeleteAll(sql = "update yc_ent set is_deleted = null , updated_time = NOW() where id = ?")
@Where(clause = "is_deleted = 0")
public class EntEntity extends BaseEntity {

    /**
     * 组织机构名称
     */
    @Column(name = "org_name")
    private String orgName;

    /**
     * 组织机构名称
     */
    @Column(name = "org_code")
    private String orgCode;

    /**
     * 上级单位名称
     */
    @Column(name = "parent_org_name")
    private String parentOrgName;

    /**
     * 所属省
     */
    @Column(name = "org_addr_province")
    private String orgAddrProvince;

    /**
     * 所属市
     */
    @Column(name = "org_addr_city")
    private String orgAddrCity;

    /**
     * 所属区
     */
    @Column(name = "org_addr_district")
    private String orgAddrDistrict;

    /**
     * 所属省份名称
     */
    @Column(name = "org_address")
    private String orgAddress;
    
    /**
     * 行业节点企业ID
     */
    @Column(name = "industry_ent_id")
    private Long industryEntId;

}
