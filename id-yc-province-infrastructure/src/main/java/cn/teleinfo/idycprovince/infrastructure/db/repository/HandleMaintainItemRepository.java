package cn.teleinfo.idycprovince.infrastructure.db.repository;

import cn.teleinfo.idycprovince.infrastructure.db.entity.HandleMaintainItemEntity;

import java.util.List;

/***
 * @title HandleMaintainRepository
 * @description <TODO description class purpose>
 * <AUTHOR>
 * @version 1.0.0
 * @create 2023/3/13 15:48
 **/
public interface HandleMaintainItemRepository extends BaseRepository<HandleMaintainItemEntity,Long> {

    List<HandleMaintainItemEntity> findByFieldIndexIn(List<Integer> fieldIndex);

    List<HandleMaintainItemEntity> findByFieldIn(List<String> field);

    List<HandleMaintainItemEntity> findByHandle(String handle);
    void deleteByMaintainId(Long maintainId);

    List<HandleMaintainItemEntity> findByMaintainIdAndFieldIndexIn(Long updateBy, List<Integer> fieldIndex);

    List<HandleMaintainItemEntity> findByMaintainIdAndFieldIn(Long updateBy, List<String> fieldIndex);

    void deleteByHandleAndFieldIndex(String handle, Integer fieldIndex);
    List<HandleMaintainItemEntity> findAllByHandleAndFieldIndex(String handle, Integer fieldIndex);

    List<HandleMaintainItemEntity> findByMaintainId(Long MaintainId);

}
