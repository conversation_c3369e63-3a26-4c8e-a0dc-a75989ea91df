package cn.teleinfo.idycprovince.infrastructure.db.repository;

import cn.teleinfo.idycprovince.infrastructure.db.entity.UserEntity;
import cn.teleinfo.idycprovince.infrastructure.db.to.EntAppUserTO;
import cn.teleinfo.idycprovince.infrastructure.db.to.EntUserInfoTO;
import cn.teleinfo.idycprovince.infrastructure.db.to.SocialUserTO;
import org.springframework.data.domain.Page;
import org.springframework.data.domain.Pageable;
import org.springframework.data.jpa.domain.Specification;
import org.springframework.data.jpa.repository.Query;
import org.springframework.data.repository.query.Param;

import java.util.List;

/*
 * File: CUserRepository.java
 * <AUTHOR>
 * @since 2022-07-01
 *
 * Copyright (c) 2022 abluepoint teleinfo All Rights Reserved.
 */

public interface UserRepository extends BaseRepository<UserEntity, Long> {

    UserEntity findByUsername(String username);

    UserEntity findByUsernameAndProvinceId(String username, Long provinceId);

    UserEntity findByEmailAndProvinceId(String email, Long provinceId);

    UserEntity findByEmail(String email);

    Boolean existsAllByProvinceId(Long provinceId);


    @Query(
            nativeQuery = true,
            value = "SELECT yai.handle_code FROM yc_user_app yuc " +
                    "left join yc_app_info yai on yai.id = yuc.app_id WHERE yuc.user_id = :userId"
    )
    List<String> findSysUserAppById(@Param("userId")Long userId);

    @Query(nativeQuery = true, value = "SELECT\n" +
            "    u.id AS sourceId,\n" +
            "    a.handle_code AS appHandleCode,\n" +
            "    e.ent_prefix AS entPrefix,\n" +
            "    SUBSTRING_INDEX(e.ent_prefix, '.', LENGTH(e.ent_prefix) - LENGTH(REPLACE(e.ent_prefix, '.', ''))) AS provincePrefix,\n" +
            "    r.role_code AS roleCode,\n" +
            "    u.created_time AS createdTime,\n" +
            "    u.updated_time AS updatedTime,\n" +
            "    u.username AS username,\n" +
            "    u.nick_name AS nickName,\n" +
            "    u.password AS password,\n" +
            "    u.address AS address,\n" +
            "    u.phone AS phone,\n" +
            "    u.email AS email,\n" +
            "    u.is_deleted AS isDeleted,\n" +
            "    2 AS sourceType,\n" +
            "    us.social_user_id AS socialUserId,\n" +
            "    u.remark AS remark\n" +
            "FROM\n" +
            "    yc_user u\n" +
            "INNER JOIN yc_user_social_user us ON u.id = us.user_id AND us.is_deleted = 0\n" +
            "LEFT JOIN yc_user_app ua ON u.id = ua.user_id AND ua.is_deleted = 0\n" +
            "LEFT JOIN yc_app_info a ON ua.app_id = a.id AND a.is_deleted = 0\n" +
            "LEFT JOIN yc_ent_prefix e ON u.ent_id = e.ent_id AND e.is_deleted = 0\n" +
            "LEFT JOIN yc_user_role ur ON u.id = ur.user_id AND ur.is_deleted = 0\n" +
            "LEFT JOIN yc_role r ON ur.role_id = r.id AND r.is_deleted = 0\n" +
            "WHERE\n" +
            "    r.role_code = 'sys_app_user';")
    List<SocialUserTO> findSocialUser();


    @Query(
            nativeQuery = true,
            value = "SELECT\n" +
                    "    u.id AS sourceId,\n" +
                    "    r.role_code AS roleCode,\n" +
                    "    u.created_time AS createdTime,\n" +
                    "    u.updated_time AS updatedTime,\n" +
                    "    u.username AS username,\n" +
                    "    u.nick_name AS nickName,\n" +
                    "    u.password AS password,\n" +
                    "    u.address AS address,\n" +
                    "    u.phone AS phone,\n" +
                    "    u.email AS email,\n" +
                    "    u.is_deleted AS isDeleted,\n" +
                    "    2 AS sourceType,\n" +
                    "    us.social_user_id AS ucOpenId,\n" +
                    "    u.remark AS remark\n" +
                    "FROM\n" +
                    "   yc_user u\n" +
                    "INNER JOIN yc_user_social_user us ON u.id = us.user_id AND us.is_deleted = 0\n" +
                    "LEFT JOIN yc_user_role ur ON u.id = ur.user_id AND ur.is_deleted = 0\n" +
                    "LEFT JOIN yc_role r ON ur.role_id = r.id and r.is_deleted = 0\n" +
                    "WHERE r.role_code = 'sys_app_user' and r.role_type = 3"
    )
    List<SocialUserTO> findUserByApp();

    @Query(nativeQuery = true,
            value = "SELECT DISTINCT\n" +
                    "    ua.user_id as sourceId,\n" +
                    "    a.app_name as appName,\n" +
                    "    a.handle_code as appHandleCode,\n" +
                    "    e.ent_prefix as entPrefix,\n" +
                    "    SUBSTRING_INDEX(e.ent_prefix, '.', LENGTH(e.ent_prefix) - LENGTH(REPLACE(e.ent_prefix, '.', ''))) AS provincePrefix\n" +
                    "FROM\n" +
                    "    yc_user_app ua\n" +
                    "LEFT JOIN yc_app_info a ON ua.app_id = a.id AND a.is_deleted = 0\n" +
                    "LEFT JOIN yc_ent_prefix e ON a.prefix_id = e.id AND e.is_deleted = 0\n" +
                    "WHERE a.app_name IS NOT NULL")
    List<SocialUserTO> findUserAppHandleCode();


    @Query(nativeQuery = true, value = "SELECT\n" +
            "    u.id AS sourceId,\n" +
            "    r.role_code AS roleCode,\n" +
            "    u.created_time AS createdTime,\n" +
            "    u.updated_time AS updatedTime,\n" +
            "    u.username AS username,\n" +
            "    u.nick_name AS nickName,\n" +
            "    u.password AS password,\n" +
            "    u.address AS address,\n" +
            "    u.phone AS phone,\n" +
            "    u.email AS email,\n" +
            "    u.is_deleted AS isDeleted,\n" +
            "    1 AS sourceType,\n" +
            "    us.social_user_id AS ucOpenId,\n" +
            "    u.remark AS remark\n" +
            "FROM\n" +
            "    yc_user u\n" +
            "INNER JOIN yc_user_social_user us ON u.id = us.user_id AND us.is_deleted = 0\n" +
            "LEFT JOIN yc_user_role ur ON u.id = ur.user_id AND ur.is_deleted = 0\n" +
            "LEFT JOIN yc_role r ON ur.role_id = r.id AND r.is_deleted = 0\n" +
            "WHERE\n" +
            "  (r.role_code = 'sys_app_user' or r.role_code = 'sys_admin') ")
    List<SocialUserTO> findProvinceSocialUser();


    Page<UserEntity> findAll(Specification<UserEntity> spec, Pageable pageable);

    @Query(nativeQuery = true, value =
            "SELECT " +
                    " p.id AS id, " +
                    " p.username AS username, " +
                    " p.nick_name AS nickName, " +
                    " p.remark AS remark, " +
                    " p.created_time AS createdTime, " +
                    " p.updated_time AS updatedTime, " +
                    " p.email AS email, " +
                    " p.phone AS phone, " +
                    " p.ent_id AS entId, " +
                    " p.address AS address, " +
                    " p.province_id AS provinceId, " +
                    " p.handle_user AS handleUser, " +
                    " e.org_name AS entName " +
                    "FROM " +
                    " yc_user p " +
                    " LEFT JOIN yc_ent e ON p.ent_id = e.id  " +
                    " AND e.is_deleted = 0  " +
                    "WHERE " +
                    " p.is_deleted = 0 " +
                    " AND p.id IN (SELECT user_id FROM yc_user_role a left join yc_role b on a.role_id = b.id WHERE b.role_type != 3 AND a.is_deleted = 0 ) " +
                    " AND p.ent_id is not null " +
                    " AND p.province_id = :provinceId " +
                    " AND if(:entId != '' and :entId is not null, p.ent_id = :entId, 1=1 ) " +
                    " AND if(:entName != '' and :entName is not null, e.org_name like CONCAT('%',:entName,'%'), 1=1 ) " +
                    " AND if(:username != '' and :username is not null, p.username like CONCAT('%',:username,'%'), 1=1 ) " +
                    "", countQuery =
            "SELECT " +
                    " count(*) " +
                    "FROM " +
                    " yc_user p " +
                    " LEFT JOIN yc_ent e ON p.ent_id = e.id  " +
                    " AND e.is_deleted = 0  " +
                    "WHERE " +
                    " p.is_deleted = 0 " +
                    " AND p.id IN (SELECT user_id FROM yc_user_role a left join yc_role b on a.role_id = b.id WHERE b.role_type != 3 AND a.is_deleted = 0 ) " +
                    " AND p.ent_id is not null " +
                    " AND p.province_id = :provinceId " +
                    " AND if(:entId != '' and :entId is not null, p.ent_id = :entId, 1=1 ) " +
                    " AND if(:entName != '' and :entName is not null, e.org_name like CONCAT('%',:entName,'%'), 1=1 ) " +
                    " AND if(:username != '' and :username is not null, p.username like CONCAT('%',:username,'%'), 1=1 ) "
    )
    Page<EntUserInfoTO> listUserByEnt(@Param("entName") String entName,
                                      @Param("provinceId") Long provinceId, @Param("entId") Long entId,
                                      @Param("username") String username, @Param("pageable") Pageable pageable);

    @Query(nativeQuery = true, value =
            "SELECT   " +
                    " u.id AS id, " +
                    " u.username AS username, " +
                    " u.remark AS remark, " +
                    " i.app_name AS appName, " +
                    " p.ent_prefix AS appPrefix, " +
                    " u.created_time AS createdTime, " +
                    " u.updated_time AS updatedTime, " +
                    " e.username AS operator " +
                    "FROM   " +
                    " yc_user u " +
                    " INNER JOIN yc_user_app a ON u.id = a.user_id AND a.is_deleted = 0 " +
                    " LEFT JOIN yc_app_info i ON a.app_id = i.id AND i.is_deleted = 0 " +
                    " LEFT JOIN yc_ent_prefix p ON i.prefix_id = p.id AND p.is_deleted = 0 " +
                    " LEFT JOIN yc_user e ON u.created_by = e.id AND e.is_deleted = 0 " +
                    "WHERE   " +
                    " u.is_deleted = 0 " +
                    " AND u.province_id = :provinceId " +
                    " AND u.ent_id = :entId " +
                    " AND (:username IS NULL OR u.username like CONCAT('%',:username,'%')) " +
                    " AND (:appName IS NULL OR i.app_name like CONCAT('%',:appName,'%'))", countQuery =
            "SELECT   " +
                    " count(*)" +
                    "FROM   " +
                    " yc_user u " +
                    " INNER JOIN yc_user_app a ON u.id = a.user_id AND a.is_deleted = 0 " +
                    " LEFT JOIN yc_app_info i ON a.app_id = i.id AND i.is_deleted = 0 " +
                    " LEFT JOIN yc_ent_prefix p ON i.prefix_id = p.id AND p.is_deleted = 0 " +
                    " LEFT JOIN yc_user e ON u.created_by = e.id AND e.is_deleted = 0 " +
                    "WHERE   " +
                    " u.is_deleted = 0 " +
                    " AND u.province_id = :provinceId " +
                    " AND u.ent_id = :entId " +
                    " AND (:username IS NULL OR u.username like CONCAT('%',:username,'%')) " +
                    " AND (:appName IS NULL OR i.app_name like CONCAT('%',:appName,'%'))"
    )
    Page<EntAppUserTO> listUserByApp(@Param("username") String username, @Param("appName") String appName,
                                     @Param("provinceId") Long provinceId, @Param("entId") Long entId, @Param("pageable") Pageable pageable);

    @Query(nativeQuery = true, value =
            "SELECT " +
                    " p.id AS id, " +
                    " p.username AS username, " +
                    " p.nick_name AS nickName, " +
                    " p.updated_time AS updatedTime, " +
                    " p.created_time AS createdTime, " +
                    " e.org_name AS orgName, " +
                    " i.app_name AS appName, " +
                    " yep.ent_prefix AS appPrefix " +
                    "FROM " +
                    " yc_user p " +
                    " LEFT JOIN yc_ent e ON p.ent_id = e.id  " +
                    " LEFT JOIN yc_user_app yua ON yua.user_id = p.id  AND yua.is_deleted = 0" +
                    " LEFT JOIN yc_app_info i ON yua.app_id = i.id AND i.is_deleted = 0 " +
                    " LEFT JOIN yc_ent_prefix yep ON i.prefix_id = yep.id AND yep.is_deleted = 0 " +
                    "WHERE " +
                    " p.is_deleted = 0 " +
                    " AND p.province_id = :provinceId " +
                    " AND if(:entId != '' and :entId is not null, p.ent_id = :entId, 1=1 ) " +
                    " AND if(:username != '' and :username is not null, p.username like CONCAT('%',:username,'%'), 1=1 ) ",
            countQuery =
                    "SELECT " +
                            " count(*) " +
                            "FROM " +
                            " yc_user p " +
                            " LEFT JOIN yc_ent e ON p.ent_id = e.id  " +
                            " LEFT JOIN yc_user_app yua ON yua.user_id = p.id  AND yua.is_deleted = 0" +
                            " LEFT JOIN yc_app_info i ON yua.app_id = i.id AND i.is_deleted = 0 " +
                            " LEFT JOIN yc_ent_prefix yep ON i.prefix_id = yep.id AND yep.is_deleted = 0 " +
                            "WHERE " +
                            " p.is_deleted = 0 " +
                            " AND p.province_id = :provinceId " +
                            " AND if(:entId != '' and :entId is not null, p.ent_id = :entId, 1=1 ) " +
                            " AND if(:username != '' and :username is not null, p.username like CONCAT('%',:username,'%'), 1=1 ) "
    )
    Page<EntUserInfoTO> listUserByProvince(@Param("provinceId") Long provinceId
            , @Param("username") String username, @Param("entId") Long entId, @Param("pageable") Pageable pageable);

    @Query(nativeQuery = true, value =
            "SELECT " +
                    " p.id AS id, " +
                    " p.username AS username, " +
                    " p.nick_name AS nickName, " +
                    " p.updated_time AS updatedTime, " +
                    " p.created_time AS createdTime, " +
                    " e.org_name AS orgName, " +
                    " i.app_name AS appName, " +
                    " yep.ent_prefix AS appPrefix " +
                    "FROM " +
                    " yc_user p " +
                    " LEFT JOIN yc_ent e ON p.ent_id = e.id  " +
                    " LEFT JOIN yc_user_app yua ON yua.user_id = p.id  AND yua.is_deleted = 0" +
                    " LEFT JOIN yc_app_info i ON yua.app_id = i.id AND i.is_deleted = 0 " +
                    " LEFT JOIN yc_ent_prefix yep ON i.prefix_id = yep.id AND yep.is_deleted = 0 " +
                    "WHERE " +
                    " p.is_deleted = 0 " +
                    " AND p.province_id = :provinceId " +
                    " AND p.id in (select user_id from yc_user_social_user)  " +
                    " AND if(:entId != '' and :entId is not null, p.ent_id = :entId, 1=1 ) " +
                    " AND if(:username != '' and :username is not null, p.username like CONCAT('%',:username,'%'), 1=1 ) ",
            countQuery =
                    "SELECT " +
                            " count(*) " +
                            "FROM " +
                            " yc_user p " +
                            " LEFT JOIN yc_ent e ON p.ent_id = e.id  " +
                            " LEFT JOIN yc_user_app yua ON yua.user_id = p.id AND yua.is_deleted = 0 " +
                            " LEFT JOIN yc_app_info i ON yua.app_id = i.id AND i.is_deleted = 0 " +
                            " LEFT JOIN yc_ent_prefix yep ON i.prefix_id = yep.id AND yep.is_deleted = 0 " +
                            "WHERE " +
                            " p.is_deleted = 0 " +
                            " AND p.province_id = :provinceId " +
                            " AND p.id in (select user_id from yc_user_social_user)  " +
                            " AND if(:entId != '' and :entId is not null, p.ent_id = :entId, 1=1 ) " +
                            " AND if(:username != '' and :username is not null, p.username like CONCAT('%',:username,'%'), 1=1 ) "
    )
    Page<EntUserInfoTO> listUserByMiddleGround(@Param("provinceId") Long provinceId
            , @Param("username") String username, @Param("entId") Long entId, @Param("pageable") Pageable pageable);

    @Query(nativeQuery = true, value =
            "select " +
                    "u.id AS id, " +
                    "u.username AS username, " +
                    "u.nick_name AS nickName, " +
                    "u.created_time AS createdTime, " +
                    "u.updated_time AS updatedTime, " +
                    "u.email AS email, " +
                    "u.phone AS phone, " +
                    "u.ent_id AS entId, " +
                    "u.address AS address, " +
                    "u.province_id AS provinceId, " +
                    "u.remark AS remark, " +
                    "u.handle_user AS handleUser, " +
                    "group_concat(c.role_name) AS roles " +
                    " from yc_user as u " +
                    " left join yc_user_role as b on b.user_id = u.id " +
                    " left join yc_role as c on c.id = b.role_id " +
                    "WHERE   " +
                    " u.is_deleted = 0 and c.role_type = 1 " +
                    " AND if(:provinceId is not null, u.province_id = :provinceId, 1=1) " +
                    " AND if(:username is not null, (:username IS NULL OR u.username like CONCAT('%',:username,'%')), 1=1) " +
                    " group by u.id", countQuery =
            "SELECT count(1) from (SELECT   " +
                    " u.id" +
                    " from yc_user as u " +
                    " left join yc_user_role as b on b.user_id = u.id " +
                    " left join yc_role as c on c.id = b.role_id " +
                    "WHERE   " +
                    " u.is_deleted = 0 and c.role_type = 1 " +
                    " AND if(:provinceId is not null, u.province_id = :provinceId, 1=1) " +
                    " AND if(:username is not null, (:username IS NULL OR u.username like CONCAT('%',:username,'%')), 1=1) " +
                    " group by u.id) as t"
    )
    Page<EntUserInfoTO> listUserBySuperAdmin(@Param("provinceId") Long provinceId, @Param("username") String username, @Param("pageable") Pageable pageable);


    boolean existsByUsernameAndProvinceId(String username, Long provinceId);

    boolean existsByPhoneAndProvinceId(String phone, Long provinceId);

    boolean existsByEmailAndProvinceId(String email, Long provinceId);
}