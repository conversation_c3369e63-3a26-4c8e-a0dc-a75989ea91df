package cn.teleinfo.idycprovince.infrastructure.db.entity;

import lombok.Data;
import org.hibernate.annotations.SQLDelete;
import org.hibernate.annotations.SQLDeleteAll;
import org.hibernate.annotations.Where;

import javax.persistence.Column;
import javax.persistence.Entity;
import javax.persistence.Table;
import java.io.Serializable;

/**
 * <AUTHOR>
 */
@Entity
@Data
@SQLDelete(sql = "update yc_handle_reference set is_deleted = null, updated_time = NOW()  where id = ?")
@SQLDeleteAll(sql = "update yc_handle_reference set is_deleted = null, updated_time = NOW()  where id = ?")
@Where(clause = "is_deleted = 0")
@Table(name = "yc_handle_reference")
public class HandleReferenceEntity extends BaseEntity implements Serializable {

    /**
    *  关联标识
    */
    @Column(name = "reference_handle")
    private String referenceHandle;

    /**
    *  关联标识属性
    */
    @Column(name = "reference_handle_prop")
    private String referenceHandleProp;

    /**
    *  查询属性
    */
    @Column(name = "query_prop")
    private String queryProp;

    /**
    *  参数属性
    */
    @Column(name = "param_prop")
    private String paramProp;

    /**
    *  标识属性ID
    */
    @Column(name = "handle_item_id")
    private Long handleItemId;

    /**
    *  企业ID
    */
    @Column(name = "ent_id")
    private Long entId;
    
    /**
     *  关联标识属性索引
     */
    @Column(name = "reference_handle_prop_index")
    private Integer referenceHandlePropIndex;
    
    /**
     *  查询属性索引
     */
    @Column(name = "query_prop_index")
    private Integer queryPropIndex;
    
    /**
     *  参数属性索引
     */
    @Column(name = "param_prop_index")
    private Integer paramPropIndex;


}