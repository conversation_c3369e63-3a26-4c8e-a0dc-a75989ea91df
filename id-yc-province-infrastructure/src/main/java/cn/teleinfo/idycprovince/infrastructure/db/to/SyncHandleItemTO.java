package cn.teleinfo.idycprovince.infrastructure.db.to;

import java.time.LocalDateTime;

public interface SyncHandleItemTO {

    /**
     * 获取主键ID
     * @return 主键ID值
     */
    Long getId();

    /**
     * 获取源标识属性ID
     * @return 源系统属性ID
     */
    Long getSourceId();

    /**
     * 获取记录创建时间
     * @return 创建时间
     */
    LocalDateTime getCreatedTime();

    /**
     * 获取记录更新时间
     * @return 最后更新时间
     */
    LocalDateTime getUpdatedTime();

    /**
     * 获取字段名称
     * @return 字段/属性名称
     */
    String getField();

    /**
     * 获取字段描述
     * @return 字段说明信息
     */
    String getDescription();

    /**
     * 获取字段类型
     * @return 类型值：1-固定值 2-标识解析数据源 3-标识值 4-标识属性
     */
    Integer getFieldType();

    /**
     * 获取关联的标识ID
     * @return 所属标识的主键ID
     */
    Long getHandleId();

    /**
     * 获取字段值内容
     * @return 字段值(长文本)
     */
    String getFieldValue();

    /**
     * 获取数据通道ID
     * @return 关联的数据通道ID
     */
    Long getDataChannelId();

    /**
     * 获取属性来源类型
     * @return 0-基础属性 1-扩展属性
     */
    Integer getFieldSourceType();

    /**
     * 获取应用标识编码
     * @return 应用系统唯一编码
     */
    String getAppHandleCode();

    /**
     * 获取备注信息
     * @return 附加说明内容
     */
    String getRemark();

    /**
     * 获取省级前缀
     * @return 省级标识前缀
     */
    String getProvincePrefix();

    /**
     * 获取企业前缀
     * @return 企业标识前缀
     */
    String getEntPrefix();

    /**
     * 获取删除状态
     * @return 0-未删除 null-已删除
     */
    Integer getIsDeleted();

    /**
     * 获取数据通道类型
     * @return 数据通道分类类型
     */
    Integer getDataChannelType();

    /**
     * 获取数据库名称
     * @return 关联的数据库名称
     */
    String getDatabaseName();

    /**
     * 获取数据库IP地址
     * @return 数据库服务器IP
     */
    String getDatabaseIp();

    /**
     * 获取关联表名
     * @return 数据来源表名称
     */
    String getTableName();

    /**
     * 获取关联字段名
     * @return 数据来源字段名称
     */
    String getColumnName();
}
