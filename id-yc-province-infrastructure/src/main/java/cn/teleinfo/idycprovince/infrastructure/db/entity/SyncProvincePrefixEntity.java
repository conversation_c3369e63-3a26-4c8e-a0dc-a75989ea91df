package cn.teleinfo.idycprovince.infrastructure.db.entity;

import lombok.Data;

import javax.persistence.Column;
import javax.persistence.Entity;
import javax.persistence.Id;
import javax.persistence.Table;
import java.time.LocalDateTime;
import java.util.Date;

/**
 * 集成省级前缀实体类
 */
@Data
@Entity
@Table(name = "yc_integrated_province_prefix")
public class SyncProvincePrefixEntity {
    /**
     * 主键
     */
    @Id
    @Column(name = "id")
    private Long id;

    @Column(name = "source_id")
    private Long sourceId;


    /**
     * 省级前缀
     */
    @Column(name = "province_prefix")
    private String provincePrefix;

    /**
     * 状态(1:正常;2:停用)
     */
    @Column(name = "province_prefix_state")
    private String provincePrefixState;

    /**
     * 创建人
     */
    @Column(name = "create_by")
    private String createBy;

    /**
     * 创建时间
     */
    @Column(name = "create_time")
    private LocalDateTime createTime;

    /**
     * 更新人
     */
    @Column(name = "update_by")
    private String updateBy;

    /**
     * 更新时间
     */
    @Column(name = "update_time")
    private LocalDateTime updateTime;

    /**
     * 同步状态(1:成功;0:失败)
     */
    @Column(name = "synchronization_state")
    private String synchronizationState;

    /**
     * 同步时间
     */
    @Column(name = "synchronization_time")
    private LocalDateTime synchronizationTime;
}