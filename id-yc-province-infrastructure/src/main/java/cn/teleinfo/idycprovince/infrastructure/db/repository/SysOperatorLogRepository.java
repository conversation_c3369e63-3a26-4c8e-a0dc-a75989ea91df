package cn.teleinfo.idycprovince.infrastructure.db.repository;

import cn.teleinfo.idycprovince.infrastructure.db.entity.SysOperatorLogEntity;
import cn.teleinfo.idycprovince.infrastructure.db.to.SysOperatorLogTO;
import org.springframework.data.domain.Page;
import org.springframework.data.domain.Pageable;
import org.springframework.data.jpa.repository.Modifying;
import org.springframework.data.jpa.repository.Query;
import org.springframework.data.repository.query.Param;

import javax.transaction.Transactional;

/**
 * @description: 系统日志
 * @author: liuyan
 * @create: 2022−11-04 5:50 PM
 */
public interface SysOperatorLogRepository extends BaseRepository<SysOperatorLogEntity, Long> {


    /**
     * 分页查询
     *
     * @param userName userName
     * @param pageable pageable
     * @return
     */
    @Query(nativeQuery = true,
            value =
                    "SELECT\n" +
                            "\t sys_log.id as logId,\n" +
                            "\t user_info.username as userName,\n" +
                            "\tsys_log.path as path,\n" +
                            "\tsys_log.host,\n" +
                            "\tsys_log.param,\n" +
                            "\t(case when sys_log.response_code = '10000' then '成功' when sys_log.response_code <> '10000' then '失败' end) as responseCode,\n" +
                            "\tDATE_FORMAT(sys_log.created_time, '%Y-%m-%d %H:%i:%s') as operateTime \n, " +
                            " sys_log.method , " +
                            " sys_log.description " +
                            "FROM\n" +
                            "\tyc_sys_operator_log sys_log\n" +
                            "\tLEFT JOIN ( SELECT u.id as id,u.username as username,a.app_id as app_id FROM yc_user u LEFT JOIN yc_user_app a ON u.id = a.user_id ) user_info ON sys_log.created_by = user_info.id\n" +
                            "\t where if(:userName !='', user_info.username like CONCAT('%',:userName,'%') , 1=1) " +
                            "\t and  if(:description !='', description like CONCAT('%',:description,'%') , 1=1) " +
                            "\t and  if(IFNULL(:entId, '') != '' ,  ent_id = :entId, 1=1)" +
                            "\t and  if(IFNULL(:proviceId, '') != '' ,  province_id = :proviceId, 1=1)" +
                            "\t and  if(IFNULL(:appId, '') != '' ,  user_info.app_id = :appId, 1=1)" +
                            "\t and  method <> 'GET'"+
                            "\t and is_deleted = 0",
            countQuery =
                    "SELECT" +
                            " count(*)\n" +
                            "FROM\n" +
                            "\tyc_sys_operator_log sys_log\n" +
                            "\tLEFT JOIN ( SELECT u.id as id,u.username as username,a.app_id as app_id FROM yc_user u LEFT JOIN yc_user_app a ON u.id = a.user_id ) user_info ON sys_log.created_by = user_info.id\n" +
                            "\t where if(:userName !='', user_info.username like CONCAT('%',:userName,'%') , 1=1) " +
                            "\t and  if(:description !='', description like CONCAT('%',:description,'%') , 1=1) " +
                            "\t and  if(IFNULL(:entId, '') != '' ,  ent_id = :entId, 1=1)" +
                            "\t and  if(IFNULL(:proviceId, '') != '' ,  province_id = :proviceId, 1=1)" +
                            "\t and  if(IFNULL(:appId, '') != '' ,  user_info.app_id = :appId, 1=1)" +
                            "\t and  method <> 'GET'"+
                            "\t and is_deleted = 0")
    Page<SysOperatorLogTO> pageQuery(@Param("userName") String userName,
                                     @Param("entId") Long entId,
                                     @Param("proviceId") Long proviceId,
                                     @Param("appId") Long appId,
                                     @Param("description") String description,
                                     Pageable pageable);


    /**
     * 逻辑删除日志
     * @param logId
     */
    @Query(nativeQuery = true, value =
            "UPDATE yc_sys_operator_log    " +
                    "SET is_deleted = null   " +
                    "WHERE   " +
                    "   id = :logId")
    @Modifying
    @Transactional
    void logicDeleteSysOpLog(@Param("logId") Long logId);
}
