package cn.teleinfo.idycprovince.infrastructure.db.repository;

import cn.teleinfo.idycprovince.infrastructure.db.entity.ObjectHandleMaintainPermissionEntity;

import java.util.List;

public interface ObjectHandleMaintainPermissionRepository extends BaseRepository<ObjectHandleMaintainPermissionEntity,Long> {

    List<ObjectHandleMaintainPermissionEntity> findByHandleAndCreatedBy(String handle, Long createdBy);

}
