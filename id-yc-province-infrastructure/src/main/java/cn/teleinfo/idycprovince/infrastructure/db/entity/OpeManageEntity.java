package cn.teleinfo.idycprovince.infrastructure.db.entity;

import lombok.Getter;
import lombok.Setter;
import org.hibernate.annotations.SQLDelete;
import org.hibernate.annotations.SQLDeleteAll;
import org.hibernate.annotations.Where;

import javax.persistence.*;
import java.sql.Timestamp;
import java.util.Objects;

/**
 * <AUTHOR>
 * @version 1.0
 * @date Created in 2022/11/4
 * @description
 */
@Setter
@Getter
@Entity
@Table(name = "yc_ope_manage", schema = "id_yc_province")
@SQLDelete(sql = "update yc_ope_manage set is_deleted = null, updated_time = NOW()  where id = ?")
@SQLDeleteAll(sql = "update yc_ope_manage set is_deleted = null, updated_time = NOW()  where id = ?")
@Where(clause = "is_deleted = 0")
public class OpeManageEntity extends BaseEntity{

    @Column(name = "operator", length = 32)
    private String operator;

    @Column(name = "phone", length = 32)
    private String phone;

    @Column(name = "email", length = 32)
    private String email;

    @Column(name = "address", length = 255)
    private String address;

    @Column(name = "ent_name", length = 255)
    private String entName;
}
