package cn.teleinfo.idycprovince.infrastructure.db.entity;

import lombok.Getter;
import lombok.Setter;

import javax.persistence.Column;
import javax.persistence.Entity;
import javax.persistence.Table;

/**
 * 字典表
 *
 * <AUTHOR>
 */
@Getter
@Setter
@Entity
@Table(name = "sys_dict")
public class SysDictEntity extends BaseEntity{

    /**
     *  字典类型
     */
    @Column(name = "dict_type")
    private String dictType;

    /**
     *  字典名称
     */
    @Column(name = "dict_name")
    private String dictName;

}