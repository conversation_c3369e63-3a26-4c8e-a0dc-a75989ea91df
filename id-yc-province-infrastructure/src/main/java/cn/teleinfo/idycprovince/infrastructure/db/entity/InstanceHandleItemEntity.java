package cn.teleinfo.idycprovince.infrastructure.db.entity;

import lombok.Getter;
import lombok.Setter;
import org.hibernate.annotations.SQLDelete;
import org.hibernate.annotations.SQLDeleteAll;
import org.hibernate.annotations.Where;

import javax.persistence.Column;
import javax.persistence.Entity;
import javax.persistence.Table;

@Getter
@Setter
@Entity
@Table(name = "yc_instance_handle_item")
@SQLDelete(sql = "update yc_instance_handle_item set is_deleted = null , updated_time = NOW() where id = ?")
@SQLDeleteAll(sql = "update yc_instance_handle_item set is_deleted = null, updated_time = NOW()  where id = ?")
@Where(clause = "is_deleted = 0")
public class InstanceHandleItemEntity extends BaseEntity {
    /**
     * 实例标识属性名称
     */
    @Column(name = "item_name")
    private String itemName;

    /**
     * 实例标识属性值
     */
    @Column(name = "item_value")
    private String itemValue;

    /**
     * 实例标识主体关联ID
     */
    @Column(name = "instance_handle_id")
    private Long instanceHandleId;

    @Column(name = "ent_id")
    private Long entId;
}
