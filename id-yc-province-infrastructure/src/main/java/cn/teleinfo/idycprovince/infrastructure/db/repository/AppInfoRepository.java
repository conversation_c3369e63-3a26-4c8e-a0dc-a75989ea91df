package cn.teleinfo.idycprovince.infrastructure.db.repository;

import cn.teleinfo.idycprovince.infrastructure.db.entity.AppInfoEntity;
import cn.teleinfo.idycprovince.infrastructure.db.to.AppTO;
import org.springframework.data.jpa.repository.Query;
import org.springframework.data.repository.query.Param;

import java.util.List;

public interface AppInfoRepository extends BaseRepository<AppInfoEntity, Long> {

    @Query(nativeQuery = true, value = " SELECT " +
            " a.id, " +
            " a.app_name , " +
            " a.app_type , " +
            " a.handle_code , " +
            " a.public_key , " +
            " a.deploy_address , " +
            " a.sys_version , " +
            " a.is_audit , " +
            " a.created_by, " +
            " a.created_time, " +
            " a.updated_by, " +
            " a.updated_time, " +
            " a.ent_id, " +
            " a.province_id, " +
            " a.is_deleted, " +
            " a.prefix_id, " +
            " a.master_data_scope, " +
            " a.mp_dmm_app_id " +
            " FROM " +
            " yc_app_info a " +
            " WHERE " +
            " a.is_deleted = 0 AND " +
            " a.province_id = :provinceId AND " +
            " (:appName IS NULL OR a.app_name LIKE CONCAT('%',:appName,'%'))  " +
            " AND if(:entId != '' and :entId is not null,a.ent_id = :entId, 1=1 ) " +
            " AND if(:appId != '' and :appId is not null,a.id = :appId, 1=1 ) ")
    List<AppInfoEntity> findAllByAppNameAndProvinceIdAndEntIdAndAppId(@Param("appName") String appName, @Param("provinceId") Long provinceId, @Param("entId") Long entId, @Param("appId") Long appId);

    boolean existsByAppNameAndProvinceIdAndEntId(String appName, Long provinceId, Long entId);

    AppInfoEntity findByEntIdAndAppName(Long entId, String appName);


    List<AppInfoEntity> findAllByPrefixId(Long prefixId);

    List<AppInfoEntity> findByAppNameAndId(String appName, Long appId);

    @Query(nativeQuery = true, value = "SELECT" +
            " b.ent_prefix " +
            " FROM" +
            " yc_app_info a" +
            " LEFT JOIN yc_ent_prefix b ON a.prefix_id = b.id " +
            " WHERE" +
            " a.id = :appId")
    String findPrefixByAppId(@Param("appId") Long appId);

    List<AppInfoEntity> findByProvinceIdAndEntId(Long provinceId, Long entId);

    AppInfoEntity findByHandleCode(String handleCode);

    @Query(nativeQuery = true, value = "SELECT\n" +
            "           a.id AS sourceId,\n" +
            "           a.created_time AS createdTime,\n" +
            "           a.updated_time AS updatedTime,\n" +
            "           a.app_name AS appName,\n" +
            "           a.handle_code AS handleCode,\n" +
            "           a.deploy_address AS deployAddress,\n" +
            "           a.sys_version AS sysVersion,\n" +
            "           p.ent_prefix AS entPrefix,\n" +
            "           SUBSTRING_INDEX(p.ent_prefix, '.', LENGTH(p.ent_prefix) - LENGTH(REPLACE(p.ent_prefix, '.', ''))) AS provincePrefix,\n" +
            "           a.is_deleted AS isDeleted,\n" +
            "           a.app_type AS appType\n" +
            "       FROM\n" +
            "           yc_app_info a\n" +
            "       LEFT JOIN\n" +
            "           yc_ent_prefix p ON a.prefix_id = p.id AND p.is_deleted = 0"
    )
    List<AppTO> findSyncApp();

    void deleteByEntId(Long entId);

    @Query(nativeQuery = true, value = "SELECT * FROM yc_app_info")
    List<AppInfoEntity> findDataServiceEntity();
}
