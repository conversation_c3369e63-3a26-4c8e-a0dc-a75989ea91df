package cn.teleinfo.idycprovince.infrastructure.db.entity;

import com.fasterxml.jackson.annotation.JsonFormat;
import lombok.Getter;
import lombok.Setter;
import org.hibernate.annotations.SQLDelete;
import org.hibernate.annotations.SQLDeleteAll;
import org.hibernate.annotations.Where;
import org.springframework.data.annotation.CreatedDate;
import org.springframework.data.annotation.LastModifiedDate;
import org.springframework.data.jpa.domain.support.AuditingEntityListener;

import javax.persistence.*;
import java.io.Serializable;
import java.time.LocalDateTime;

@Getter
@Setter
@EntityListeners(AuditingEntityListener.class)
@Entity
@Table(name = "yc_handle_auto_maintain_state")
@SQLDelete(sql = "update yc_handle_auto_maintain_state set is_deleted = null , updated_time = NOW() where id = ?")
@SQLDeleteAll(sql = "update yc_handle_auto_maintain_state set is_deleted = null , updated_time = NOW() where id = ?")
@Where(clause = "is_deleted = 0")
public class HandleAutoMaintainStateEntity implements Serializable {

    private static final long serialVersionUID = 1L;

    /**
     * 主键ID
     */
    @Column(name = "id")
    @Id
    @GeneratedValue(strategy = GenerationType.IDENTITY)
    private Long id;


    /**
     * 创建时间
     */
    @CreatedDate
    @Column(name = "created_time")
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss")
    private LocalDateTime createdTime;

    /**
     * 更新时间
     */
    @LastModifiedDate
    @Column(name = "updated_time")
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss")
    private LocalDateTime updatedTime;

    /**
     * 逻辑删除: 0 未删除 null 已删除
     */
    @Column(name = "is_deleted")
    private Integer isDeleted = 0;

    /**
     *  监测标识
     */
    @Column(name = "handle" )
    private String handle;

    /**
     *  监测标识中文名
     */
    @Column(name = "description" )
    private String description;

    /**
     *  监测属性
     */
    @Column(name = "field" )
    private String field;

    /**
     *  维护标识
     */
    @Column(name = "reference_handle" )
    private String referenceHandle;

    /**
     *  维护标识中文名
     */
    @Column(name = "reference_handle_name" )
    private String referenceHandleName;

    /**
     * 自动维护状态，0：未维护，1：维护中，2：已维护
     */
    @Column(name = "state")
    private Integer state;

}
