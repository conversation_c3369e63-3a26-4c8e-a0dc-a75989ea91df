package cn.teleinfo.idycprovince.infrastructure.db.repository;

import cn.teleinfo.idycprovince.infrastructure.db.entity.HostingServerEntity;
import org.springframework.data.jpa.repository.Query;
import org.springframework.data.repository.query.Param;

import java.util.List;

public interface HostingServerRepository extends BaseRepository<HostingServerEntity, Long> {

    @Query(nativeQuery = true, value =" SELECT " +
            " a.id, " +
            " a.srv_name , " +
            " a.ip_type , " +
            " a.ip, " +
            " a.tcp_port , " +
            " a.udp_port , " +
            " a.http_port , " +
            " a.ip_type_reslove , " +
            " a.ip_reslove , " +
            " a.tcp_port_reslove , " +
            " a.udp_port_reslove , " +
            " a.http_port_reslove , " +
            " a.auth_handle , " +
            " a.auth_index , " +
            " a.auth_private_key , " +
            " a.created_time , "+
            " a.created_by , "+
            " a.updated_time , "+
            " a.updated_by , "+
            " a.province_id,  "+
            " a.is_deleted "+
            " FROM " +
            " yc_hosting_server AS a " +
            " WHERE " +
            " (:srvName is null or a.srv_name LIKE CONCAT('%',:srvName,'%')) AND " +
            " a.is_deleted = 0 " +
            " and a.province_id = :provinceId ")
    List<HostingServerEntity> findBySvrName(@Param("srvName") String srvName,@Param("provinceId") Long provinceId);


}
