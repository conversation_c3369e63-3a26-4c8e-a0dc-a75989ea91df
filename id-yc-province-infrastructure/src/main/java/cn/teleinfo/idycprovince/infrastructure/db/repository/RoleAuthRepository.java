package cn.teleinfo.idycprovince.infrastructure.db.repository;

import cn.teleinfo.idycprovince.infrastructure.db.entity.RoleAuthEntity;

import java.util.Collection;
import java.util.List;

public interface RoleAuthRepository extends BaseRepository<RoleAuthEntity, Long> {
    void deleteByRoleId(Long roleId);
    void deleteAllByRoleIdIn(List<Long> roleIdList);

    List<RoleAuthEntity> findRoleAuthEntitiesByRoleIdIn(Collection<Long> roleId);
    List<RoleAuthEntity> findAllByRoleId(Long roleId);
}
