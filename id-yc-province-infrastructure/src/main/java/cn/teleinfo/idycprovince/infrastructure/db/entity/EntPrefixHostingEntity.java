package cn.teleinfo.idycprovince.infrastructure.db.entity;

import lombok.Data;
import org.hibernate.annotations.SQLDelete;
import org.hibernate.annotations.SQLDeleteAll;
import org.hibernate.annotations.Where;

import javax.persistence.Column;
import javax.persistence.Entity;
import javax.persistence.Table;
import java.io.Serializable;

@Entity
@Data
@Table(name = "yc_ent_prefix_hosting")
@SQLDelete(sql = "update yc_ent_prefix_hosting set is_deleted = null, updated_time = NOW() where id = ?")
@SQLDeleteAll(sql = "update yc_ent_prefix_hosting set is_deleted = null, updated_time = NOW() where id = ?")
@Where(clause = "is_deleted = 0")
public class EntPrefixHostingEntity extends BaseEntity implements Serializable {

    /** 企业前缀 */
    @Column(name = "ent_prefix")
    private String entPrefix ;
    /** 企业前缀id */
    @Column(name = "ent_prefix_id")
    private Long entPrefixId ;
    /** 服务器地址 */
    @Column(name = "hosting_server_id")
    private Long hostingServerId ;
    /** 所属企业 */
    @Column(name = "ent_id")
    private Long entId ;
    /** 托管状态(1.正在托管，0.取消托管) */
    @Column(name = "hosting_state")
    private Integer hostingState ;
}
