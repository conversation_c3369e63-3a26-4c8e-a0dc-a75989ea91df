package cn.teleinfo.idycprovince.infrastructure.db.entity;

import lombok.Data;
import org.hibernate.annotations.SQLDelete;
import org.hibernate.annotations.SQLDeleteAll;
import org.hibernate.annotations.Where;

import javax.persistence.Column;
import javax.persistence.Entity;
import javax.persistence.Table;
import javax.persistence.Transient;
import java.util.List;

/**
 *
 *
 * @Author: liusp
 * @Date: 2023/10/10/15:46
 * @Description:数据库表信息表
 */
@Entity
@Data
@SQLDelete(sql = "update yc_metadata_table set is_deleted = null, updated_time = NOW()  where id = ?")
@SQLDeleteAll(sql = "update yc_metadata_table set is_deleted = null, updated_time = NOW()  where id = ?")
@Where(clause = "is_deleted = 0")
@Table(name = "yc_metadata_table")
public class MetaDataTableEntity extends BaseEntity{
    /**
     * 企业ID
     */
    @Column(name = "ent_id")
    private Long entId;
    /**
     * 应用ID
     */
    @Column(name = "app_id")
    private Long appId;
    /**
     * 数据库ID
     */
    @Column(name = "db_id")
    private Long dbId ;
    /**
     * 表名称
     */
    @Column(name = "table_name")
    private String tableName;
    /**
     * 描述
     */
    @Column(name = "description")
    private String description;
    @Transient
    List<MetaDataColumnEntity> metaDataColumnEntityList;

}
