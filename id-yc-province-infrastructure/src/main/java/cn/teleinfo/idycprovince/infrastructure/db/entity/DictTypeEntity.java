package cn.teleinfo.idycprovince.infrastructure.db.entity;

import lombok.Getter;
import lombok.Setter;

import javax.persistence.Column;
import javax.persistence.Entity;
import javax.persistence.Table;
import java.io.Serializable;

/**
 * <p>
 *
 * </p>
 *
 * <AUTHOR>
 * @since 2022-11-05
 */

@Getter
@Setter
@Entity
@Table(name = "yc_dict_type")
public class DictTypeEntity extends BaseEntity implements Serializable {

    private static final long serialVersionUID = 1L;

    /**
     * 字典名称
     */
    @Column(name = "dict_name")
    private String dictName;

    /**
     * 字典码
     */
    @Column(name = "dict_type")
    private String dictType;

    @Column(name = "dict_desc")
    private String dictDesc;
}
