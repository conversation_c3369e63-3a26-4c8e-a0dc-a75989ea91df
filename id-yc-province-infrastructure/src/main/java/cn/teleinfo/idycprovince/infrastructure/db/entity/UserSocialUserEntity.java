package cn.teleinfo.idycprovince.infrastructure.db.entity;

import lombok.Getter;
import lombok.NoArgsConstructor;
import lombok.Setter;
import org.hibernate.annotations.SQLDelete;
import org.hibernate.annotations.SQLDeleteAll;
import org.hibernate.annotations.Where;

import javax.persistence.Column;
import javax.persistence.Entity;
import javax.persistence.GeneratedValue;
import javax.persistence.GenerationType;
import javax.persistence.Id;
import javax.persistence.Table;
import java.io.Serializable;

/**
 * <AUTHOR>
 */
@Getter
@Setter
@Entity
@Table(name = "yc_user_social_user")
@SQLDelete(sql = "update yc_user_social_user set is_deleted = null where id = ?")
@SQLDeleteAll(sql = "update yc_user_social_user set is_deleted = null where id = ?")
@Where(clause = "is_deleted = 0")
@NoArgsConstructor
public class UserSocialUserEntity implements Serializable {

    private static final long serialVersionUID = 1L;

    @Id
    @GeneratedValue(strategy = GenerationType.IDENTITY)
    @Column(name = "id", nullable = false)
    private Long id;

    /**
     * 用户ID
     */
    @Column(name = "user_id")
    private Long userId;

    /**
     * 应用ID
     */
    @Column(name = "social_user_id")
    private String socialUserId;

    /**
     * 逻辑删除: 0 未删除 null 已删除
     */
    @Column(name = "is_deleted")
    private Integer isDeleted = 0;

}
