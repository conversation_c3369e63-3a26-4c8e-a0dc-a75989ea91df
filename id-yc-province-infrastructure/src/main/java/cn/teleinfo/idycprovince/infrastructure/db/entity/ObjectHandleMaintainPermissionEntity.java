package cn.teleinfo.idycprovince.infrastructure.db.entity;

import lombok.Getter;
import lombok.Setter;
import org.hibernate.annotations.SQLDelete;
import org.hibernate.annotations.SQLDeleteAll;
import org.hibernate.annotations.Where;

import javax.persistence.Column;
import javax.persistence.Entity;
import javax.persistence.Table;

@Getter
@Setter
@Entity
@Table(name = "yc_object_handle_maintain_permission")
@SQLDelete(sql = "update yc_object_handle_maintain_permission set is_deleted = null, updated_time = NOW()  where id = ?")
@SQLDeleteAll(sql = "update yc_object_handle_maintain_permission set is_deleted = null, updated_time = NOW()  where id = ?")
@Where(clause = "is_deleted = 0")
public class ObjectHandleMaintainPermissionEntity extends BaseEntity{

    /**
     * 对象标识
     */
    @Column(name = "handle")
    private String handle;


    /**
    *  字段名称
    */
    @Column(name = "filed")
    private String filed;

    /**
    *  企业ID
    */
    @Column(name = "ent_id")
    private Long entId;

}
