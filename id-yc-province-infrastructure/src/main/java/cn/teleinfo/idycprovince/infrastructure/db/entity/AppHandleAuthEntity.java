package cn.teleinfo.idycprovince.infrastructure.db.entity;

import lombok.Getter;
import lombok.Setter;
import org.hibernate.annotations.SQLDelete;
import org.hibernate.annotations.SQLDeleteAll;
import org.hibernate.annotations.Where;

import javax.persistence.Column;
import javax.persistence.Entity;
import javax.persistence.Table;

@Getter
@Setter
@Entity
@Table(name = "yc_app_handle_auth")
@SQLDelete(sql = "update yc_app_handle_auth set is_deleted = null , updated_time = NOW()where id = ?")
@SQLDeleteAll(sql = "update yc_app_handle_auth set is_deleted = null , updated_time = NOW()where id = ?")
@Where(clause = "is_deleted = 0")
public class AppHandleAuthEntity extends BaseEntity{

    /**
     * 企业ID
     */
    @Column(name = "ent_id")
    private Long entId;

    /**
     * 应用标识身份
     */
    @Column(name = "handle_code")
    private String handleCode;

    /**
     * 应用名称
     */
    @Column(name = "app_name")
    private String appName;

    /**
     * 密钥
     */
    @Column(name = "secret_key")
    private String SecretKey;

    /**
     * 备注
     */
    @Column(name = "remark")
    private String remark;
}
