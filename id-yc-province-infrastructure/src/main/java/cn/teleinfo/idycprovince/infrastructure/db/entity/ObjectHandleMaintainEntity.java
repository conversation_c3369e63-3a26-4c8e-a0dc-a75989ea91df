package cn.teleinfo.idycprovince.infrastructure.db.entity;

import lombok.Getter;
import lombok.Setter;
import org.hibernate.annotations.SQLDelete;
import org.hibernate.annotations.SQLDeleteAll;
import org.hibernate.annotations.Where;

import javax.persistence.Column;
import javax.persistence.Entity;
import javax.persistence.Table;

@Getter
@Setter
@Entity
@Table(name = "yc_object_handle_maintain")
@SQLDelete(sql = "update yc_object_handle_maintain set is_deleted = null, updated_time = NOW()  where id = ?")
@SQLDeleteAll(sql = "update yc_object_handle_maintain set is_deleted = null, updated_time = NOW()  where id = ?")
@Where(clause = "is_deleted = 0")
public class ObjectHandleMaintainEntity extends BaseEntity{


    /**
    *  对象标识名称
    */
    @Column(name = "name")
    private String name;

    /**
    *  对象标识
    */
    @Column(name = "handle")
    private String handle;
    
    /**
     *  对象标识所属企业
     */
    @Column(name = "handle_ent_name")
    private String handleEntName;
    
    /**
     *  所属应用名称
     */
    @Column(name = "app_name")
    private String appName;
    
    /**
    *  企业ID
    */
    @Column(name = "ent_id")
    private Long entId;
    
    /**
     *  实体类型 1：资源实体 2：业务实体
     */
    @Column(name = "entity_type")
    private Integer entityType;

    /**
     *  所属应用
     */
    @Column(name = "app_id")
    private Long appId;


}
