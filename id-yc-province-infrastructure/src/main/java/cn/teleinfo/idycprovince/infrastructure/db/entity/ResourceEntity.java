package cn.teleinfo.idycprovince.infrastructure.db.entity;

import lombok.Getter;
import lombok.Setter;
import org.hibernate.annotations.SQLDelete;
import org.hibernate.annotations.SQLDeleteAll;
import org.hibernate.annotations.Where;
import org.springframework.data.annotation.CreatedBy;
import org.springframework.data.annotation.CreatedDate;
import org.springframework.data.annotation.LastModifiedBy;
import org.springframework.data.annotation.LastModifiedDate;
import org.springframework.data.jpa.domain.support.AuditingEntityListener;

import javax.persistence.Column;
import javax.persistence.Entity;
import javax.persistence.EntityListeners;
import javax.persistence.GeneratedValue;
import javax.persistence.GenerationType;
import javax.persistence.Id;
import javax.persistence.Table;
import java.io.Serializable;
import java.time.LocalDateTime;

/**
 * <p>
 * 功能权限表
 * </p>
 *
 * <AUTHOR>
 * @since 2022-11-05
 */

@Getter
@Setter
@Entity
@Table(name = "yc_resource")
@SQLDelete(sql = "update yc_resource set is_deleted = null, updated_time = NOW()  where id = ?")
@SQLDeleteAll(sql = "update yc_resource set is_deleted = null, updated_time = NOW()  where id = ?")
@Where(clause = "is_deleted = 0")
@EntityListeners(AuditingEntityListener.class)
public class ResourceEntity implements Serializable {

    private static final long serialVersionUID = 1L;

    /**
     * 资源编码
     */
    @Column(name = "resource_code")
    private String resourceCode;

    /**
     * 资源名称
     */
    @Column(name = "resource_name")
    private String resourceName;

    /**
     * 请求方式;GET,POST,PUT,DELETE
     */
    @Column(name = "method")
    private String method;

    /**
     * 是否启用;0：未删除，1：已删除；默认为0
     */
    @Column(name = "enabled")
    private Integer enabled;

    @Column(name = "path")
    private String path;
    
    @Id
    @GeneratedValue(strategy = GenerationType.IDENTITY)
    private Long id;
    
    /**
     * 创建时间
     */
    @CreatedDate
    @Column(name = "created_time")
    private LocalDateTime createdTime;
    
    /**
     * 创建人
     */
    @CreatedBy
    @Column(name = "created_by")
    private Long createdBy;
    
    /**
     * 更新时间
     */
    @LastModifiedDate
    @Column(name = "updated_time")
    private LocalDateTime updatedTime;
    
    /**
     * 更新人
     */
    @LastModifiedBy
    @Column(name = "updated_by")
    private Long updatedBy;
    
    /**
     * 逻辑删除: 0 未删除 null 已删除
     */
    @Column(name = "is_deleted")
    private Integer isDeleted = 0;
}
