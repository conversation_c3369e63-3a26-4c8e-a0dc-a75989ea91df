package cn.teleinfo.idycprovince.infrastructure.db.entity;

import lombok.Getter;
import lombok.Setter;
import org.hibernate.annotations.SQLDelete;
import org.hibernate.annotations.SQLDeleteAll;
import org.hibernate.annotations.Where;

import javax.persistence.Column;
import javax.persistence.Entity;
import javax.persistence.Table;

@Getter
@Setter
@Entity
@Table(name = "yc_instance_handle")
@SQLDelete(sql = "update yc_instance_handle set is_deleted = null, updated_time = NOW()  where id = ?")
@SQLDeleteAll(sql = "update yc_instance_handle set is_deleted = null, updated_time = NOW()  where id = ?")
@Where(clause = "is_deleted = 0")
public class InstanceHandleEntity extends BaseEntity {
    /**
     * 实例标识
     */
    @Column(name = "instance_handle")
    private String instanceHandle;

    /**
     * 实例标识名称
     */
    @Column(name = "instance_handle_name")
    private String instanceHandleName;

    @Column(name = "ent_id")
    private Long entId;

    /**
     *  所属应用
     */
    @Column(name = "app_id")
    private Long appId;

}
