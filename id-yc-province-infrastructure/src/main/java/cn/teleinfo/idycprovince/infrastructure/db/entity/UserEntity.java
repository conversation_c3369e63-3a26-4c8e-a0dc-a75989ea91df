package cn.teleinfo.idycprovince.infrastructure.db.entity;

import lombok.Getter;
import lombok.Setter;
import org.hibernate.annotations.SQLDelete;
import org.hibernate.annotations.SQLDeleteAll;
import org.hibernate.annotations.Where;

import javax.persistence.Column;
import javax.persistence.Entity;
import javax.persistence.Table;
import java.time.LocalDateTime;

@Getter
@Setter
@Entity
@Table(name = "yc_user")
@SQLDelete(sql = "update yc_user set is_deleted = null where id = ?")
@SQLDeleteAll(sql = "update yc_user set is_deleted = null where id = ?")
@Where(clause = "is_deleted = 0")
public class UserEntity extends BaseEntity{

    /**
    *  用户名
    */
    @Column(name = "username")
    private String username;

    /**
    *  密码
    */
    @Column(name = "password")
    private String password;

    /**
    *  昵称
    */
    @Column(name = "nick_name")
    private String nickName;

    /**
    *  邮箱
     */
    @Column(name = "email")
    private String email;

    /**
     * 手机号
     */
    @Column(name = "phone")
    private String phone;

    /**
     * 企业租户id
     */
    @Column(name = "ent_id")
    private Long entId;

    @Column(name = "dept_id")
    private Long deptId;

    @Column(name = "type")
    private Integer type;

    @Column(name = "address")
    private String address;

    @Column(name = "handle_user")
    private String handleUser;

    @Column(name = "enabled")
    private Integer enabled;

    @Column(name = "password_reset_time")
    private LocalDateTime passwordResetTime;

    @Column(name = "remark")
    private String remark;


}