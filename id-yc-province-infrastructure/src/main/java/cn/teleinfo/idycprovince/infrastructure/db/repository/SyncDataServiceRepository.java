package cn.teleinfo.idycprovince.infrastructure.db.repository;

import cn.teleinfo.idycprovince.infrastructure.db.entity.SyncDataServiceEntity;
import org.springframework.data.jpa.repository.Query;

import java.util.List;

public interface SyncDataServiceRepository extends BaseRepository<SyncDataServiceEntity, Long>{

    @Query(nativeQuery = true, value = "SELECT * FROM yc_integrated_data_service")
    List<SyncDataServiceEntity> findSyncDataService();

    List<SyncDataServiceEntity> findByEntPrefix(String entPrefix);
}
