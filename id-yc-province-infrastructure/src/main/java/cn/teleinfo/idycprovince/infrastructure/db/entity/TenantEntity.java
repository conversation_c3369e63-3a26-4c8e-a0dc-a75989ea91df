package cn.teleinfo.idycprovince.infrastructure.db.entity;


import lombok.Getter;
import lombok.Setter;

import javax.persistence.Column;
import javax.persistence.Entity;
import javax.persistence.Table;
import java.io.Serializable;
import java.time.LocalDateTime;

/**
 * <p>
 * 租户表
 * </p>
 *
 * <AUTHOR>
 * @since 2022-11-05
 */

@Getter
@Setter
@Entity
@Table(name = "yc_tenant")
public class TenantEntity extends BaseEntity implements Serializable {

    private static final long serialVersionUID = 1L;

    /**
     * 租户名称
     */
    @Column(name = "tenant_name")
    private String tenantName;

    /**
     * 租户编码
     */
    @Column(name = "tenant_code")
    private String tenantCode;

    /**
     * 有效期
     */
    @Column(name = "expire_time")
    private LocalDateTime expireTime;

    /**
     * 路径
     */
    @Column(name = "path")
    private String path;

    /**
     * 状态
     */
    @Column(name = "enabled")
    private Integer enabled;

    /**
     * 企业租户id
     */
    @Column(name = "ent_tenant_id")
    private Long entTenantId;

}
