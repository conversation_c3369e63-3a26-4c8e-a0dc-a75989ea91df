package cn.teleinfo.idycprovince.infrastructure.db.repository;

import cn.teleinfo.idycprovince.infrastructure.db.entity.UserRoleEntity;
import org.springframework.data.jpa.repository.Query;
import org.springframework.data.repository.query.Param;

import java.util.List;

public interface UserRoleRepository extends BaseRepository<UserRoleEntity, Long> {

    void deleteByUserId(Long userId);

    void deleteByRoleId(Long roleId);

    List<UserRoleEntity> findUserRoleEntitiesByUserId(Long userId);

    List<UserRoleEntity> findUserRoleEntitiesByRoleId(Long roleId);

    @Query(nativeQuery = true,value = "SELECT" +
            " a.role_type " +
            " FROM" +
            " yc_role a" +
            " LEFT JOIN yc_user_role b ON a.id = b.role_id " +
            " WHERE" +
            " a.is_deleted = 0 " +
            " AND b.is_deleted = 0 " +
            " AND b.user_id = :userId " +
            " AND a.province_id = :provinceId ")
    Integer selectRoleTypeByUserId(@Param("userId") Long userId, @Param("provinceId") Long provinceId);

    UserRoleEntity findByUserId(Long userId);
}
