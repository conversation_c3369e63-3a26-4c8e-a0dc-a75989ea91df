package cn.teleinfo.idycprovince.infrastructure.db.repository;

import cn.teleinfo.idycprovince.infrastructure.db.entity.PrefixTotalCountEntity;
import cn.teleinfo.idycprovince.infrastructure.db.view.PrefixTotalCountView;
import org.springframework.data.jpa.repository.Query;
import org.springframework.data.repository.query.Param;

import java.util.Collection;
import java.util.List;

/**
 * <AUTHOR>
 * @version 1.0
 * @date Created in 2022/11/5
 * @description 节点统计Repository
 */
public interface PrefixTotalCountRepository extends BaseRepository<PrefixTotalCountEntity,Long> {

    PrefixTotalCountEntity findByEntPrefix(String entPrefix);

    /**
     * 企业昨日累计量
     * @param provinceId
     * @return
     */
    @Query(nativeQuery = true, value =
            "SELECT " +
                    "ifnull( sum(yptc.handle_create_num), 0 ) AS handleCreateNum " +
                    "FROM yc_prefix_total_count yptc " +
                    "LEFT JOIN yc_ent_prefix AS yep " +
                    "ON yptc.ent_prefix = yep.ent_prefix " +
                    "AND yep.is_deleted = 0 " +
                    "WHERE yep.province_id = :provinceId "
                    )
    Long getEntSelfBuildRegCount(@Param("provinceId") Long provinceId);

    @Query(nativeQuery = true, value =
            "SELECT " +
                    "ifnull( sum(yptc.handle_query_num), 0 ) AS handleQueryNum " +
                    "FROM yc_prefix_total_count yptc " +
                    "LEFT JOIN yc_ent_prefix AS yep " +
                    "ON yptc.ent_prefix = yep.ent_prefix " +
                    "WHERE yep.province_id = :provinceId " +
                    "AND yep.is_deleted = 0 " )
    Long getEntSelfBuildResolveCount(@Param("provinceId") Long provinceId);

    /**
     * 企业上报历史统计量
     * @param provinceId
     * @return
     */
    @Query(nativeQuery = true, value = "SELECT " +
                    "ifnull( sum(yptc.handle_query_num), 0 ) AS handleQueryNum," +
                    "ifnull( sum(yptc.handle_create_num), 0 ) AS handleCreateNum " +
                    "FROM yc_prefix_total_count AS yptc " +
                    "LEFT JOIN yc_ent_prefix AS yep " +
                    "ON yptc.ent_prefix = yep.ent_prefix " +
                    "AND yep.is_deleted = 0 " +
                    "WHERE yep.province_id = :provinceId ")
    PrefixTotalCountView findPrefixTotalCountAll(@Param("provinceId") Long provinceId);


    @Query(nativeQuery = true, value =
            "SELECT  yptc.ent_prefix AS entPrefix," +
                    "ifnull( sum(yptc.handle_query_num), 0 ) AS handleQueryNum," +
                    "ifnull( sum(yptc.handle_create_num), 0 ) AS handleCreateNum " +
                    "FROM yc_prefix_total_count yptc " +
                    "LEFT JOIN yc_ent_prefix AS yep " +
                    "ON yptc.ent_prefix = yep.ent_prefix " +
                    "WHERE yep.province_id = :provinceId " +
                    "AND yep.is_deleted = 0 " +
                    "GROUP BY entPrefix")
    List<PrefixTotalCountView> findEntPrefixReportTOByDate(@Param("provinceId") Long provinceId);

    void deleteByEntPrefixIn(Collection<String> entPrefixes);
}
