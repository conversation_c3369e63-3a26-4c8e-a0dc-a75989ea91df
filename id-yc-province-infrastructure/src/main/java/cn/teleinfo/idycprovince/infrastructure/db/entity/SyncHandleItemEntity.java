package cn.teleinfo.idycprovince.infrastructure.db.entity;

import lombok.Getter;
import lombok.Setter;
import org.hibernate.annotations.BatchSize;
import org.hibernate.annotations.SQLDelete;
import org.hibernate.annotations.SQLDeleteAll;
import org.hibernate.annotations.Where;

import javax.persistence.Column;
import javax.persistence.Entity;
import javax.persistence.Id;
import javax.persistence.Table;
import java.time.LocalDateTime;


@Getter
@Setter
@Entity
@Table(name = "yc_integrated_handle_item")

public class SyncHandleItemEntity {
    @Id
    @Column(name = "id")
    private Long id;
    @Column(name = "source_id")
    private Long sourceId;

    @Column(name = "created_time")
    private LocalDateTime createdTime;

    @Column(name = "updated_time")
    private LocalDateTime updatedTime;

    @Column(name = "field")
    private String field;

    @Column(name = "description")
    private String description;

    @Column(name = "field_type")
    private Integer fieldType;

    @Column(name = "handle_id")
    private Long handleId;

    @Column(name = "field_value")
    private String fieldValue;

    @Column(name = "data_channel_id")
    private Long dataChannelId;

    @Column(name = "field_source_type")
    private Integer fieldSourceType;

    @Column(name = "app_handle_code")
    private String appHandleCode;

    @Column(name = "remark")
    private String remark;

    @Column(name = "province_prefix")
    private String provincePrefix;

    @Column(name = "ent_prefix")
    private String entPrefix;

    @Column(name = "is_deleted")
    private Integer isDeleted;

    @Column(name = "data_channel_type")
    private Integer dataChannelType;

    @Column(name = "database_name")
    private String databaseName;

    @Column(name = "database_ip")
    private String databaseIp;

    @Column(name = "table_name")
    private String tableName;

    @Column(name = "column_name")
    private String columnName;
}
