package cn.teleinfo.idycprovince.infrastructure.db.entity;

import lombok.Data;
import org.springframework.data.annotation.CreatedDate;
import org.springframework.data.annotation.LastModifiedDate;

import javax.persistence.*;
import javax.validation.constraints.NotNull;
import java.time.LocalDateTime;

/**
 * 字典表
 *
 * <AUTHOR>
 */
@Data
@Entity
@Table(name = "yc_sys_dict")
public class SysDictPO {

    /**
     *
     */
    private static final long serialVersionUID = 1L;

    @Id
    @GeneratedValue(strategy = GenerationType.AUTO)
    @Column(columnDefinition = "bigint(20) COMMENT '主键ID'")
    protected Long id;

    @CreatedDate
    @Column(name = "create_date", updatable = false, columnDefinition = "datetime COMMENT '创建时间'")
    protected LocalDateTime createDate;

    @Column(name = "update_date", columnDefinition = "datetime COMMENT '更新时间'")
    @LastModifiedDate
    protected LocalDateTime updateDate;

    @NotNull(message = "字典KEY不能为空")
    @Column(name = "dict_key", columnDefinition = "varchar(50) COMMENT '字典KEY'")
    private String dictKey;

    @Column(name = "dict_code", columnDefinition = "varchar(50) COMMENT '字典CODE'")
    private String dictCode;

    @Column(name = "dict_value", columnDefinition = "varchar(50) COMMENT '字典VALUE'")
    private String dictValue;

    @NotNull(message = "父ID不能为空")
    @Column(name = "parent_id", columnDefinition = "bigint(20) DEFAULT 0 COMMENT '父ID'")
    private Long parentId;
}
