package cn.teleinfo.idycprovince.infrastructure.db.repository;

import cn.teleinfo.idycprovince.infrastructure.db.entity.SyncUserEntity;
import cn.teleinfo.idycprovince.infrastructure.db.view.UserSyncView;
import org.springframework.data.jpa.repository.Query;

import java.util.List;


public interface SyncUserRepository extends BaseRepository<SyncUserEntity, Long> {


    @Query(
            nativeQuery = true,
            value = "SELECT * FROM yc_integrated_user"
    )
    List<SyncUserEntity> findSyncUserEntity();

    @Query(
            nativeQuery = true,
            value = "SELECT * FROM yc_integrated_user"
    )
    List<SyncUserEntity> findSyncUserEntityBySourceType();

    @Query(
            nativeQuery = true,
            value = "SELECT \n" +
                    "    iu.id,\n" +
                    "    iu.source_id as sourceId,\n" +
                    "    iu.username,\n" +
                    "    iu.uc_open_id as ucOpenId,\n" +
                    "    iu.role_code as roleCode,\n" +
                    "    iu.ent_prefix as entPrefix,\n" +
                    "    iu.province_prefix as provincePrefix,\n" +
                    "    iu.created_time as createdTime,\n" +
                    "    iu.updated_time as updatedTime,\n" +
                    "    iu.is_deleted as isDeleted,\n" +
                    "    iu.name as name,\n" +
                    "    iu.app_handle_codes AS handleCodes\n" +
                    "FROM \n" +
                    "   yc_integrated_user iu\n"
    )
    List<UserSyncView> findSyncUserEntityByGroupByUserName();

}