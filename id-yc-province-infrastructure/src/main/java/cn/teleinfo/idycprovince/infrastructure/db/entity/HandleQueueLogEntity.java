package cn.teleinfo.idycprovince.infrastructure.db.entity;

import lombok.Getter;
import lombok.Setter;
import org.hibernate.annotations.SQLDelete;
import org.hibernate.annotations.SQLDeleteAll;
import org.hibernate.annotations.Where;
import org.springframework.data.annotation.CreatedBy;
import org.springframework.data.annotation.CreatedDate;
import org.springframework.data.annotation.LastModifiedBy;
import org.springframework.data.annotation.LastModifiedDate;
import org.springframework.data.jpa.domain.support.AuditingEntityListener;

import javax.persistence.*;
import java.time.LocalDateTime;

/***
 * @title HandleQueueLog
 * @description 消息发送记录表
 * <AUTHOR>
 * @version 1.0.0
 * @create 2023/3/13 15:41
 **/
@Getter
@Setter
@Entity
@Table(name = "yc_handle_queue_log")
@SQLDelete(sql = "update yc_handle_queue_log set is_deleted = null, updated_time = NOW()  where id = ?")
@SQLDeleteAll(sql = "update yc_handle_queue_log set is_deleted = null, updated_time = NOW()  where id = ?")
@Where(clause = "is_deleted = 0")
@EntityListeners(AuditingEntityListener.class)
public class HandleQueueLogEntity {

    /**
     *  标识
     */
    @Column(name = "handle")
    private String handle;

    /**
     *  发送类型（0发送request 1接收response）
     */
    @Column(name = "send_type")
    private Integer sendType;

    /**
     *  业务ID
     */
    @Column(name = "business_id")
    private String businessId;

    /**
     *  业务编码
     */
    @Column(name = "business_code")
    private String businessCode;

    /**
     *  mq主题
     */
    @Column(name = "topic")
    private String topic;

    /**
     *  消息体数据,不包含外层
     */
    @Column(name = "msg_data")
    private String msgData;

    /**
     *  更新的数据
     */
    @Column(name = "info_update")
    private String infoUpdate;

    /**
     *  结果状态码
     */
    @Column(name = "result_code")
    private int resultCode;

    /**
     *  结果描述
     */
    @Column(name = "result_desc")
    private String resultDesc;

    /**
     *  重试次数
     */
    @Column(name = "retry_times")
    private Integer retryTimes;

    /**
     *  维护状态 0维护中 1 维护成功 2 维护失败
     */
    @Column(name = "maintain_state")
    private Integer maintainState;

    /**
     *  维护标识id
     */
    @Column(name = "maintain_id")
    private Long maintainId;

    //  逻辑删除初始化字段
    public static final Integer INIT_STATUS = 0;

    @Id
    @GeneratedValue(strategy = GenerationType.IDENTITY)
    private Long id;

    /**
     * 创建时间
     */
    @Column(name = "created_time")
    private LocalDateTime createdTime;

    /**
     * 创建人
     */
    @Column(name = "created_by")
    private Long createdBy;

    /**
     * 更新时间
     */
    @LastModifiedDate
    @Column(name = "updated_time")
    private LocalDateTime updatedTime;

    /**
     * 更新人
     */
    @Column(name = "updated_by")
    private Long updatedBy;

    /**
     * 逻辑删除: 0 未删除 null 已删除
     */
    @Column(name = "is_deleted")
    private Integer isDeleted = 0;

    /**
     * 省级租户id
     */
    @Column(name = "province_id")
    private Long provinceId;

    /**
     * 省级租户id
     */
    @Column(name = "ack")
    private Integer ack;

}
