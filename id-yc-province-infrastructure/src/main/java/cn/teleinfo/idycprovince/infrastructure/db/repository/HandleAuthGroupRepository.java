package cn.teleinfo.idycprovince.infrastructure.db.repository;

import cn.teleinfo.idycprovince.infrastructure.db.entity.HandleAuthGroupEntity;
import cn.teleinfo.idycprovince.infrastructure.db.view.GroupHandlePageVIew;
import org.springframework.data.domain.Page;
import org.springframework.data.domain.Pageable;
import org.springframework.data.jpa.repository.Query;
import org.springframework.data.repository.query.Param;

import java.util.List;

public interface HandleAuthGroupRepository extends BaseRepository<HandleAuthGroupEntity, Long>{
    List<HandleAuthGroupEntity> findAllByAuthGroupId(Long authGroupId);


    @Query(nativeQuery = true,value = "SELECT" +
            " a.id as id, " +
            " a.app_id as appId, " +
            " a.updated_time as updatedTime, " +
            " a.created_time as createdTime, " +
            " h.handle as handle, " +
            " a.handle_id as handleId, " +
            " h.`name` as name, " +
            " ai.`app_name` as appName " +
            " FROM" +
            " yc_handle_auth_group a " +
            " LEFT JOIN yc_handle h ON a.handle_id = h.id and h.is_deleted = 0 " +
            " LEFT JOIN yc_app_info ai ON a.app_id = ai.id and ai.is_deleted = 0 " +
            " WHERE " +
            "  a.is_deleted = 0 " +
            " AND if(:name !='' , h.`name` like CONCAT('%',:name,'%'), 1=1 )  " +
            " AND if(:handle !='' , h.`handle` like CONCAT('%',:handle,'%') , 1=1 ) " +
            " AND a.auth_group_id = :authGroupId ",
    countQuery = "SELECT" +
            " count(*) " +
            " FROM" +
            " yc_handle_auth_group a " +
            " LEFT JOIN yc_handle h ON a.handle_id = h.id and h.is_deleted = 0 " +
            " LEFT JOIN yc_app_info ai ON a.app_id = ai.id and ai.is_deleted = 0 " +
            " WHERE " +
            "  a.is_deleted = 0 " +
            " AND if(:name !='' , h.`name` like CONCAT('%',:name,'%'), 1=1 )  " +
            " AND if(:handle !='' , h.`handle` like CONCAT('%',:handle,'%') , 1=1 ) " +
            " AND a.auth_group_id = :authGroupId " )
    Page<GroupHandlePageVIew> findGroupHandlePage(@Param("authGroupId") Long authGroupId,
                                                  @Param("name") String name,
                                                  @Param("handle") String handle,
                                                   Pageable pageable);


    HandleAuthGroupEntity findByHandleIdAndAuthGroupId(Long handleId, Long authGroupId);

    void deleteAllByHandleId(Long handleId);
}
