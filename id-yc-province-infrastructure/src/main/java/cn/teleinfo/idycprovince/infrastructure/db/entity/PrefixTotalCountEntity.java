package cn.teleinfo.idycprovince.infrastructure.db.entity;

import lombok.Getter;
import lombok.Setter;

import javax.persistence.*;
import java.io.Serializable;
import java.time.LocalDateTime;

/**
 * <AUTHOR>
 * @version 1.0
 * @date Created in 2022/11/5
 * @description 前缀统计
 */
@Getter
@Setter
@Entity
@Table(name = "id_yc_province.yc_prefix_total_count")
public class PrefixTotalCountEntity implements Serializable {

    @Id
    @GeneratedValue(strategy = GenerationType.IDENTITY)
    private Long id;

    /**
     * 创建时间
     */
    @Column(name = "created_time")
    private LocalDateTime createdTime;

    /**
     * 更新时间
     */
    @Column(name = "updated_time")
    private LocalDateTime updatedTime;

    /**
     * 省级前缀
     */
    @Column(name = "province_prefix")
    private String provincePrefix;

    /**
     * 企业前缀
     */
    @Column(name = "ent_prefix")
    private String entPrefix;

    /**
     * 标识解析量
     */
    @Column(name = "handle_query_num")
    private Long handleQueryNum;

    /**
     * 标识注册量
     */
    @Column(name = "handle_create_num")
    private Long handleCreateNum;

    /**
     * 标识删除量
     */
    @Column(name = "handle_delete_num")
    private Long handleDeleteNum;

    /**
     * 新增属性数量
     */
    @Column(name = "handle_value_add_num")
    private Long handleValueAddNum;

    /**
     * 更新属性数量
     */
    @Column(name = "handle_value_modify_num")
    private Long handleValueModifyNum;

    /**
     * 移除属性数量
     */
    @Column(name = "handle_value_remove_num")
    private Long handleValueRemoveNum;

}
