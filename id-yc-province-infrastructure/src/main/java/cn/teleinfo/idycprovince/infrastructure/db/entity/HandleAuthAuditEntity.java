package cn.teleinfo.idycprovince.infrastructure.db.entity;

import lombok.Data;
import org.hibernate.annotations.SQLDelete;
import org.hibernate.annotations.SQLDeleteAll;
import org.hibernate.annotations.Where;

import javax.persistence.Column;
import javax.persistence.Entity;
import javax.persistence.Table;
import java.time.LocalDateTime;

@Entity
@Data
@SQLDelete(sql = "update yc_handle_auth_audit set is_deleted = null, updated_time = NOW() where id = ? ")
@SQLDeleteAll(sql = "update yc_handle_auth_audit set is_deleted = null, updated_time = NOW() where id = ? ")
@Where(clause = "is_deleted = 0")
@Table(name = "yc_handle_auth_audit")
public class HandleAuthAuditEntity extends BaseEntity{

    /**
     * 申请记录表id*
     */
    @Column(name = "apply_id")
    private Long applyId;
    /**
     * 申请人身份名称*
     */
    @Column(name = "apply_handle_name")
    private String applyHandleName;

    /**
     * 申请人标识身份*
     */
    @Column(name = "apply_handle_user")
    private String applyHandleUser;

    /**
     * 申请人身份类型：1应用身份，2前缀身份*
     */
    @Column(name = "apply_handle_type")
    private Integer applyHandleType;

    /**
     * 申请人身份索引*
     */
    @Column(name = "apply_handle_index")
    private Integer applyHandleIndex;

    /**
     * 申请时间*
     */
    @Column(name = "apply_time")
    private LocalDateTime applyTime;

    /**
     * 标识名称*
     */
    @Column(name = "name")
    private String name;

    /**
     * 标识*
     */
    @Column(name = "handle")
    private String handle;

    /**
     * 实体类型：1业务实体，2资源实体*
     */
    @Column(name = "entity_type")
    private Integer entityType;

    /**
     * 审核状态：0待审核，1审核通过，2驳回*
     */
    @Column(name = "audit_state")
    private Integer auditState;

    /**
     * 应用id*
     */
    @Column(name = "app_id")
    private Long appId;

    /**
     * 企业id*
     */
    @Column(name = "ent_id")
    private Long entId;

    /**
     * 标识所属应用名称*
     */
    @Column(name = "app_name")
    private String appName;
}
