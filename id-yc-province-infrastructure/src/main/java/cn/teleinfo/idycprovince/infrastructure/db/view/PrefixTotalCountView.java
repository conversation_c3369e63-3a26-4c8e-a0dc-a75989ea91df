package cn.teleinfo.idycprovince.infrastructure.db.view;

import java.time.LocalDateTime;

/**
 * Created with IntelliJ IDEA.
 *
 * @Author: liusp
 * @Date: 2024/01/14/16:28
 * @Description:
 */
public interface PrefixTotalCountView {
    /**
     * 创建时间
     * @return
     */
    LocalDateTime getCreatedTime();

    /**
     * 更新时间
     * @return
     */
    LocalDateTime getUpdatedTime();

    /**
     * 省级前缀
     */
    String getProvincePrefix();

    /**
     * 企业前缀
     */
    String getEntPrefix();

    /**
     * 解析量
     * @return
     */
    Long getHandleQueryNum();

    /**
     * 注册量
     * @return
     */
    Long getHandleCreateNum();

    /**
     * 删除量
     * @return
     */
    Long getHandleDeleteNum();

    /**
     * 统计日期(月)
     * @return
     */
    String getStatisticsMouth();

    /**
     * 统计日期（天）
     * @return
     */
    String getStatisticsDate();

    /**
     *新增属性数量
     * @return
     */
    Long getHandleValueAddNum();

    /**
     * 更新属性数量
     * @return
     */
    Long getHandleValueModifyNum();
    /**
     * 移除属性数量
     * @return
     */
    Long getHandleValueRemoveNum();

    /**
     * 数据来源
     * @return
     */
    Integer getSourceType();

}
