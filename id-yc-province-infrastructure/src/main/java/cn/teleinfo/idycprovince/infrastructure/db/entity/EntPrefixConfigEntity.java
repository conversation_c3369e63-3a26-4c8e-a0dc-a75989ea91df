package cn.teleinfo.idycprovince.infrastructure.db.entity;

import lombok.Getter;
import lombok.Setter;
import org.hibernate.annotations.SQLDelete;
import org.hibernate.annotations.SQLDeleteAll;
import org.hibernate.annotations.Where;

import javax.persistence.*;
import java.sql.Timestamp;
import java.util.Objects;

@Getter
@Setter
@Entity
@Table(name = "yc_ent_prefix_config")
@SQLDelete(sql = "update yc_ent_prefix_config set is_deleted = null, updated_time = NOW() where id = ?")
@SQLDeleteAll(sql = "update yc_ent_prefix_config set is_deleted = null , updated_time = NOW() where id = ?")
@Where(clause = "is_deleted = 0")
public class EntPrefixConfigEntity extends BaseEntity {

    /**
     * IP地址
     */
    @Column(name = "ip")
    private String ip;

    /**
     * IP类型
     */
    @Column(name = "ip_type")
    private Integer ipType;

    /**
     * HTTP端口
     */
    @Column(name = "http_port")
    private Integer httpPort;

    /**
     * TCP端口
     */
    @Column(name = "tcp_port")
    private Integer tcpPort;

    /**
     * UDP端口
     */
    @Column(name = "udp_port")
    private Integer udpPort;

    /**
     * 企业前缀ID
     */
    @Column(name = "ent_prefix_id")
    private Long entPrefixId;

    /**
     * 企业ID
     */
    @Column(name = "ent_id")
    private Long entId;

}
