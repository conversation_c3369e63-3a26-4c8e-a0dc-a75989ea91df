package cn.teleinfo.idycprovince.infrastructure.db.entity;

import java.time.LocalDateTime;

import lombok.Getter;
import lombok.Setter;

import javax.persistence.*;

@Getter
@Setter
@Entity
@Table(name = "yc_province_buss")
public class ProvinceBussEntity extends BaseEntity{

    /**
    *  资金来源
    */
    @Column(name = "capital_source")
    private String capitalSource;

    /**
    *  投入人员数量
    */
    @Column(name = "input_people")
    private Integer inputPeople;

    /**
    *  所获奖项
    */
    @Column(name = "received_awards")
    private String receivedAwards;
}