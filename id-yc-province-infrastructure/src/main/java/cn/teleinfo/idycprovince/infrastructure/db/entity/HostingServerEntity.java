package cn.teleinfo.idycprovince.infrastructure.db.entity;

import lombok.Data;

import javax.persistence.Column;
import javax.persistence.Entity;
import javax.persistence.Table;
import java.io.Serializable;

@Entity
@Data
@Table(name = "yc_hosting_server")
public class HostingServerEntity extends BaseEntity implements Serializable {

    private static final long serialVersionUID = 1L;

    /** 服务器名称 */
    @Column(name = "srv_name")
    private String srvName ;
    /** 内网IP类型 */
    @Column(name = "ip_type")
    private Integer ipType ;
    /** 内网IP地址 */
    @Column(name = "ip")
    private String ip ;
    /** 内网tcp端口 */
    @Column(name = "tcp_port")
    private Integer tcpPort ;
    /** 内网udp端口 */
    @Column(name = "udp_port")
    private Integer udpPort ;
    /** 内网http端口 */
    @Column(name = "http_port")
    private Integer httpPort ;
    /** 外网IP类型 */
    @Column(name = "ip_type_reslove")
    private Integer ipTypeReslove ;
    /** 外网IP地址 */
    @Column(name = "ip_reslove")
    private String ipReslove ;
    /** 外网tcp端口 */
    @Column(name = "tcp_port_reslove")
    private Integer tcpPortReslove ;
    /** 外网udp端口 */
    @Column(name = "udp_port_reslove")
    private Integer udpPortReslove ;
    /** 外网http端口 */
    @Column(name = "http_port_reslove")
    private Integer httpPortReslove ;
    /** 服务管理员 */
    @Column(name = "auth_handle")
    private String authHandle ;
    /** 服务管理员公钥索引 */
    @Column(name = "auth_index")
    private Integer authIndex ;
    /** 服务管理员私钥 */
    @Column(name = "auth_private_key")
    private String authPrivateKey ;

}
