package cn.teleinfo.idycprovince.infrastructure.db.entity;

import lombok.Getter;
import lombok.Setter;
import org.hibernate.annotations.SQLDelete;
import org.hibernate.annotations.SQLDeleteAll;
import org.hibernate.annotations.Where;

import javax.persistence.Column;
import javax.persistence.Entity;
import javax.persistence.Table;

/**
 * <AUTHOR>
 */
@Getter
@Setter
@Entity
@Table(name = "yc_social_user")
@SQLDelete(sql = "update yc_social_user set is_deleted = null, updated_time = NOW()  where id = ?")
@SQLDeleteAll(sql = "update yc_social_user set is_deleted = null, updated_time = NOW()  where id = ?")
@Where(clause = "is_deleted = 0")
public class SocialUserEntity extends BaseEntity{

    /**
    *  第三方用户来源  1阿里 2华为
    */
    @Column(name = "source")
    private Integer source;

    /**
    *  第三方用户id
    */
    @Column(name = "uuid")
    private String uuid;

    /**
    *  第三方用户账号
    */
    @Column(name = "account")
    private String account;

    /**
    *  第三方用户密码
     */
    @Column(name = "password")
    private String password;

    /**
     * 第三方用户手机号
     */
    @Column(name = "phone")
    private String phone;

    /**
     * 第三方用户邮箱
     */
    @Column(name = "email")
    private String email;
    
    /**
     * 第三方用户组织机构代码
     */
    @Column(name = "org_code")
    private String orgCode;
    
    /**
     * 第三方用户组织/单位名称
     */
    @Column(name = "org_name")
    private String orgName;
    
    /**
     * 第三方用户任职姓名
     */
    @Column(name = "name")
    private String name;



}