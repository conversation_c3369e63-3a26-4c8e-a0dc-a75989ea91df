package cn.teleinfo.idycprovince.infrastructure.db.entity;


import lombok.Getter;
import lombok.Setter;
import org.hibernate.annotations.SQLDelete;
import org.hibernate.annotations.SQLDeleteAll;
import org.hibernate.annotations.Where;
import org.springframework.data.annotation.CreatedBy;
import org.springframework.data.annotation.CreatedDate;
import org.springframework.data.annotation.LastModifiedBy;
import org.springframework.data.annotation.LastModifiedDate;
import org.springframework.data.jpa.domain.support.AuditingEntityListener;

import javax.persistence.*;
import java.io.Serializable;
import java.time.LocalDateTime;

/**
 * <p>
 * 菜单表
 * </p>
 *
 * <AUTHOR>
 * @since 2022-11-05
 */

@Getter
@Setter
@Entity
@Table(name = "yc_province_tenant")
@SQLDelete(sql = "update yc_province_tenant set is_deleted = null, updated_time = NOW()  where id = ?")
@SQLDeleteAll(sql = "update yc_province_tenant set is_deleted = null , updated_time = NOW() where id = ?")
@Where(clause = "is_deleted = 0")
//@MappedSuperclass
@EntityListeners(AuditingEntityListener.class)
public class ProvinceTenantEntity implements Serializable {

    private static final long serialVersionUID = 1L;


    /**
     * 前缀
     */
    @Column(name = "prefix")
    private String prefix;
    /**
     * 节点名称
     */
    @Column(name = "node_name")
    private String nodeName;
    /**
     * 组织机构代码
     */
    @Column(name = "biz_code")
    private String bizCode;
    /**
     * 企业名称
     */
    @Column(name = "ent_name")
    private String entName;
    /**
     * 节点地址
     */
    @Column(name = "node_address")
    private String nodeAddress;
    /**
     * 上报地址
     */
    @Column(name = "report_url")
    private String reportUrl;
    /**
     * appid
     */
    @Column(name = "appid")
    private String appid;
    /**
     * 私钥
     */
    @Column(name = "private_key")
    private String privateKey;

    /**
     * 创建用户开关 0关闭 1开启
     */
    @Column(name = "enable_user_add")
    private Integer enableUserAdd;

    @Id
    @GeneratedValue(strategy = GenerationType.IDENTITY)
    private Long id;

    /**
     * 创建时间
     */
    @CreatedDate
    @Column(name = "created_time")
    private LocalDateTime createdTime;

    /**
     * 创建人
     */
    @CreatedBy
    @Column(name = "created_by")
    private Long createdBy;

    /**
     * 更新时间
     */
    @LastModifiedDate
    @Column(name = "updated_time")
    private LocalDateTime updatedTime;

    /**
     * 更新人
     */
    @LastModifiedBy
    @Column(name = "updated_by")
    private Long updatedBy;

    /**
     * 逻辑删除: 0 未删除 null 已删除
     */
    @Column(name = "is_deleted")
    private Integer isDeleted = 0;


}
