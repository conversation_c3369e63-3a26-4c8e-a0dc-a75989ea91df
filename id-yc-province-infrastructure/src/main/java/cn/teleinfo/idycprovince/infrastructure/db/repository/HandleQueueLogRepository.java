package cn.teleinfo.idycprovince.infrastructure.db.repository;

import cn.teleinfo.idycprovince.infrastructure.db.entity.HandleQueueLogEntity;
import cn.teleinfo.idycprovince.infrastructure.db.to.HandleQueueLogTO;
import org.springframework.data.domain.Page;
import org.springframework.data.domain.Pageable;
import org.springframework.data.jpa.repository.Query;
import org.springframework.data.repository.query.Param;

import java.time.LocalDateTime;
import java.util.List;

/***
 * @title HandleMaintainRepository
 * @description <TODO description class purpose>
 * <AUTHOR>
 * @version 1.0.0
 * @create 2023/3/13 15:48
 **/
public interface HandleQueueLogRepository extends BaseRepository<HandleQueueLogEntity,Long> {

    @Query(nativeQuery = true, value =
            " SELECT " +
            " handle AS handle, " +
            " created_time AS updatedTime, " +
            " updated_by AS updatedBy, " +
            " maintain_state AS maintainState, " +
            " info_update AS infoUpdate, " +
                    " maintain_id AS maintainId " +
            " FROM " +
            " yc_handle_queue_log " +
            " WHERE " +
            " is_deleted = 0 " +
                    " AND if(:handle != '' and :handle is not null, handle = :handle, 1=1) " +
                    " AND if(:maintainId != '' and :maintainId is not null, maintain_id = :maintainId, 1=1) " +
                    " AND (coalesce(:maintainIdList,null) is null or maintain_id in (:maintainIdList)) " +
            " AND if(:maintainState != '' and :maintainState is not null, maintain_state = :maintainState , 1=1) " +
                    " AND business_code = :businessCode " ,
            countQuery = " SELECT " +
                    " COUNT(*) " +
                    " FROM " +
                    " yc_handle_queue_log ql " +
                    " WHERE " +
                    " is_deleted = 0 " +
                    " AND if(:handle != '' and :handle is not null, handle = :handle, 1=1) " +
                    " AND if(:maintainId != '' and :maintainId is not null, maintain_id = :maintainId, 1=1) " +
                    " AND (coalesce(:maintainIdList,null) is null or maintain_id in (:maintainIdList)) " +
                    " AND if(:maintainState != '' and :maintainState is not null, maintain_state = :maintainState , 1=1) " +
                    " AND business_code = :businessCode ")
    Page<HandleQueueLogTO> recordPage(@Param("handle") String handle,
                                      @Param("maintainId") Long maintainId,
                                      @Param("maintainIdList") List<Long> maintainIdList,
                                      @Param("maintainState") String maintainState,
                                      @Param("pageable") Pageable pageable,
                                      @Param("businessCode") String businessCode);

    HandleQueueLogEntity findByHandle(String handle);
    void deleteAllByHandle(String handle);
    void deleteAllByMaintainId(Long maintainId);
    @Query(nativeQuery = true, value =
            "select a.* from yc_handle_queue_log as a left join yc_handle_maintain as b on b.id = a.maintain_id AND b.is_deleted = 0 " +
                    " where a.is_deleted = 0 and a.business_code = :businessCode and b.app_id = :appId and a.maintain_state != 0 and a.ack = 0 order by b.created_time desc limit 1")
    HandleQueueLogEntity selectUnasked(@Param("businessCode") String businessCode, @Param("appId") Long appId);

    HandleQueueLogEntity findByBusinessIdAndBusinessCode(String businessId, String businessCode);

    List<HandleQueueLogEntity> findByBusinessId(String businessId);

    /**
     * 获取超时未成功，标记为失败
     * @param businessCode
     * @param maintainState
     * @param time
     * @return
     */
    List<HandleQueueLogEntity> findAllByBusinessCodeAndMaintainStateAndUpdatedTimeLessThanEqual(String businessCode, int maintainState, LocalDateTime time);

    /**
     * 查询第80w条记录
     * @return
     */
    @Query(nativeQuery = true, value = "select id from yc_handle_queue_log order by id asc limit 800000, 1")
    Long find80WLimit1();

    void deleteByIdBefore(Long id);

}
