package cn.teleinfo.idycprovince.infrastructure.db.entity;

import lombok.Data;
import org.hibernate.annotations.SQLDelete;
import org.hibernate.annotations.SQLDeleteAll;
import org.hibernate.annotations.Where;

import javax.persistence.Column;
import javax.persistence.Entity;
import javax.persistence.Table;

/***
 * @title HandleUploadRecordEntity
 * @description <TODO description class purpose>
 * <AUTHOR>
 * @version 1.0.0
 * @create 2023/5/15 10:14
 **/
@Entity
@Data
@SQLDelete(sql = "update yc_handle_upload_record set is_deleted = null, updated_time = NOW()  where id = ?")
@SQLDeleteAll(sql = "update yc_handle_upload_record set is_deleted = null, updated_time = NOW()  where id = ?")
@Where(clause = "is_deleted = 0")
@Table(name = "yc_handle_upload_record")
public class HandleUploadRecordEntity extends BaseEntity {

    /**
     * 上传标识总数
     */
    @Column(name = "upload_handle_num")
    private Long uploadHandleNum;

    /**
     * 上传失败总数
     */
    @Column(name = "upload_fail_num")
    private Long uploadFailNum;

    /**
     * 上传状态(0:上传中,1:完成)
     */
    @Column(name = "upload_status")
    private Integer uploadStatus;

    /**
     * 文件地址
     */
    @Column(name = "file_address")
    private String fileAddress;

    /**
     * 文件名称
     */
    @Column(name = "file_name")
    private String fileName;

    /**
     * 企业ID
     */
    @Column(name = "ent_id")
    private Long entId;

    /**
     * 应用ID
     */
    @Column(name = "app_id")
    private Long appId;

    @Column(name = "export_state")
    private Integer exportState;

}
