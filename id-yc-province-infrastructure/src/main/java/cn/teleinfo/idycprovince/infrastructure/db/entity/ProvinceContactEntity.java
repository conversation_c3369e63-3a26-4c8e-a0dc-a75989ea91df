package cn.teleinfo.idycprovince.infrastructure.db.entity;

import java.time.LocalDateTime;

import lombok.Getter;
import lombok.Setter;
import org.hibernate.annotations.SQLDelete;
import org.hibernate.annotations.SQLDeleteAll;
import org.hibernate.annotations.Where;

import javax.persistence.*;

@Getter
@Setter
@Entity
@Table(name = "yc_province_contact")
@SQLDelete(sql = "update yc_province_contact set is_deleted = null, updated_time = NOW()  where id = ?")
@SQLDeleteAll(sql = "update yc_province_contact set is_deleted = null, updated_time = NOW()  where id = ?")
@Where(clause = "is_deleted = 0")
public class ProvinceContactEntity extends BaseEntity{

    /**
    *  职责类型
    */
    @Column(name = "duty_type")
    private String dutyType;

    /**
    *  联系人姓名
    */
    @Column(name = "contact_name")
    private String contactName;

    /**
    *  职位
    */
    @Column(name = "position")
    private String position;

    /**
    *  联系电话
    */
    @Column(name = "phone")
    private String phone;

    /**
    *  邮箱
    */
    @Column(name = "email")
    private String email;

}