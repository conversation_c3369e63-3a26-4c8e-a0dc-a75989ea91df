<?xml version="1.0" encoding="UTF-8"?>
<settings xmlns="http://maven.apache.org/SETTINGS/1.2.0"
          xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance"
          xsi:schemaLocation="http://maven.apache.org/SETTINGS/1.2.0 
          http://maven.apache.org/xsd/settings-1.2.0.xsd">
  
  <localRepository>/Users/<USER>/.m2/repository</localRepository>
  
  <!-- 不使用镜像，让Maven直接访问POM中配置的仓库 -->
  
  <profiles>
    <profile>
      <id>env-dev</id>
      <repositories>
        <repository>
          <id>nexus</id>
          <name>HFQ Nexus</name>
          <url>http://124.221.62.70:8081/repository/maven-public/</url>
          <releases>
            <enabled>true</enabled>
          </releases>
          <snapshots>
            <enabled>true</enabled>
            <updatePolicy>always</updatePolicy>
          </snapshots>
        </repository>
        <repository>
          <id>nexus-snapshots</id>
          <name>HFQ Nexus Snapshots</name>
          <url>http://124.221.62.70:8081/repository/maven-snapshots/</url>
          <releases>
            <enabled>false</enabled>
          </releases>
          <snapshots>
            <enabled>true</enabled>
            <updatePolicy>always</updatePolicy>
          </snapshots>
        </repository>
      </repositories>
    </profile>
  </profiles>
  
  <activeProfiles>
    <activeProfile>env-dev</activeProfile>
  </activeProfiles>
  
</settings>
